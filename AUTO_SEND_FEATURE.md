# 语音解答自动发送功能

## 功能概述

在语音解答模式下，新增了自动发送消息的功能，可以根据语音识别的轮次自动发送消息给AI助手，提高对话效率。

## 功能特性

### 1. 配置管理
- **语音设置页面配置**：在语音设置对话框中可以配置自动发送功能
- **开启/关闭开关**：可以启用或禁用自动发送功能
- **轮次设置**：可以设置每几轮识别结果自动发送（默认2轮）
- **识别次数设置**：可以设置每轮包含多少次完整识别结果（默认4次）

### 2. 实时控制
- **语音解答页面控制**：在语音解答界面有专门的自动发送控制按钮
- **随时开启/关闭**：可以在对话过程中随时开启或关闭自动发送
- **状态指示**：实时显示自动发送状态和当前识别进度

### 3. 兼容性保证
- **手动发送保留**：自动发送开启时，手动发送功能仍然正常工作
- **灵活控制**：用户可以在未达到设定轮次时主动发送消息

## 使用方法

### 配置自动发送

1. **打开语音设置**
   - 点击设置按钮，选择"语音识别 API 设置"

2. **启用自动发送**
   - 在"自动发送消息"部分，勾选启用开关
   - 设置自动发送轮次（1-5轮可选，默认2轮）
   - 设置每轮识别次数（3-6次可选，默认4次）

3. **保存设置**
   - 点击"保存设置"按钮保存配置

### 在语音解答中使用

1. **进入语音解答模式**
   - 按 Cmd/Ctrl+Shift+M 进入语音解答模式

2. **查看自动发送状态**
   - 如果已启用，会显示🚀按钮（绿色表示已开启）
   - 状态指示器会显示"自动发送已开启 (当前识别次数/总需要次数)"

3. **控制自动发送**
   - 点击🚀按钮可以随时开启/关闭自动发送
   - 开启时按钮为绿色🚀，关闭时为灰色⏸️

4. **自动发送触发**
   - 当语音识别达到设定的轮次时，会自动发送消息
   - 发送后会自动清空输入框并重置计数

## 工作原理

### 识别计数机制
- **完整识别**：每次语音识别完成（sentence_end=true）计为1次
- **轮次计算**：每N次完整识别计为1轮（N为配置的每轮识别次数）
- **自动发送**：达到设定轮次时自动发送

### 示例场景
假设配置为"2轮后自动发送，每轮4次识别"：
1. 用户说话，识别到"你好" → 计数1/8
2. 用户继续说话，识别到"我想问一个问题" → 计数2/8
3. 用户继续说话，识别到"关于React" → 计数3/8
4. 用户继续说话，识别到"的状态管理" → 计数4/8（完成第1轮）
5. 用户继续说话，识别到"应该怎么做" → 计数5/8
6. 用户继续说话，识别到"比较好" → 计数6/8
7. 用户继续说话，识别到"有什么建议" → 计数7/8
8. 用户继续说话，识别到"吗" → 计数8/8（完成第2轮，自动发送）

## 技术实现

### 配置存储
- 配置保存在 `voice_config.json` 文件中
- 包含 `autoSend` 对象：`enabled`、`roundsToSend`、`recognitionsPerRound`

### 状态管理
- 使用 React hooks 管理识别计数和轮次状态
- 实时跟踪语音识别结果，统计完整识别次数

### 自动发送逻辑
- 在语音识别结果处理中检查计数
- 达到条件时触发自动发送回调
- 发送后重置所有相关状态

## 注意事项

1. **识别准确性**：自动发送基于语音识别的完整结果，识别准确性会影响计数
2. **网络延迟**：在网络较慢时，可能会影响识别结果的及时性
3. **手动干预**：用户随时可以手动发送消息，不受自动发送限制
4. **状态重置**：每次手动发送或自动发送后，计数都会重置

## 故障排除

### 自动发送不工作
1. 检查是否在语音设置中启用了自动发送
2. 确认语音识别正常工作
3. 查看状态指示器中的计数是否正常增加

### 计数不准确
1. 确保语音识别结果是完整的句子
2. 检查网络连接是否稳定
3. 尝试重新开始语音识别

### 按钮不显示
1. 确认已在语音设置中配置了自动发送
2. 检查是否正确进入了语音解答模式
