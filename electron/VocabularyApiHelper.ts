// VocabularyApiHelper.ts
import https from 'https';

// 热词项接口（与阿里云API对应）
interface ApiHotWord {
  text: string;
  lang: string;
  target_lang?: string;
  translation?: string;
}

// API响应接口
interface ApiResponse<T = any> {
  output?: T;
  usage?: {
    count: number;
  };
  request_id?: string;
  error?: {
    code: string;
    message: string;
  };
  // 直接在响应中的错误字段（当API返回错误时）
  code?: string;
  message?: string;
}

// 创建热词表响应
interface CreateVocabularyResponse {
  vocabulary_id: string;
}

// 查询热词表响应
interface QueryVocabularyResponse {
  vocabulary_list: Array<{
    vocabulary_id: string;
    gmt_create: string;
    gmt_modified: string;
    status: string;
  }>;
}

// 热词表详情响应
interface VocabularyDetailResponse {
  vocabulary: ApiHotWord[];
  gmt_create: string;
  gmt_modified: string;
  target_model: string;
  status: string;
}

export class VocabularyApiHelper {
  private apiKey: string;
  private baseUrl = 'https://dashscope.aliyuncs.com/api/v1/services/audio/asr/customization';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * 创建热词表
   */
  async createVocabulary(prefix: string, vocabulary: ApiHotWord[]): Promise<{ success: boolean; vocabularyId?: string; error?: string }> {
    try {
      const requestData = {
        model: 'speech-biasing',
        input: {
          action: 'create_vocabulary',
          target_model: 'gummy-realtime-v1',
          prefix,
          vocabulary
        }
      };

      const response = await this.makeRequest<CreateVocabularyResponse>(requestData);

      // 检查是否有错误（包括 error 字段和 code 字段）
      if (response.error) {
        console.error('API返回错误:', response.error);
        return { success: false, error: response.error.message };
      }

      if (response.code && response.code !== 'Success') {
        console.error('API返回错误代码:', response.code, '错误信息:', response.message);
        return { success: false, error: response.message || `API错误: ${response.code}` };
      }

      const vocabularyId = response.output?.vocabulary_id;
      console.log('提取的热词表ID:', vocabularyId);

      if (!vocabularyId) {
        console.error('API响应中没有找到热词表ID');
        return { success: false, error: '创建成功但未返回热词表ID' };
      }

      return {
        success: true,
        vocabularyId
      };
    } catch (error) {
      console.error('创建热词表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建热词表失败'
      };
    }
  }

  /**
   * 查询所有热词表
   */
  async listVocabularies(prefix?: string): Promise<{ success: boolean; vocabularies?: any[]; error?: string }> {
    try {
      const requestData = {
        model: 'speech-biasing',
        input: {
          action: 'list_vocabulary',
          prefix,
          page_index: 0,
          page_size: 100
        }
      };

      const response = await this.makeRequest<QueryVocabularyResponse>(requestData);

      if (response.error) {
        return { success: false, error: response.error.message };
      }

      if (response.code && response.code !== 'Success') {
        return { success: false, error: response.message || `API错误: ${response.code}` };
      }

      return {
        success: true,
        vocabularies: response.output?.vocabulary_list || []
      };
    } catch (error) {
      console.error('查询热词表失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '查询热词表失败' 
      };
    }
  }

  /**
   * 查询指定热词表详情
   */
  async queryVocabulary(vocabularyId: string): Promise<{ success: boolean; vocabulary?: VocabularyDetailResponse; error?: string }> {
    try {
      const requestData = {
        model: 'speech-biasing',
        input: {
          action: 'query_vocabulary',
          vocabulary_id: vocabularyId
        }
      };

      const response = await this.makeRequest<VocabularyDetailResponse>(requestData);

      if (response.error) {
        return { success: false, error: response.error.message };
      }

      if (response.code && response.code !== 'Success') {
        return { success: false, error: response.message || `API错误: ${response.code}` };
      }

      return {
        success: true,
        vocabulary: response.output
      };
    } catch (error) {
      console.error('查询热词表详情失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '查询热词表详情失败' 
      };
    }
  }

  /**
   * 更新热词表
   */
  async updateVocabulary(vocabularyId: string, vocabulary: ApiHotWord[]): Promise<{ success: boolean; error?: string }> {
    try {
      const requestData = {
        model: 'speech-biasing',
        input: {
          action: 'update_vocabulary',
          vocabulary_id: vocabularyId,
          vocabulary
        }
      };

      const response = await this.makeRequest(requestData);

      if (response.error) {
        return { success: false, error: response.error.message };
      }

      if (response.code && response.code !== 'Success') {
        return { success: false, error: response.message || `API错误: ${response.code}` };
      }

      return { success: true };
    } catch (error) {
      console.error('更新热词表失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '更新热词表失败' 
      };
    }
  }

  /**
   * 删除热词表
   */
  async deleteVocabulary(vocabularyId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const requestData = {
        model: 'speech-biasing',
        input: {
          action: 'delete_vocabulary',
          vocabulary_id: vocabularyId
        }
      };

      const response = await this.makeRequest(requestData);

      if (response.error) {
        return { success: false, error: response.error.message };
      }

      if (response.code && response.code !== 'Success') {
        return { success: false, error: response.message || `API错误: ${response.code}` };
      }

      return { success: true };
    } catch (error) {
      console.error('删除热词表失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '删除热词表失败' 
      };
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest<T = any>(data: any): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(data);
      
      const options = {
        hostname: 'dashscope.aliyuncs.com',
        port: 443,
        path: '/api/v1/services/audio/asr/customization',
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';

        // console.log('HTTP响应状态码:', res.statusCode);
        // console.log('HTTP响应头:', res.headers);

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          // console.log('原始响应数据:', responseData);
          try {
            const parsedResponse = JSON.parse(responseData);
            // console.log('解析后的响应:', JSON.stringify(parsedResponse, null, 2));
            resolve(parsedResponse);
          } catch (error) {
            console.error('解析响应失败:', error);
            console.error('原始响应数据:', responseData);
            reject(new Error(`解析响应失败: ${error instanceof Error ? error.message : String(error)}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }
}
