// ConfigHelper.ts
import fs from "node:fs"
import path from "node:path"
import { app } from "electron"
import { EventEmitter } from "events"

interface Config {
  apiKey: string;
  apiProvider: "openrouter" | "zhipu" | "custom";
  baseUrl?: string;  // 自定义API的base URL
  extractionModel: string;
  solutionModel: string;
  debuggingModel: string;
  language: string;
  opacity: number;
  promptLanguage: "chinese" | "english";  // 添加提示词语言选项
  loggingEnabled: boolean;  // 添加日志开关字段
  theme: "dark" | "light";  // 添加主题选项
  openrouterReasoningEnabled: boolean;  // OpenRouter 模型推理思考开关
  zhipuThinkingEnabled: boolean;  // 智谱AI 模型推理思考开关
}

export class ConfigHelper extends EventEmitter {
  private configPath: string;
  private defaultConfig: Config = {
    apiKey: "",
    apiProvider: "openrouter",
    baseUrl: "",
    extractionModel: "qwen/qwen2.5-vl-32b-instruct:free",
    solutionModel: "z-ai/glm-4.5-air:free",
    debuggingModel: "mistralai/mistral-small-3.2-24b-instruct:free",
    language: "python",
    opacity: 1.0,
    promptLanguage: "chinese",  // 默认使用中文提示词
    loggingEnabled: false,  // 默认禁用日志
    theme: "dark",  // 默认使用深色主题
    openrouterReasoningEnabled: false,  // 默认不开启 OpenRouter 推理思考
    zhipuThinkingEnabled: false  // 默认不开启智谱AI推理思考
  };

  constructor() {
    super();
    // Use the app's user data directory to store the config
    try {
      if (process.env.NODE_ENV === "development"){
        //dev 开启
        app.setPath("userData", path.join(app.getPath('appData'), 'Electron'));
      }
      this.configPath = path.join(app.getPath('userData'), 'config.json');
      console.log('Config path:', this.configPath);
    } catch (err) {
      console.warn('Could not access user data path, using fallback');
      this.configPath = path.join(process.cwd(), 'config.json');
    }
    
    // Ensure the initial config file exists
    this.ensureConfigExists();
  }

  /**
   * Ensure config file exists
   */
  private ensureConfigExists(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig);
      }
    } catch (err) {
      console.error("Error ensuring config exists:", err);
    }
  }

  /**
   * Validate and sanitize model selection to ensure only allowed models are used
   * 现在允许自定义模型，所以只做基本验证
   */
  private sanitizeModelSelection(model: string, provider: "openrouter" | "zhipu" | "custom"): string {
    if (provider === "openrouter") {
      // 如果模型为空，返回默认模型
      if (!model || model.trim() === "") {
        return 'z-ai/glm-4.5-air:free';
      }
      // 允许任何非空的模型ID，包括自定义模型
      return model.trim();
    } else if (provider === "zhipu") {
      // 如果模型为空，返回智谱AI的默认模型
      if (!model || model.trim() === "") {
        return 'GLM-4.5-Flash';
      }
      // 允许任何非空的模型ID
      return model.trim();
    } else if (provider === "custom") {
      // 如果模型为空，返回自定义API的默认模型
      if (!model || model.trim() === "") {
        return 'gpt-3.5-turbo';
      }
      // 允许任何非空的模型ID
      return model.trim();
    }
    // Default fallback
    return model || 'z-ai/glm-4.5-air:free';
  }

  public loadConfig(): Config {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const config = JSON.parse(configData);
        
        // Ensure apiProvider is a valid value
        const validProviders = ["openrouter", "zhipu", "custom"];
        if (!validProviders.includes(config.apiProvider)) {
          config.apiProvider = "openrouter"; // Default to OpenRouter if invalid
        }
        
        // 基本的模型选择清理（允许自定义模型）
        if (config.extractionModel) {
          config.extractionModel = this.sanitizeModelSelection(config.extractionModel, config.apiProvider);
        }
        if (config.solutionModel) {
          config.solutionModel = this.sanitizeModelSelection(config.solutionModel, config.apiProvider);
        }
        if (config.debuggingModel) {
          config.debuggingModel = this.sanitizeModelSelection(config.debuggingModel, config.apiProvider);
        }
        
        return {
          ...this.defaultConfig,
          ...config
        };
      }
      
      // If no config exists, create a default one
      this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    } catch (err) {
      console.error("Error loading config:", err);
      return this.defaultConfig;
    }
  }

  /**
   * Save configuration to disk
   */
  public saveConfig(config: Config): void {
    try {
      // Ensure the directory exists
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      // Write the config file
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (err) {
      console.error("Error saving config:", err);
    }
  }

  /**
   * Update specific configuration values
   */
  public updateConfig(updates: Partial<Config>): Config {
    try {
      const currentConfig = this.loadConfig();
      let provider = updates.apiProvider || currentConfig.apiProvider;
      
      // Auto-detect provider based on API key format if a new key is provided
      if (updates.apiKey && !updates.apiProvider) {
        // Auto-detect based on API key format
        if (updates.apiKey.trim().startsWith('sk-or-')) {
          provider = "openrouter";
          console.log("Auto-detected OpenRouter API key format");
        } else if (updates.apiKey.trim().match(/^[a-f0-9]{32}\.[a-zA-Z0-9]{6}$/)) {
          // 智谱AI API key格式检测
          provider = "zhipu";
          console.log("Auto-detected Zhipu AI API key format");
        } else {
          // For other API key formats, default to custom
          provider = "custom";
          console.log("Auto-detected custom API provider");
        }

        // Update the provider in the updates object
        updates.apiProvider = provider;
      }
      
      const isFirstTimeConfig = !currentConfig.apiKey && updates.apiKey;
      const isChangingProvider = updates.apiProvider && updates.apiProvider !== currentConfig.apiProvider;
      
      // 如果是首次配置或更改了提供商，设置默认模型
      if (isFirstTimeConfig || isChangingProvider) {
        console.log(`Setting default models for ${provider} (firstTime: ${isFirstTimeConfig}, changingProvider: ${isChangingProvider})`);

        // 根据提供商设置默认模型
        if (provider === "openrouter") {
          if (!updates.extractionModel) updates.extractionModel = "qwen/qwen2.5-vl-32b-instruct:free";
          if (!updates.solutionModel) updates.solutionModel = "z-ai/glm-4.5-air:free";
          if (!updates.debuggingModel) updates.debuggingModel = "mistralai/mistral-small-3.2-24b-instruct:free";
        } else if (provider === "zhipu") {
          if (!updates.extractionModel) updates.extractionModel = "glm-4v-plus-0111";
          if (!updates.solutionModel) updates.solutionModel = "GLM-4.5-Flash";
          if (!updates.debuggingModel) updates.debuggingModel = "glm-4v-plus-0111";
        } else if (provider === "custom") {
          if (!updates.extractionModel) updates.extractionModel = "gpt-4-vision-preview";
          if (!updates.solutionModel) updates.solutionModel = "gpt-3.5-turbo";
          if (!updates.debuggingModel) updates.debuggingModel = "gpt-4-vision-preview";
        }
      }
      
      // 移除硬编码的模型验证，现在允许自定义模型
      // 模型验证将在前端通过API调用进行
      
      // Save the merged config
      const newConfig = { ...currentConfig, ...updates };
      
      // Sanitize the model selections based on the provider
      newConfig.extractionModel = this.sanitizeModelSelection(
        newConfig.extractionModel || "",
        newConfig.apiProvider
      );
      newConfig.solutionModel = this.sanitizeModelSelection(
        newConfig.solutionModel || "",
        newConfig.apiProvider
      );
      newConfig.debuggingModel = this.sanitizeModelSelection(
        newConfig.debuggingModel || "",
        newConfig.apiProvider
      );
      
      this.saveConfig(newConfig);
      
      // Only emit update event for changes other than opacity
      // This prevents re-initializing the AI client when only opacity changes
      if (updates.apiKey !== undefined || updates.apiProvider !== undefined ||
          updates.extractionModel !== undefined || updates.solutionModel !== undefined ||
          updates.debuggingModel !== undefined || updates.language !== undefined ||
          updates.openrouterReasoningEnabled !== undefined || updates.zhipuThinkingEnabled !== undefined) {
        this.emit('config-updated', newConfig);
      }
      
      return newConfig;
    } catch (error) {
      console.error('Error updating config:', error);
      return this.defaultConfig;
    }
  }

  /**
   * Check if the API key is configured
   */
  public hasApiKey(): boolean {
    const config = this.loadConfig();
    return !!config.apiKey && config.apiKey.trim().length > 0;
  }
  
  /**
   * Validate the API key format
   */
  public isValidApiKeyFormat(apiKey: string, provider?: "openrouter" | "zhipu" | "custom"): boolean {
    if (provider === "openrouter") {
      return /^sk-or-[a-zA-Z0-9]{32,}$/.test(apiKey.trim());
    } else if (provider === "zhipu") {
      // 智谱AI API key格式验证
      return /^[a-f0-9]{32}\.[a-zA-Z0-9]{6}$/.test(apiKey.trim());
    } else if (provider === "custom") {
      // For custom providers, accept any non-empty API key
      return apiKey.trim().length > 0;
    }

    // Auto-detect based on format
    if (apiKey.trim().startsWith('sk-or-')) {
      return /^sk-or-[a-zA-Z0-9]{32,}$/.test(apiKey.trim());
    } else if (apiKey.trim().match(/^[a-f0-9]{32}\.[a-zA-Z0-9]{6}$/)) {
      return true; // 智谱AI格式
    }

    // For other formats, assume custom provider
    return apiKey.trim().length > 0;
  }
  
  /**
   * Get the stored opacity value
   */
  public getOpacity(): number {
    const config = this.loadConfig();
    return config.opacity !== undefined ? config.opacity : 1.0;
  }

  /**
   * Set the window opacity value
   */
  public setOpacity(opacity: number): void {
    // Ensure opacity is between 0.1 and 1.0
    const validOpacity = Math.min(1.0, Math.max(0.1, opacity));
    this.updateConfig({ opacity: validOpacity });
  }  
  
  /**
   * Get the preferred programming language
   */
  public getLanguage(): string {
    const config = this.loadConfig();
    return config.language || "python";
  }

  /**
   * Set the preferred programming language
   */
  public setLanguage(language: string): void {
    this.updateConfig({ language });
  }

  /**
   * Get the current theme
   */
  public getTheme(): "dark" | "light" {
    const config = this.loadConfig();
    return config.theme || "dark";
  }

  /**
   * Set the theme
   */
  public setTheme(theme: "dark" | "light"): void {
    this.updateConfig({ theme });
  }

  /**
   * Get the OpenRouter reasoning enabled status
   */
  public getOpenrouterReasoningEnabled(): boolean {
    const config = this.loadConfig();
    return config.openrouterReasoningEnabled !== undefined ? config.openrouterReasoningEnabled : false;
  }

  /**
   * Set the OpenRouter reasoning enabled status
   */
  public setOpenrouterReasoningEnabled(enabled: boolean): void {
    this.updateConfig({ openrouterReasoningEnabled: enabled });
  }

  /**
   * Get the Zhipu thinking enabled status
   */
  public getZhipuThinkingEnabled(): boolean {
    const config = this.loadConfig();
    return config.zhipuThinkingEnabled !== undefined ? config.zhipuThinkingEnabled : false;
  }

  /**
   * Set the Zhipu thinking enabled status
   */
  public setZhipuThinkingEnabled(enabled: boolean): void {
    this.updateConfig({ zhipuThinkingEnabled: enabled });
  }
  
  /**
   * Test API key with the selected provider
   */
  public async testApiKey(apiKey: string, provider?: "openrouter"): Promise<{valid: boolean, error?: string}> {
    // Only OpenRouter is supported
    return this.testOpenRouterKey(apiKey);
  }
  


  /**
   * Test OpenRouter API key
   */
  private async testOpenRouterKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      // 验证 OpenRouter API 密钥格式
      if (apiKey && /^sk-or-[a-zA-Z0-9]{32,}$/.test(apiKey.trim())) {
        // 在实际应用中，应该尝试对 OpenRouter API 发出请求来验证密钥
        return { valid: true };
      }
      return { valid: false, error: '无效的 OpenRouter API 密钥格式。' };
    } catch (error: any) {
      console.error('OpenRouter API key test failed:', error);
      let errorMessage = '验证 OpenRouter API 密钥时出现未知错误';
      
      if (error.message) {
        errorMessage = `错误: ${error.message}`;
      }
      
      return { valid: false, error: errorMessage };
    }
  }
}

// Export a singleton instance
export const configHelper = new ConfigHelper();
