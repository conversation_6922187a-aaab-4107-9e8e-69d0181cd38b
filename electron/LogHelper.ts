// LogHelper.ts
import electronLog from 'electron-log';
import path from 'path';
import fs from 'fs';
import { app } from 'electron';
import {configHelper} from "./ConfigHelper";

/**
 * 日志助手类，用于将 API 请求和响应记录到文件中
 */
export class LogHelper {
  private logDir: string;
  private logInitialized: boolean = false;
  private appDataDir: string;
  private loggingEnabled: boolean = false; // 默认关闭日志

  constructor() {
    try {
      // 配置electron-log
      this.configureElectronLog();

      const config = configHelper.loadConfig()
      this.loggingEnabled = config.loggingEnabled;
      if (!this.loggingEnabled) {
        return; // 如果日志未启用，直接返回
      }

      // 启用详细日志
      // electronLog.transports.file.level = 'debug';
      // electronLog.transports.console.level = 'debug';
      electronLog.transports.file.level = 'error'; // 只记录错误级别
      electronLog.transports.console.level = false; // 禁用控制台输出
      
      // 获取日志目录
      this.appDataDir = app.getPath('userData');
      electronLog.info('Application data directory path:', this.appDataDir);

      // 尝试使用 appData/interview-artifact/logs 路径
      this.logDir = path.join(this.appDataDir, 'logs');
      electronLog.info('Try to use the log directory path:', this.logDir);

      this.ensureLogDirExists();

      // 记录一个启动日志文件，确认日志系统正常工作
      this.logAppStart();
      
      // 重定向控制台日志到electron-log
      this.redirectConsoleToLog();
    } catch (err) {
      electronLog.error('Log system initialization failed, error details:', err);

      // 尝试使用备用路径
      try {
        const appDataEnv = process.env.APPDATA || process.env.HOME || process.cwd();
        electronLog.info('Using the APPDATA path obtained from environment variables as an alternative:', appDataEnv);

        this.logDir = path.join(appDataEnv, 'interview-artifact', 'logs');
        electronLog.info('Attempt to use the alternate log directory path:', this.logDir);

        this.ensureLogDirExists();
        this.logAppStart();
      } catch (backupErr) {
        electronLog.error('The initialization of the backup logging system also failed, error details:', backupErr);
        // 最后尝试在当前目录创建日志
        this.logDir = path.join(process.cwd(), 'logs');
        electronLog.info('Finally try using the current working directory as the log path:', this.logDir);
        this.ensureLogDirExists();
      }
    }
  }

  /**
   * 配置electron-log
   */
  private configureElectronLog(): void {
    // 配置日志文件路径
    electronLog.transports.file.resolvePathFn = () => {
      let userDataPath;
      try {
        userDataPath = app.getPath('userData');
      } catch (error) {
        userDataPath = process.env.APPDATA || process.env.HOME || process.cwd();
      }
      
      const logDir = path.join(userDataPath, 'logs');
      
      // 确保日志目录存在
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      // 日志文件路径: error.log 和 renderer.log
      const processType = process.type === 'renderer' ? 'renderer' : 'error';
      return path.join(logDir, `${processType}.log`);
    };
    
    // 配置文件日志格式
    electronLog.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}';
    
    // 配置控制台日志格式
    electronLog.transports.console.format = '[{level}] {text}';
    
    // 设置日志文件大小和旋转
    electronLog.transports.file.maxSize = 10 * 1024 * 1024; // 10MB
  }

  /**
   * 重定向控制台日志到electron-log
   */
  public redirectConsoleToLog(): void {
    if (!this.loggingEnabled) return;
    
    // 保存原始console方法的引用
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug,
    };
    
    // 重定向console方法到electron-log
    console.log = (...args) => {
      originalConsole.log(...args); // 仍然输出到原始控制台
      electronLog.info(...args); // 也记录到日志文件
    };
    
    console.error = (...args) => {
      originalConsole.error(...args);
      electronLog.error(...args);
    };
    
    console.warn = (...args) => {
      originalConsole.warn(...args);
      electronLog.warn(...args);
    };
    
    console.info = (...args) => {
      originalConsole.info(...args);
      electronLog.info(...args);
    };
    
    console.debug = (...args) => {
      originalConsole.debug(...args);
      electronLog.debug(...args);
    };
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirExists(): void {
    try {
      if (!fs.existsSync(this.logDir)) {
        electronLog.info('Log directory does not exist, creating now:', this.logDir);
        fs.mkdirSync(this.logDir, { recursive: true });
        electronLog.info('Log directory created successfully');
      } else {
        electronLog.info('The log directory already exists');
      }
      this.logInitialized = true;
    } catch (err) {
      electronLog.error("Error creating log directory:", err);
      this.logInitialized = false;
    }
  }

  /**
   * 记录应用启动信息
   */
  private logAppStart(): void {
    try {
      if (!this.logInitialized) {
        electronLog.error('The log system is not initialized, unable to record application startup information');
        return;
      }

      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filename = `${timestamp}_app_start.json`;
      const filePath = path.join(this.logDir, filename);

      const logData = {
        timestamp: new Date().toISOString(),
        event: 'app_start',
        appDataPath: this.appDataDir,
        logDirPath: this.logDir,
        env: {
          NODE_ENV: process.env.NODE_ENV,
          APPDATA: process.env.APPDATA,
          platform: process.platform,
          arch: process.arch
        }
      };

      fs.writeFileSync(filePath, JSON.stringify(logData, null, 2), { encoding: 'utf8' });
      electronLog.info('The application start log has been created.:', filePath);
    } catch (err) {
      electronLog.error("Error occurred when recording application startup information:", err);
    }
  }

  /**
   * 记录 API 请求
   * @param provider API 提供商
   * @param operation 操作类型（提取问题、生成解决方案、调试分析）
   * @param model 使用的模型
   * @param requestData 请求数据
   */
  public logApiRequest(
      provider: string,
      operation: string,
      model: string,
      requestData: any
  ): string {
    if (!this.loggingEnabled) {
      return ''; // 如果日志未启用，直接返回
    }
    try {
      if (!this.logInitialized) {
        electronLog.error('The log system is not initialized, unable to record API requests');
        return '';
      }

      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filename = `${timestamp}_${provider}_${operation}_req.json`;
      const filePath = path.join(this.logDir, filename);

      const logData = {
        timestamp: new Date().toISOString(),
        provider,
        operation,
        model,
        requestData
      };

      fs.writeFileSync(filePath, JSON.stringify(logData, null, 2), { encoding: 'utf8' });
      electronLog.info(`API request log has been created.: ${filePath}`);
      return filename;
    } catch (err) {
      electronLog.error("Error occurred when recording API requests:", err);
      return '';
    }
  }

  /**
   * 记录 API 响应
   * @param requestLogFilename 对应请求的日志文件名
   * @param responseData 响应数据
   * @param parsedResult 解析结果
   */
  public logApiResponse(
      requestLogFilename: string,
      responseData: any,
      parsedResult?: any
  ): void {
    if (!this.loggingEnabled) {
      return; // 如果日志未启用，直接返回
    }
    try {
      if (!this.logInitialized) {
        electronLog.error('The logging system is not initialized, unable to record API responses');
        return;
      }

      if (!requestLogFilename) {
        electronLog.error('The request log file name is empty, unable to record API response');
        return;
      }

      // 将请求日志文件名中的 req 替换为 resp
      const responseFilename = requestLogFilename.replace('_req.json', '_resp.json');
      const filePath = path.join(this.logDir, responseFilename);

      const logData = {
        timestamp: new Date().toISOString(),
        responseData,
        parsedResult
      };

      fs.writeFileSync(filePath, JSON.stringify(logData, null, 2), { encoding: 'utf8' });
      electronLog.info(`API response log has been created: ${filePath}`);
    } catch (err) {
      electronLog.error("Recording an error in API response:", err);
    }
  }

  /**
   * 记录错误信息
   * @param provider API 提供商
   * @param operation 操作类型
   * @param error 错误对象或消息
   */
  public logError(
      provider: string,
      operation: string,
      error: any
  ): void {
    if (!this.loggingEnabled) {
      return; // 如果日志未启用，直接返回
    }
    try {
      if (!this.logInitialized) {
        electronLog.error('The log system is not initialized, unable to record error messages');
        return;
      }

      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filename = `${timestamp}_${provider}_${operation}_error.json`;
      const filePath = path.join(this.logDir, filename);

      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      const logData = {
        timestamp: new Date().toISOString(),
        provider,
        operation,
        errorMessage,
        errorStack,
        errorDetails: error
      };

      fs.writeFileSync(filePath, JSON.stringify(logData, null, 2), { encoding: 'utf8' });
      electronLog.info(`Error log has been created: ${filePath}`);
      
      // 同时使用electron-log记录错误
      electronLog.error(`[${provider}] ${operation} 错误:`, error);
    } catch (err) {
      electronLog.error("Error occurred when recording error information:", err);
    }
  }

  /**
   * 获取日志目录路径
   */
  public getLogDir(): string {
    return this.logDir;
  }

  /**
   * 手动创建测试日志，用于诊断问题
   */
  public createTestLog(): void {
    if (!this.loggingEnabled) {
      return; // 如果日志未启用，直接返回
    }
    try {
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const filename = `${timestamp}_test_log.json`;
      const filePath = path.join(this.logDir, filename);

      const logData = {
        timestamp: new Date().toISOString(),
        event: 'test_log',
        message: 'This is a test log entry'
      };

      fs.writeFileSync(filePath, JSON.stringify(logData, null, 2), { encoding: 'utf8' });
      electronLog.info(`Test log has been created: ${filePath}`);
    } catch (err) {
      electronLog.error("Error occurred when creating test log:", err);
    }
  }
  
  /**
   * 获取electron-log日志路径
   */
  public getElectronLogPath(): string {
    return electronLog.transports.file.getFile().path;
  }
  
  /**
   * 检查日志是否启用
   */
  public isLoggingEnabled(): boolean {
    return this.loggingEnabled;
  }
}

// 导出单例实例
export const logHelper = new LogHelper(); 