// CheatSheetHelper.ts
import fs from "node:fs"
import path from "node:path"
import { dialog } from "electron"
import { CheatSheetConfigHelper, CheatSheetDocument } from "./CheatSheetConfigHelper"

export class CheatSheetHelper {
  private configHelper: CheatSheetConfigHelper;

  constructor() {
    this.configHelper = new CheatSheetConfigHelper();
  }

  /**
   * 处理Markdown中的图片路径，将相对路径转换为绝对路径
   */
  private processMarkdownImages(content: string, markdownFilePath: string): string {
    const markdownDir = path.dirname(markdownFilePath);

    // 匹配Markdown图片语法: ![alt](path) 和 <img src="path">
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)|<img[^>]+src=["']([^"']+)["'][^>]*>/g;

    return content.replace(imageRegex, (match, alt, imgPath, imgSrc) => {
      const imagePath = imgPath || imgSrc;

      // 如果已经是绝对路径或网络路径，不处理
      if (path.isAbsolute(imagePath) || imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return match;
      }

      // 将相对路径转换为绝对路径
      const absolutePath = path.resolve(markdownDir, imagePath);

      // 检查文件是否存在
      if (fs.existsSync(absolutePath)) {
        // 使用Base64编码路径，避免特殊字符问题
        const encodedPath = Buffer.from(absolutePath).toString('base64');
        const imageMarker = `data:image/local;path,${encodedPath}`;

        // console.log(`图片路径转换: ${imagePath} -> ${absolutePath}`);

        if (imgPath) {
          // Markdown语法
          return `![${alt}](${imageMarker})`;
        } else {
          // HTML img标签
          return match.replace(imgSrc, imageMarker);
        }
      } else {
        console.warn(`图片文件不存在: ${absolutePath}`);
      }

      // 如果文件不存在，保持原样
      return match;
    });
  }

  /**
   * 读取本地文件内容
   */
  public async readLocalFile(filePath: string): Promise<{ content: string; size: number }> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error('文件不存在');
      }

      const stats = fs.statSync(filePath);
      const size = stats.size;

      // 检查文件大小
      if (!this.configHelper.validateFileSize(size)) {
        const maxSizeMB = this.configHelper.getMaxFileSize() / (1024 * 1024);
        throw new Error(`文件大小超过限制 (${maxSizeMB}MB)`);
      }

      const ext = path.extname(filePath).toLowerCase();
      
      if (ext === '.md' || ext === '.txt') {
        let content = fs.readFileSync(filePath, 'utf8');

        // 如果是Markdown文件，处理相对路径的图片
        if (ext === '.md') {
          content = this.processMarkdownImages(content, filePath);
        }

        return { content, size };
      } else if (ext === '.pdf') {
        // PDF文件返回特殊标识和文件路径
        return { content: `PDF_FILE:${filePath}`, size };
      } else {
        throw new Error('不支持的文件格式，仅支持 .md, .txt, .pdf 文件');
      }
    } catch (error: any) {
      console.error('读取文件失败:', error);
      throw error;
    }
  }

  /**
   * 选择文件对话框
   */
  public async selectFile(): Promise<string | null> {
    try {
      const result = await dialog.showOpenDialog({
        title: '选择小抄文档',
        filters: [
          { name: 'Markdown文件', extensions: ['md'] },
          { name: 'PDF文件', extensions: ['pdf'] },
          { name: '文本文件', extensions: ['txt'] },
          { name: '所有支持的文件', extensions: ['md', 'pdf', 'txt'] }
        ],
        properties: ['openFile']
      });

      if (result.canceled || result.filePaths.length === 0) {
        return null;
      }

      return result.filePaths[0];
    } catch (error) {
      console.error('选择文件失败:', error);
      throw error;
    }
  }

  /**
   * 选择目录对话框
   */
  public async selectDirectory(): Promise<string | null> {
    try {
      const result = await dialog.showOpenDialog({
        title: '选择小抄文档目录',
        properties: ['openDirectory']
      });

      if (result.canceled || result.filePaths.length === 0) {
        return null;
      }

      return result.filePaths[0];
    } catch (error) {
      console.error('选择目录失败:', error);
      throw error;
    }
  }

  /**
   * 扫描目录中的支持文件
   */
  public async scanDirectory(directoryPath: string): Promise<CheatSheetDocument[]> {
    try {
      if (!fs.existsSync(directoryPath)) {
        throw new Error('目录不存在');
      }

      const files = fs.readdirSync(directoryPath);
      const supportedExtensions = ['.md', '.pdf', '.txt'];
      const documents: CheatSheetDocument[] = [];

      for (const file of files) {
        const filePath = path.join(directoryPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile()) {
          const ext = path.extname(file).toLowerCase();
          
          if (supportedExtensions.includes(ext)) {
            // 检查文件大小
            if (this.configHelper.validateFileSize(stats.size)) {
              // 确定文件类型
              let type: 'markdown' | 'pdf' | 'text';
              if (ext === '.pdf') {
                type = 'pdf';
              } else if (ext === '.md') {
                type = 'markdown';
              } else {
                type = 'text';
              }

              const document: CheatSheetDocument = {
                id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                name: path.basename(file, ext),
                type: type,
                filePath: filePath,
                size: stats.size,
                createdAt: Date.now(),
                updatedAt: Date.now()
              };
              documents.push(document);
            }
          }
        }
      }

      return documents;
    } catch (error) {
      console.error('扫描目录失败:', error);
      throw error;
    }
  }

  /**
   * 创建临时Markdown文件并添加文档
   */
  public async createMarkdownDocument(name: string, content: string): Promise<CheatSheetDocument> {
    try {
      const size = Buffer.byteLength(content, 'utf8');

      if (!this.configHelper.validateFileSize(size)) {
        const maxSizeMB = this.configHelper.getMaxFileSize() / (1024 * 1024);
        throw new Error(`文档大小超过限制 (${maxSizeMB}MB)`);
      }

      // 创建临时文件
      const { app } = require('electron');
      const tempDir = path.join(app.getPath('userData'), 'cheatsheet_temp');

      // 确保临时目录存在
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // 生成唯一文件名
      const fileName = `${name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}_${Date.now()}.md`;
      const filePath = path.join(tempDir, fileName);

      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');

      return this.configHelper.addDocument({
        name,
        type: 'markdown',
        filePath,
        size
      });
    } catch (error) {
      console.error('创建Markdown文档失败:', error);
      throw error;
    }
  }

  /**
   * 添加本地文件文档
   */
  public async addLocalFileDocument(filePath: string): Promise<CheatSheetDocument> {
    try {
      // 检查文件是否存在和大小
      if (!fs.existsSync(filePath)) {
        throw new Error('文件不存在');
      }

      const stats = fs.statSync(filePath);
      const size = stats.size;

      // 检查文件大小
      if (!this.configHelper.validateFileSize(size)) {
        const maxSizeMB = this.configHelper.getMaxFileSize() / (1024 * 1024);
        throw new Error(`文件大小超过限制 (${maxSizeMB}MB)`);
      }

      const ext = path.extname(filePath).toLowerCase();
      const name = path.basename(filePath, ext);

      // 确定文件类型
      let type: 'markdown' | 'pdf' | 'text';
      if (ext === '.pdf') {
        type = 'pdf';
      } else if (ext === '.md') {
        type = 'markdown';
      } else {
        type = 'text';
      }

      return this.configHelper.addDocument({
        name,
        type,
        filePath,
        size
      });
    } catch (error) {
      console.error('添加本地文件失败:', error);
      throw error;
    }
  }

  /**
   * 获取文档内容
   */
  public async getDocumentContent(document: CheatSheetDocument): Promise<string> {
    try {
      if (!document.filePath) {
        throw new Error('文档缺少文件路径');
      }

      const { content } = await this.readLocalFile(document.filePath);
      return content;
    } catch (error) {
      console.error('获取文档内容失败:', error);
      throw error;
    }
  }

  /**
   * 搜索文档内容
   */
  public async searchInDocument(document: CheatSheetDocument, searchTerm: string): Promise<{
    matches: Array<{ line: number; content: string; index: number }>;
    totalMatches: number;
  }> {
    try {
      const content = await this.getDocumentContent(document);
      const lines = content.split('\n');
      const matches: Array<{ line: number; content: string; index: number }> = [];
      
      const searchRegex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      
      lines.forEach((line, index) => {
        const lineMatches = line.match(searchRegex);
        if (lineMatches) {
          matches.push({
            line: index + 1,
            content: line,
            index: line.toLowerCase().indexOf(searchTerm.toLowerCase())
          });
        }
      });

      return {
        matches,
        totalMatches: matches.length
      };
    } catch (error) {
      console.error('搜索文档失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有文档
   */
  public getDocuments(): CheatSheetDocument[] {
    return this.configHelper.getDocuments();
  }

  /**
   * 获取当前文档
   */
  public getCurrentDocument(): CheatSheetDocument | null {
    return this.configHelper.getCurrentDocument();
  }

  /**
   * 下一个文档
   */
  public nextDocument(): CheatSheetDocument | null {
    return this.configHelper.nextDocument();
  }

  /**
   * 上一个文档
   */
  public previousDocument(): CheatSheetDocument | null {
    return this.configHelper.previousDocument();
  }

  /**
   * 设置当前文档
   */
  public setCurrentDocument(index: number): void {
    this.configHelper.setCurrentDocumentIndex(index);
  }

  /**
   * 删除文档
   */
  public deleteDocument(id: string): boolean {
    return this.configHelper.deleteDocument(id);
  }

  /**
   * 更新文档
   */
  public updateDocument(id: string, updates: Partial<CheatSheetDocument>): boolean {
    return this.configHelper.updateDocument(id, updates);
  }

  /**
   * 添加本地目录
   */
  public addLocalDirectory(directory: string): void {
    this.configHelper.addLocalDirectory(directory);
  }

  /**
   * 删除本地目录
   */
  public removeLocalDirectory(directory: string): void {
    this.configHelper.removeLocalDirectory(directory);
  }

  /**
   * 获取本地目录列表
   */
  public getLocalDirectories(): string[] {
    return this.configHelper.getLocalDirectories();
  }

  /**
   * 格式化文件大小
   */
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Export a singleton instance
export const cheatSheetHelper = new CheatSheetHelper();
