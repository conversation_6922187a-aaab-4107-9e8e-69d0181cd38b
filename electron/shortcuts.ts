import { globalShortcut, app } from "electron"
import { IShortcutsHelperDeps } from "./main"
import { configHelper } from "./ConfigHelper"
import { state } from "./main"  // 导入state对象访问最新状态

export class ShortcutsHelper {
  private deps: IShortcutsHelperDeps

  constructor(deps: IShortcutsHelperDeps) {
    this.deps = deps
  }

  private adjustOpacity(delta: number): void {
    const mainWindow = this.deps.getMainWindow();
    if (!mainWindow) return;
    
    let currentOpacity = mainWindow.getOpacity();
    let newOpacity = Math.max(0.1, Math.min(1.0, currentOpacity + delta));
    console.log(`Adjusting opacity from ${currentOpacity} to ${newOpacity}`);
    
    mainWindow.setOpacity(newOpacity);
    
    // Save the opacity setting to config without re-initializing the client
    try {
      const config = configHelper.loadConfig();
      config.opacity = newOpacity;
      configHelper.saveConfig(config);
    } catch (error) {
      console.error('Error saving opacity to config:', error);
    }
    
    // If we're making the window visible, also make sure it's shown and interaction is enabled
    if (newOpacity > 0.1 && !this.deps.isVisible()) {
      this.deps.toggleMainWindow();
    }
  }

  public registerGlobalShortcuts(): void {
    globalShortcut.register("CommandOrControl+H", async () => {
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        console.log("Taking screenshot...")
        try {
          const screenshotPath = await this.deps.takeScreenshot()
          const preview = await this.deps.getImagePreview(screenshotPath)
          mainWindow.webContents.send("screenshot-taken", {
            path: screenshotPath,
            preview
          })
        } catch (error) {
          console.error("Error capturing screenshot:", error)
        }
      }
    })

    globalShortcut.register("CommandOrControl+Enter", async () => {
      // 检查当前是否处于语音对话模式
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        // 发送事件到渲染进程，让渲染进程决定如何处理Ctrl+Enter
        mainWindow.webContents.send("handle-ctrl-enter")
      } else {
        // 如果窗口不存在，使用原来的处理方式
        await this.deps.processingHelper?.processScreenshots()
      }
    })

    // 添加CommandOrControl+Shift+Enter快捷键，用于问题提取模型直接解答
    globalShortcut.register("CommandOrControl+Shift+Enter", async () => {
      console.log("Command/Ctrl + Shift + Enter pressed. Direct answer mode.");
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        // 检查ultra版本权限
        try {
          if (state.activationHelper) {
            const isActivated = await state.activationHelper.isActivated();
            const isUltra = await state.activationHelper.isUltraVersion();

            if (!isActivated) {
              console.log("应用未激活，无法使用直接解答功能");
              mainWindow.webContents.send('activation-required');
              return;
            }

            if (!isUltra) {
              console.log("需要Ultra版本权限才能使用直接解答功能");
              mainWindow.webContents.send("direct-answer-permission-required");
              return;
            }
          }

          // 发送事件到渲染进程，让渲染进程决定如何处理Ctrl+Shift+Enter
          mainWindow.webContents.send("handle-ctrl-shift-enter")
        } catch (err) {
          console.error("直接解答权限检查失败:", err);
        }
      }
    })

    globalShortcut.register("CommandOrControl+R", () => {
      console.log("Command + R pressed. 检测当前模式...")
      
      // 直接从state获取实时的语音聊天模式状态
      const isInVoiceChatMode = state.isVoiceChatMode;
      console.log(`当前语音聊天模式状态: ${isInVoiceChatMode ? "已启用" : "未启用"}`);
      
      const mainWindow = this.deps.getMainWindow()
      if (!mainWindow || mainWindow.isDestroyed()) {
        console.log("主窗口不可用，无法执行重置操作");
        return;
      }
      
      // 检查是否在语音聊天模式
      if (isInVoiceChatMode) {
        console.log("在语音聊天模式下重置对话历史...");
        // 发送重置语音聊天的事件到渲染进程
        mainWindow.webContents.send("reset-voice-chat");
        console.log("已发送reset-voice-chat事件");
      } else {
        // 原有的重置逻辑
        console.log("在非语音聊天模式下执行标准重置...");
        // Cancel ongoing API requests
        this.deps.processingHelper?.cancelOngoingRequests();
        console.log("已取消正在进行的API请求");

        // Clear both screenshot queues
        this.deps.clearQueues();
        console.log("已清除截图队列");

        // Update the view state to 'queue'
        this.deps.setView("queue");
        console.log("已重置视图状态为queue");

        // Notify renderer process to switch view to 'queue'
        mainWindow.webContents.send("reset-view");
        mainWindow.webContents.send("reset");
        console.log("已发送reset-view和reset事件");
      }
    })

    // New shortcuts for moving the window
    globalShortcut.register("CommandOrControl+Left", () => {
      console.log("Command/Ctrl + Left pressed. Moving window left.")
      this.deps.moveWindowLeft()
    })

    globalShortcut.register("CommandOrControl+Right", () => {
      console.log("Command/Ctrl + Right pressed. Moving window right.")
      this.deps.moveWindowRight()
    })

    globalShortcut.register("CommandOrControl+Down", () => {
      console.log("Command/Ctrl + down pressed. Moving window down.")
      this.deps.moveWindowDown()
    })

    globalShortcut.register("CommandOrControl+Up", () => {
      console.log("Command/Ctrl + Up pressed. Moving window Up.")
      this.deps.moveWindowUp()
    })

    globalShortcut.register("CommandOrControl+B", () => {
      console.log("Command/Ctrl + B pressed. Toggling window visibility.")
      this.deps.toggleMainWindow()
    })

    globalShortcut.register("CommandOrControl+N", () => {
      console.log("Command/Ctrl + N pressed. Toggling mouse click through.")
      this.deps.toggleMouseClickThrough()
    })

    globalShortcut.register("CommandOrControl+Q", () => {
      console.log("Command/Ctrl + Q pressed. Quitting application.")

      // Clean up any pending operations before quitting
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        // Send cleanup signal to renderer process
        mainWindow.webContents.send("app-will-quit")
      }

      // Unregister all shortcuts before quitting
      globalShortcut.unregisterAll()

      app.quit()
    })

    // 添加Ctrl+T/Cmd+T快捷键用于折叠/展开思考分析
    globalShortcut.register("CommandOrControl+T", () => {
      console.log("Command/Ctrl + T pressed. Toggling thoughts visibility.");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.send("toggle-thoughts-visibility");
      }
    })

    // Adjust opacity shortcuts
    globalShortcut.register("CommandOrControl+[", () => {
      console.log("Command/Ctrl + [ pressed. Decreasing opacity.")
      this.adjustOpacity(-0.1)
    })

    globalShortcut.register("CommandOrControl+]", () => {
      console.log("Command/Ctrl + ] pressed. Increasing opacity.")
      this.adjustOpacity(0.1)
    })

    // 小抄模式快捷键
    globalShortcut.register("CommandOrControl+Alt+C", () => {
      console.log("Command/Ctrl + Alt + C pressed. Toggling cheat sheet mode.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        // 获取当前视图状态
        const currentView = this.deps.getView()
        console.log("Current view:", currentView)

        if (currentView === "cheatsheet") {
          // 如果当前在小抄模式，切换回队列页面
          this.deps.setView("queue")
          console.log("Switching from cheatsheet to queue")
        } else {
          // 否则切换到小抄模式
          this.deps.setView("cheatsheet")
          console.log("Switching to cheatsheet mode")
        }

        // 发送事件到渲染进程
        mainWindow.webContents.send("open-cheatsheet-mode")
      }
    })

    // 小抄翻页快捷键
    globalShortcut.register("CommandOrControl+Alt+Left", () => {
      console.log("Command/Ctrl + Alt + Left pressed. Previous cheat sheet.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        mainWindow.webContents.send("cheatsheet-previous")
      }
    })

    globalShortcut.register("CommandOrControl+Alt+Right", () => {
      console.log("Command/Ctrl + Alt + Right pressed. Next cheat sheet.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        mainWindow.webContents.send("cheatsheet-next")
      }
    })
    
    // Zoom controls
    globalShortcut.register("CommandOrControl+-", () => {
      console.log("Command/Ctrl + - pressed. Zooming out.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        const currentZoom = mainWindow.webContents.getZoomLevel()
        mainWindow.webContents.setZoomLevel(currentZoom - 0.5)
      }
    })
    
    globalShortcut.register("CommandOrControl+0", () => {
      console.log("Command/Ctrl + 0 pressed. Resetting zoom.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        mainWindow.webContents.setZoomLevel(0)
      }
    })
    
    globalShortcut.register("CommandOrControl+=", () => {
      console.log("Command/Ctrl + = pressed. Zooming in.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        const currentZoom = mainWindow.webContents.getZoomLevel()
        mainWindow.webContents.setZoomLevel(currentZoom + 0.5)
      }
    })
    
    // Delete last screenshot shortcut
    globalShortcut.register("CommandOrControl+L", () => {
      console.log("Command/Ctrl + L pressed. Deleting last screenshot.")
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        // Send an event to the renderer to delete the last screenshot
        mainWindow.webContents.send("delete-last-screenshot")
      }
    })
    
    // 添加语音模式快捷键 Ctrl+Shift+M
    globalShortcut.register("CommandOrControl+Shift+M", async () => {
      console.log("Command/Ctrl + Shift + M pressed. Toggling voice chat mode.");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        // 检查ultra版本权限
        try {
          if (state.activationHelper) {
            const isActivated = await state.activationHelper.isActivated();
            const isUltra = await state.activationHelper.isUltraVersion();

            if (!isActivated) {
              console.log("应用未激活，无法使用语音聊天模式");
              mainWindow.webContents.send('activation-required');
              return;
            }

            if (!isUltra) {
              console.log("需要Ultra版本权限才能使用语音聊天模式");
              // 可以发送一个特定的事件来显示权限不足的提示
              mainWindow.webContents.send("voice-permission-required");
              return;
            }
          }

          // 发送指令到ipcMain处理程序
          mainWindow.webContents.send("execute-toggle-voice-chat-mode");
          console.log("语音聊天模式切换指令已发送");
        } catch (err) {
          console.error("语音聊天模式切换指令发送失败:", err);
        }
      }
    })

    // 添加语音录制控制快捷键 Ctrl+M
    globalShortcut.register("CommandOrControl+M", async () => {
      console.log("Command/Ctrl + M pressed. Toggle voice recording.");
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        // 检查ultra版本权限
        try {
          if (state.activationHelper) {
            const isActivated = await state.activationHelper.isActivated();
            const isUltra = await state.activationHelper.isUltraVersion();

            if (!isActivated) {
              console.log("应用未激活，无法使用语音录制功能");
              mainWindow.webContents.send('activation-required');
              return;
            }

            if (!isUltra) {
              console.log("需要Ultra版本权限才能使用语音录制功能");
              mainWindow.webContents.send("voice-recording-permission-required");
              return;
            }
          }

          mainWindow.webContents.send("toggle-voice-recording");
        } catch (err) {
          console.error("语音录制权限检查失败:", err);
        }
      }
    })
    
    // Unregister shortcuts when quitting
    app.on("will-quit", () => {
      globalShortcut.unregisterAll()
    })
  }
}
