// VoiceConfigHelper.ts
import fs from "node:fs"
import path from "node:path"
import { app } from "electron"
import { EventEmitter } from "events"

export type VoiceProvider = 'tongyi-gummy';
export type AudioListeningMode = 'microphone-only' | 'system-only' | 'dual-audio';
export type InputLayoutMode = 'bottom' | 'left'; // 输入框布局模式：底部或左侧

interface TongyiGummyCredentials {
  apiKey: string;
}

type ProviderCredentials = TongyiGummyCredentials;

// 热词项接口
interface HotWord {
  text: string; // 热词文本
  lang: string; // 语言代码，支持 'zh' 和 'en'
}

// 热词表接口
interface VocabularyTable {
  id: string; // 本地生成的唯一ID
  name: string; // 热词表名称
  vocabularyId?: string; // 阿里云返回的热词表ID
  prefix: string; // 热词表前缀
  isActive: boolean; // 是否启用
  hotWords: HotWord[]; // 热词列表
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
}

interface VoiceConfig {
  selectedProvider: VoiceProvider;
  language: string; // 语音识别源语言，支持 'zh' (中文) 和 'en' (英文)
  audioListeningMode: AudioListeningMode; // 音频监听模式
  inputLayoutMode: InputLayoutMode; // 输入框布局模式
  tongyiGummy: TongyiGummyCredentials;
  vocabularyTables: VocabularyTable[]; // 热词表列表
  activeVocabularyId?: string; // 当前激活的热词表ID
  autoSend: {
    enabled: boolean; // 是否开启自动发送
    roundsToSend: number; // 每几轮识别结果自动发送（默认2轮）
    recognitionsPerRound: number; // 每轮包含多少次完整识别结果（默认4次）
  };
}

export class VoiceConfigHelper extends EventEmitter {
  private configPath: string;
  private defaultConfig: VoiceConfig = {
    selectedProvider: 'tongyi-gummy', // 默认使用通义Gummy语音识别
    language: 'zh', // 默认使用中文
    audioListeningMode: 'microphone-only', // 默认仅麦克风模式
    inputLayoutMode: 'bottom', // 默认输入框在底部
    tongyiGummy: {
      apiKey: ""
    },
    vocabularyTables: [], // 默认空的热词表列表
    activeVocabularyId: undefined, // 默认没有激活的热词表
    autoSend: {
      enabled: false, // 默认关闭自动发送
      roundsToSend: 2, // 默认2轮自动发送
      recognitionsPerRound: 4 // 默认每轮4次完整识别结果
    }
  };

  constructor() {
    super();
    // Use the app's user data directory to store the voice config
    try {
      this.configPath = path.join(app.getPath('userData'), 'voice_config.json');
      console.log('Voice config path:', this.configPath);
    } catch (err) {
      console.warn('Could not access user data path, using fallback');
      this.configPath = path.join(process.cwd(), 'voice_config.json');
    }
    
    // Ensure the initial config file exists
    this.ensureConfigExists();
  }

  /**
   * Ensure config file exists
   */
  private ensureConfigExists(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig);
      }
    } catch (err) {
      console.error("Error ensuring voice config exists:", err);
    }
  }

  /**
   * Load voice configuration from disk
   */
  public loadConfig(): VoiceConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const config = JSON.parse(configData);
        
        return {
          ...this.defaultConfig,
          ...config
        };
      }
      
      // If no config exists, create a default one
      this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    } catch (err) {
      console.error("Error loading voice config:", err);
      return this.defaultConfig;
    }
  }

  /**
   * Save voice configuration to disk
   */
  public saveConfig(config: VoiceConfig): void {
    try {
      // Ensure the directory exists
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      // Write the config file
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (err) {
      console.error("Error saving voice config:", err);
    }
  }

  /**
   * Update specific voice configuration values
   */
  public updateConfig(updates: Partial<VoiceConfig>): VoiceConfig {
    try {
      const currentConfig = this.loadConfig();

      // Merge the updates with current config
      const newConfig = { ...currentConfig, ...updates };

      // If updating nested objects, merge them properly
      if (updates.tongyiGummy) {
        newConfig.tongyiGummy = { ...currentConfig.tongyiGummy, ...updates.tongyiGummy };
      }

      // 特别处理热词表数组：如果 updates 中没有明确包含 vocabularyTables，保留现有的热词表
      if (!updates.hasOwnProperty('vocabularyTables')) {
        newConfig.vocabularyTables = currentConfig.vocabularyTables;
      }

      // 特别处理自动发送配置：如果 updates 中包含 autoSend，进行深度合并
      if (updates.autoSend) {
        newConfig.autoSend = { ...currentConfig.autoSend, ...updates.autoSend };
      }

      this.saveConfig(newConfig);

      // Emit update event
      this.emit('voice-config-updated', newConfig);

      return newConfig;
    } catch (error) {
      console.error('Error updating voice config:', error);
      return this.defaultConfig;
    }
  }

  /**
   * Get configuration for a specific provider
   */
  public getProviderConfig(provider: VoiceProvider): ProviderCredentials | null {
    const config = this.loadConfig();
    switch (provider) {
      case 'tongyi-gummy':
        return config.tongyiGummy;
      default:
        return null;
    }
  }

  /**
   * Check if a provider is properly configured
   */
  public isProviderConfigured(provider: VoiceProvider): boolean {
    const config = this.getProviderConfig(provider);
    if (!config) return false;

    switch (provider) {
      case 'tongyi-gummy': {
        const tongyiConfig = config as TongyiGummyCredentials;
        return !!(tongyiConfig.apiKey);
      }
      default:
        return false;
    }
  }

  /**
   * Get the currently selected provider
   */
  public getSelectedProvider(): VoiceProvider {
    const config = this.loadConfig();
    return config.selectedProvider;
  }

  /**
   * Set the selected provider
   */
  public setSelectedProvider(provider: VoiceProvider): void {
    this.updateConfig({ selectedProvider: provider });
  }

  /**
   * Get the currently selected audio listening mode
   */
  public getAudioListeningMode(): AudioListeningMode {
    const config = this.loadConfig();
    return config.audioListeningMode;
  }

  /**
   * Set the audio listening mode
   */
  public setAudioListeningMode(mode: AudioListeningMode): void {
    this.updateConfig({ audioListeningMode: mode });
  }

  /**
   * Validate API credentials format
   */
  public validateCredentials(provider: VoiceProvider, credentials: ProviderCredentials): { valid: boolean; error?: string } {
    switch (provider) {
      case 'tongyi-gummy': {
        const tongyiCreds = credentials as TongyiGummyCredentials;
        if (!tongyiCreds.apiKey) {
          return { valid: false, error: '请填写API Key' };
        }
        if (tongyiCreds.apiKey.length < 16) {
          return { valid: false, error: 'API Key格式不正确' };
        }
        return { valid: true };
      }
      default:
        return { valid: false, error: '不支持的语音识别提供商' };
    }
  }

  /**
   * Test API credentials (placeholder for future implementation)
   */
  public async testCredentials(provider: VoiceProvider, credentials: ProviderCredentials): Promise<{ valid: boolean; error?: string }> {
    // For now, just validate the format
    // In the future, this could make actual API calls to test the credentials
    return this.validateCredentials(provider, credentials);
  }

  // 热词表管理方法

  /**
   * 创建热词表
   */
  public createVocabularyTable(name: string, hotWords: HotWord[]): VocabularyTable {
    const config = this.loadConfig();
    const newTable: VocabularyTable = {
      id: this.generateUniqueId(),
      name,
      prefix: this.generatePrefix(),
      isActive: false,
      hotWords,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    config.vocabularyTables.push(newTable);
    this.saveConfig(config);
    this.emit('voice-config-updated', config);

    return newTable;
  }

  /**
   * 更新热词表
   */
  public updateVocabularyTable(id: string, updates: Partial<Omit<VocabularyTable, 'id' | 'createdAt'>>): VocabularyTable | null {
    const config = this.loadConfig();
    const tableIndex = config.vocabularyTables.findIndex(table => table.id === id);

    if (tableIndex === -1) {
      return null;
    }

    config.vocabularyTables[tableIndex] = {
      ...config.vocabularyTables[tableIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveConfig(config);
    this.emit('voice-config-updated', config);

    return config.vocabularyTables[tableIndex];
  }

  /**
   * 删除热词表
   */
  public deleteVocabularyTable(id: string): boolean {
    const config = this.loadConfig();
    const initialLength = config.vocabularyTables.length;

    config.vocabularyTables = config.vocabularyTables.filter(table => table.id !== id);

    // 如果删除的是当前激活的热词表，清除激活状态
    if (config.activeVocabularyId === id) {
      config.activeVocabularyId = undefined;
    }

    if (config.vocabularyTables.length < initialLength) {
      this.saveConfig(config);
      this.emit('voice-config-updated', config);
      return true;
    }

    return false;
  }

  /**
   * 设置激活的热词表
   */
  public setActiveVocabularyTable(id: string | undefined): boolean {
    const config = this.loadConfig();

    if (id && !config.vocabularyTables.find(table => table.id === id)) {
      return false; // 热词表不存在
    }

    // 取消所有热词表的激活状态
    config.vocabularyTables.forEach(table => {
      table.isActive = false;
    });

    // 设置新的激活热词表
    if (id) {
      const activeTable = config.vocabularyTables.find(table => table.id === id);
      if (activeTable) {
        activeTable.isActive = true;
      }
    }

    config.activeVocabularyId = id;
    this.saveConfig(config);
    this.emit('voice-config-updated', config);

    return true;
  }

  /**
   * 获取激活的热词表
   */
  public getActiveVocabularyTable(): VocabularyTable | null {
    const config = this.loadConfig();
    return config.vocabularyTables.find(table => table.isActive) || null;
  }

  /**
   * 获取所有热词表
   */
  public getVocabularyTables(): VocabularyTable[] {
    const config = this.loadConfig();
    return config.vocabularyTables;
  }

  /**
   * 生成唯一ID
   */
  private generateUniqueId(): string {
    return `vocab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成热词表前缀
   */
  private generatePrefix(): string {
    return "person"; // 统一使用固定前缀，符合10字符限制
  }
}

// Export a singleton instance
export const voiceConfigHelper = new VoiceConfigHelper();
