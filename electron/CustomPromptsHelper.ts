// CustomPromptsHelper.ts
import fs from "node:fs"
import path from "node:path"
import { app } from "electron"

// 自定义提示词配置接口
export interface CustomPromptsConfig {
  // 直接解答提示词
  directAnswer: {
    extraction: {
      userPrompt: string;
    };
    debugging: {
      userPrompt: string;
    };
  };
  // 语音模式流式解答提示词
  voiceStreaming: {
    solution: {
      systemPrompt: string;
    };
  };
}

export class CustomPromptsHelper {
  private configPath: string;

  // 默认中文提示词配置
  private defaultChineseConfig: CustomPromptsConfig = {
    directAnswer: {
      extraction: {
        userPrompt: "你是一个专业的笔试问题解答助手。请仔细分析提供的截图，识别其中的编程问题或技术问题，并直接提供详细的解决方案。\n\n请按照以下格式回复：\n\n## 问题分析\n[从截图中识别出的问题类型和核心要求]\n\n## 解决方案\n[提供详细的解决方案，包括代码实现]\n\n## 关键要点\n[列出解决问题的关键思路和注意事项]\n\n请优先使用图片中要求使用的框架或编程语言来提供解决方案，没有要求的话，用户擅长的主语言是${language}（如果涉及编程）。\n请确保答案详细、准确且易于理解。请用中文回复。"
      },
      debugging: {
        userPrompt: "你是一个专业的编程调试专家。请仔细分析提供的所有截图，这些截图可能包含：\n1. 原始问题或代码\n2. 运行结果或错误信息\n3. 调试过程中的其他相关信息\n\n请基于这些截图进行全面的调试分析。\n\n请按照以下格式提供调试分析：\n\n## 问题识别\n[从截图中识别出的问题或错误]\n\n## 根因分析\n[分析问题的根本原因]\n\n## 解决方案\n[提供具体的解决方案和修复代码]\n\n## 验证建议\n[建议如何验证修复是否有效]\n\n请优先使用图片中要求使用的框架或编程语言来提供解决方案，没有要求的话，用户擅长的主语言是${language}（如果涉及编程）。\n请提供准确、详细的调试建议，帮助解决截图中显示的问题。请用中文回复。"
      }
    },
    voiceStreaming: {
      solution: {
        systemPrompt: "你是一个专业的程序员面试题目解答助手。请分析用户与面试官的对话内容，清晰准确地找出用户所遇到的核心问题，帮助用户回答面试遇到的问题，顺利的通过面试。请用中文回复。"
      }
    }
  };

  // 默认英文提示词配置
  private defaultEnglishConfig: CustomPromptsConfig = {
    directAnswer: {
      extraction: {
        userPrompt: "You are a professional technical interview assistant. Please carefully analyze the provided screenshots, identify the programming or technical problems within, and provide detailed solutions directly.\n\nPlease respond in the following format:\n\n## Problem Analysis\n[Identify the problem type and core requirements from the screenshots]\n\n## Solution\n[Provide detailed solution including code implementation]\n\n## Key Points\n[List key insights and important considerations for solving the problem]\n\nPlease prioritize using the framework or programming language required in the image. If not specified, the user's preferred language is ${language} (if programming is involved).\nPlease ensure the answer is detailed, accurate, and easy to understand. Please respond in English."
      },
      debugging: {
        userPrompt: "You are a professional programming debugging expert. Please carefully analyze all provided screenshots, which may contain:\n1. Original problem or code\n2. Runtime results or error messages\n3. Other relevant information from the debugging process\n\nPlease provide comprehensive debugging analysis based on these screenshots.\n\nPlease respond in the following format:\n\n## Problem Identification\n[Identify the problem or error from the screenshots]\n\n## Root Cause Analysis\n[Analyze the fundamental cause of the problem]\n\n## Solution\n[Provide specific solution and fix code]\n\n## Verification Suggestions\n[Suggest how to verify if the fix is effective]\n\nPlease prioritize using the framework or programming language required in the image. If not specified, the user's preferred language is ${language} (if programming is involved).\nPlease provide accurate, detailed debugging advice to help solve the problems shown in the screenshots. Please respond in English."
      }
    },
    voiceStreaming: {
      solution: {
        systemPrompt: "You are a professional programmer interview question solving assistant. Please analyze the content of the conversation between the user and the interviewer, clearly and accurately identify the core problem the user is encountering, help the user answer the interview questions, and smoothly pass the interview. Please respond in English."
      }
    }
  };

  constructor() {
    const userDataPath = app.getPath('userData');
    this.configPath = path.join(userDataPath, 'custom_prompts_config.json');
  }

  /**
   * 加载自定义提示词配置
   */
  public loadConfig(): CustomPromptsConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf-8');
        const config = JSON.parse(configData);
        
        // 合并默认配置，确保所有字段都存在
        return this.mergeWithDefaults(config);
      }
    } catch (error) {
      console.error('加载自定义提示词配置失败:', error);
    }

    // 返回默认中文配置
    return this.defaultChineseConfig;
  }

  /**
   * 保存自定义提示词配置
   */
  public saveConfig(config: CustomPromptsConfig): void {
    try {
      // 确保目录存在
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      
      // 写入配置文件
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error('保存自定义提示词配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取默认中文配置（用于界面显示）
   */
  public getDefaultChineseConfig(): CustomPromptsConfig {
    return JSON.parse(JSON.stringify(this.defaultChineseConfig));
  }

  /**
   * 获取默认英文配置（用于界面显示）
   */
  public getDefaultEnglishConfig(): CustomPromptsConfig {
    return JSON.parse(JSON.stringify(this.defaultEnglishConfig));
  }

  /**
   * 判断用户是否使用了自定义提示词
   */
  public hasCustomPrompts(): boolean {
    try {
      return fs.existsSync(this.configPath);
    } catch (error) {
      console.error('检查自定义提示词配置文件失败:', error);
      return false;
    }
  }

  /**
   * 判断指定的提示词是否为自定义内容（非空且与默认值不同）
   */
  public isCustomPrompt(prompt: string, defaultPrompt: string): boolean {
    return prompt && prompt.trim() !== '' && prompt !== defaultPrompt;
  }

  /**
   * 合并用户配置和默认配置
   */
  private mergeWithDefaults(userConfig: any): CustomPromptsConfig {
    const merged = JSON.parse(JSON.stringify(this.defaultChineseConfig));
    
    // 深度合并配置
    
    if (userConfig.directAnswer) {
      if (userConfig.directAnswer.extraction) {
        Object.assign(merged.directAnswer.extraction, userConfig.directAnswer.extraction);
      }
      if (userConfig.directAnswer.debugging) {
        Object.assign(merged.directAnswer.debugging, userConfig.directAnswer.debugging);
      }
    }
    
    if (userConfig.voiceStreaming?.solution) {
      Object.assign(merged.voiceStreaming.solution, userConfig.voiceStreaming.solution);
    }
    
    return merged;
  }

  /**
   * 获取最终使用的提示词（优先级：自定义 > 系统默认）
   * @param promptType 提示词类型路径，如 ['directAnswer', 'extraction', 'userPrompt']
   * @param promptLanguage 提示词语言设置 ('chinese' | 'english')
   * @param variables 变量替换对象
   * @param hasUltraPermission 是否有Ultra权限
   */
  public getFinalPrompt(
    promptType: string[],
    promptLanguage: 'chinese' | 'english',
    variables: Record<string, any> = {},
    hasUltraPermission: boolean = false
  ): string {
    // 如果没有Ultra权限，直接使用系统默认提示词
    if (!hasUltraPermission) {
      return this.getSystemDefaultPrompt(promptType, promptLanguage, variables);
    }

    // 检查是否有自定义提示词配置文件
    if (!this.hasCustomPrompts()) {
      return this.getSystemDefaultPrompt(promptType, promptLanguage, variables);
    }

    // 加载自定义配置
    const customConfig = this.loadConfig();

    // 获取自定义提示词
    const customPrompt = this.getNestedValue(customConfig, promptType);

    // 获取对应的默认提示词用于比较
    const defaultPrompt = this.getSystemDefaultPrompt(promptType, 'chinese', {});

    // 如果自定义提示词存在且不为空，使用自定义提示词
    if (this.isCustomPrompt(customPrompt, defaultPrompt)) {
      return this.processPromptVariables(customPrompt, variables);
    }

    // 否则使用系统默认提示词
    return this.getSystemDefaultPrompt(promptType, promptLanguage, variables);
  }

  /**
   * 获取系统默认提示词
   */
  private getSystemDefaultPrompt(
    promptType: string[],
    promptLanguage: 'chinese' | 'english',
    variables: Record<string, any>
  ): string {
    const defaultConfig = promptLanguage === 'english' ? this.defaultEnglishConfig : this.defaultChineseConfig;
    const prompt = this.getNestedValue(defaultConfig, promptType);
    return this.processPromptVariables(prompt, variables);
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string[]): string {
    let current = obj;
    for (const key of path) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return '';
      }
    }
    return typeof current === 'string' ? current : '';
  }

  /**
   * 处理提示词中的变量替换
   */
  public processPromptVariables(prompt: string, variables: Record<string, any>): string {
    let processedPrompt = prompt;

    // 替换所有变量
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\$\\{${key}\\}`, 'g');
      processedPrompt = processedPrompt.replace(regex, String(value));
    }

    return processedPrompt;
  }
}

// 创建全局实例
export const customPromptsHelper = new CustomPromptsHelper();
