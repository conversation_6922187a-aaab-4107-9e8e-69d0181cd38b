import axios from 'axios';
import { app } from 'electron';
import fs from 'fs';
import path from 'path';
import os from 'os';
import crypto from 'crypto';
import { execSync } from 'child_process';

// 激活服务器 URL
const ACTIVATION_SERVER_URL = process.env.ACTIVATION_SERVER_URL || 'http://47.110.40.223:3000/api';

export class ActivationHelper {
  private activationFilePath: string;
  private machineIdFilePath: string;
  private machineId: string;
  private encryptionKey: string;
  private saltValue: string = 'encryption_salt_value'; // 用于生成加密密钥的固定盐值
  
  constructor() {
    // 激活文件路径
    this.activationFilePath = path.join(app.getPath('userData'), '.activation');
    //机器码只做验证
    this.machineIdFilePath = path.join(app.getPath('userData'), '.machineId');

    // 生成机器ID（每次启动都生成，但对同一设备保持稳定）
    this.machineId = this.generateStableMachineId();
    
    // 创建基于机器ID的加密密钥
    this.encryptionKey = this.generateEncryptionKey(this.machineId);

    //只做保存验证
    fs.writeFileSync(this.machineIdFilePath, this.machineId);
  }
  
  /**
   * 生成稳定的机器唯一标识（对同一台机器每次生成的结果相同）
   */
  private generateStableMachineId(): string {
    try {
      // 收集多种稳定的硬件标识符
      const stableIdentifiers: string[] = [];
      
      // 1. 首先获取最稳定的硬件序列号，这些通常不会变化
      if (process.platform === 'win32') {
        // 对于Windows系统，获取多种硬件序列号
        
        // 主板序列号（非常稳定）
        try {
          const motherboardOutput = execSync('wmic baseboard get serialnumber').toString();
          const motherboardMatch = motherboardOutput.match(/SerialNumber\s*\n\s*(\S+)/);
          if (motherboardMatch && motherboardMatch[1]) {
            // 使用主板序列号作为最主要标识
            stableIdentifiers.push(`mb:${motherboardMatch[1]}`);
          }
        } catch (e) {
          // 忽略错误
        }
        
        // BIOS序列号（非常稳定）
        try {
          const biosOutput = execSync('wmic bios get serialnumber').toString();
          const biosMatch = biosOutput.match(/SerialNumber\s*\n\s*(\S+)/);
          if (biosMatch && biosMatch[1]) {
            stableIdentifiers.push(`bios:${biosMatch[1]}`);
          }
        } catch (e) {
          // 忽略错误
        }
        
        // CPU ID（稳定）
        try {
          const cpuOutput = execSync('wmic cpu get processorid').toString();
          const cpuMatch = cpuOutput.match(/ProcessorId\s*\n\s*(\S+)/);
          if (cpuMatch && cpuMatch[1]) {
            stableIdentifiers.push(`cpu:${cpuMatch[1]}`);
          }
        } catch (e) {
          // 忽略错误
        }
        
        // 系统盘序列号（相对稳定，可能更换）
        try {
          const diskOutput = execSync('wmic diskdrive where index=0 get serialnumber').toString();
          const diskMatch = diskOutput.match(/SerialNumber\s*\n\s*(\S+)/);
          if (diskMatch && diskMatch[1]) {
            stableIdentifiers.push(`disk:${diskMatch[1]}`);
          }
        } catch (e) {
          // 忽略错误
        }
        
      } else if (process.platform === 'darwin') {
        // 对于macOS系统
        
        // 获取硬件UUID（最稳定）
        try {
          const uuidOutput = execSync('ioreg -rd1 -c IOPlatformExpertDevice | grep -i "UUID" | cut -c27-62').toString().trim();
          if (uuidOutput && uuidOutput.length > 0) {
            stableIdentifiers.push(`mac_uuid:${uuidOutput}`);
          }
        } catch (e) {
          // 忽略错误
        }
        
        // 获取序列号
        try {
          const serialOutput = execSync('ioreg -l | grep IOPlatformSerialNumber').toString();
          const serialMatch = serialOutput.match(/\"IOPlatformSerialNumber\" = \"(\S+)\"/);
          if (serialMatch && serialMatch[1]) {
            stableIdentifiers.push(`mac_serial:${serialMatch[1]}`);
          }
        } catch (e) {
          // 忽略错误
        }
        
      } else if (process.platform === 'linux') {
        // 对于Linux系统
        
        // 使用machine-id（非常稳定）
        try {
          if (fs.existsSync('/etc/machine-id')) {
            const machineIdOutput = fs.readFileSync('/etc/machine-id', 'utf-8').trim();
            if (machineIdOutput && machineIdOutput.length > 0) {
              stableIdentifiers.push(`linux_id:${machineIdOutput}`);
            }
          }
        } catch (e) {
          // 忽略错误
        }
        
        // 尝试获取DMIDECODE信息（需要root权限）
        try {
          const dmiOutput = execSync('dmidecode -s system-uuid').toString().trim();
          if (dmiOutput && dmiOutput.length > 0) {
            stableIdentifiers.push(`dmi_uuid:${dmiOutput}`);
          }
        } catch (e) {
          // 忽略错误，可能没有权限
        }
      }
      
      // 2. 如果没有获取到硬件序列号，使用操作系统信息和硬件特征
      if (stableIdentifiers.length === 0) {
        // CPU信息
        const cpuInfo = os.cpus();
        if (cpuInfo && cpuInfo.length > 0) {
          stableIdentifiers.push(`cpu_model:${cpuInfo[0].model}`);
        }
        
        // 操作系统信息
        stableIdentifiers.push(`os:${os.platform()}_${os.release()}_${os.arch()}`);
        
        // 总内存
        stableIdentifiers.push(`mem:${os.totalmem()}`);
        
        // 用户信息和计算机名
        try {
          stableIdentifiers.push(`host:${os.hostname()}`);
          stableIdentifiers.push(`user:${os.userInfo().username}`);
        } catch (e) {
          // 忽略错误
        }
      }
      
      // 确保我们至少有一些标识符
      if (stableIdentifiers.length === 0) {
        throw new Error('无法获取任何稳定的硬件标识符');
      }
      
      // 组合所有标识符并哈希，生成机器ID
      const combinedIdentifiers = stableIdentifiers.join('|');
      return crypto.createHash('sha256').update(combinedIdentifiers).digest('hex');
    } catch (error) {
      console.error('生成机器ID失败:', error);
      
      // 生成后备ID，使用更基本的系统信息
      const backupInfo = [
        os.hostname(),
        os.platform(),
        os.arch(),
        String(os.totalmem()),
        app.getPath('userData')
      ].join('_');
      
      return crypto.createHash('sha256').update(backupInfo).digest('hex');
    }
  }
  
  /**
   * 生成加密密钥
   */
  private generateEncryptionKey(machineId: string): string {
    return crypto.createHash('sha256')
      .update(machineId + this.saltValue)
      .digest('hex')
      .substring(0, 32); // AES-256 需要32字节密钥
  }
  
  /**
   * 加密数据
   */
  private encryptData(data: string): string {
    try {
      if (!this.encryptionKey) {
        throw new Error('加密密钥不存在');
      }
      
      const iv = crypto.randomBytes(16); // 初始化向量
      const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(this.encryptionKey), iv);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // 将 IV 与加密数据一起存储
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      console.error('加密数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 解密数据
   */
  private decryptData(encryptedData: string): string {
    try {
      if (!this.encryptionKey) {
        throw new Error('解密密钥不存在');
      }
      
      const parts = encryptedData.split(':');
      if (parts.length !== 2) {
        throw new Error('加密数据格式无效');
      }
      
      const iv = Buffer.from(parts[0], 'hex');
      const encrypted = parts[1];
      
      const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(this.encryptionKey), iv);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('解密数据失败:', error);
      throw error;
    }
  }
  
  /**
   * 检查应用是否已激活
   */
  public async isActivated(): Promise<boolean> {
    try {
      // 检查激活文件是否存在
      if (!fs.existsSync(this.activationFilePath)) {
        return false;
      }
      
      // 读取激活文件内容并解密
      const encryptedData = fs.readFileSync(this.activationFilePath, 'utf-8');
      const activationDataStr = this.decryptData(encryptedData);
      const activationData = JSON.parse(activationDataStr);

      // 兼容性处理：为旧版本数据添加默认planType字段
      if (!activationData.planType) {
        activationData.planType = 'standard';
      }
      
      // 验证激活数据与当前机器是否匹配
      if (activationData.machineId !== this.machineId) {
        console.warn('激活信息与当前机器不匹配，可能是激活文件被复制');
        return false;
      }
      
      // 在线验证激活状态
      try {
        const response = await axios.post(`${ACTIVATION_SERVER_URL}/verify`, {
          activationId: activationData.activationId,
          machineId: this.machineId
        });
        
        // 检查剩余调用次数或永久激活状态
        if (response.data.valid === true) {
          if (response.data.isPermanent) {
            // 永久激活无需检查调用次数
            // 检查是否需要更新本地数据
            let needUpdate = false;
            if (!activationData.isPermanent) {
              activationData.isPermanent = response.data.isPermanent;
              needUpdate = true;
            }
            if (response.data.planType && activationData.planType !== response.data.planType) {
              activationData.planType = response.data.planType;
              needUpdate = true;
            }
            if (needUpdate) {
              const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
              fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
            }
            return true;
          }else {
            // 检查是否需要更新本地数据
            let needUpdate = false;
            if (activationData.isPermanent) {
              activationData.isPermanent = response.data.isPermanent;
              needUpdate = true;
            }
            if (response.data.planType && activationData.planType !== response.data.planType) {
              activationData.planType = response.data.planType;
              needUpdate = true;
            }
            if (needUpdate) {
              const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
              fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
            }
          }
          
          // 检查剩余调用次数
          if (response.data.remainingCalls > 0) {
            // 更新本地存储的剩余调用次数和版本信息
            let needUpdate = false;
            if (activationData.remainingCalls !== response.data.remainingCalls) {
              activationData.remainingCalls = response.data.remainingCalls;
              needUpdate = true;
            }
            if (activationData.isPermanent !== response.data.isPermanent) {
              activationData.isPermanent = response.data.isPermanent;
              needUpdate = true;
            }
            if (response.data.planType && activationData.planType !== response.data.planType) {
              activationData.planType = response.data.planType;
              needUpdate = true;
            }
            if (needUpdate) {
              const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
              fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
            }

            return true;
          } else {
            console.log('激活码已用尽调用次数');
            return false;
          }
        }
        
        return false;
      } catch (error) {
        // 如果在线验证失败，使用本地验证作为备用
        console.error('在线验证失败:', error);
        // 如果本地数据显示是永久激活，可以临时允许
        if (activationData.isPermanent) {
          return true;
        }
        
        // 如果本地数据显示还有调用次数，也可以临时允许
        return activationData.remainingCalls > 0;
      }
    } catch (error) {
      console.error('激活验证出错:', error);
      return false;
    }
  }
  
  /**
   * 使用激活码激活应用
   */
  public async activate(activationCode: string): Promise<{ success: boolean; message: string }> {
    try {
      // 向激活服务器发送请求
      const response = await axios.post(`${ACTIVATION_SERVER_URL}/activate`, {
        activationCode,
        machineId: this.machineId,
        appVersion: app.getVersion()
      });
      
      if (response.data.success) {
        // 保存激活信息到本地（加密）
        const activationData = {
          activationId: response.data.activationId,
          machineId: this.machineId,
          activationDate: new Date().toISOString(),
          remainingCalls: response.data.remainingCalls,
          isPermanent: response.data.isPermanent,
          planType: response.data.planType || 'standard'
        };
        
        const encryptedData = this.encryptData(JSON.stringify(activationData));
        fs.writeFileSync(this.activationFilePath, encryptedData);
        
        return { 
          success: true, 
          message: '应用已成功激活！' 
        };
      } else {
        return { 
          success: false, 
          message: response.data.message || '激活失败，请检查激活码是否有效。' 
        };
      }
    } catch (error) {
      console.error('激活请求失败:', error);
      return { 
        success: false, 
        message: '无法连接到激活服务器，请检查网络连接后重试。'
      };
    }
  }
  
  /**
   * 消耗一次调用次数
   */
  public async consumeCall(): Promise<{ success: boolean; remainingCalls?: number; message: string }> {
    try {
      // 检查激活文件是否存在
      if (!fs.existsSync(this.activationFilePath)) {
        return { success: false, message: '应用未激活' };
      }
      
      // 读取激活文件内容并解密
      const encryptedData = fs.readFileSync(this.activationFilePath, 'utf-8');
      const activationDataStr = this.decryptData(encryptedData);
      const activationData = JSON.parse(activationDataStr);

      // 兼容性处理：为旧版本数据添加默认planType字段
      if (!activationData.planType) {
        activationData.planType = 'standard';
      }
      
      // 向激活服务器发送请求
      const response = await axios.post(`${ACTIVATION_SERVER_URL}/consume`, {
        activationId: activationData.activationId,
        machineId: this.machineId
      });
      
      if (response.data.success) {
        // 更新本地存储的剩余调用次数和版本信息
        let needUpdate = false;
        if (activationData.remainingCalls !== response.data.remainingCalls) {
          activationData.remainingCalls = response.data.remainingCalls;
          needUpdate = true;
        }
        if (response.data.planType && activationData.planType !== response.data.planType) {
          activationData.planType = response.data.planType;
          needUpdate = true;
        }
        if (needUpdate) {
          const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
          fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
        }

        return {
          success: true,
          remainingCalls: response.data.remainingCalls,
          message: '成功消耗一次调用'
        };
      } else {
        return { 
          success: false, 
          message: response.data.message || '消耗调用次数失败' 
        };
      }
    } catch (error) {
      console.error('消耗调用次数失败:', error);
      
      // 如果在线请求失败，尝试从本地减少次数作为备用方案
      try {
        if (fs.existsSync(this.activationFilePath)) {
          const encryptedData = fs.readFileSync(this.activationFilePath, 'utf-8');
          const activationDataStr = this.decryptData(encryptedData);
          const activationData = JSON.parse(activationDataStr);

          // 兼容性处理：为旧版本数据添加默认planType字段
          if (!activationData.planType) {
            activationData.planType = 'standard';
          }
          
          if (activationData.isPermanent) {
            return { 
              success: true, 
              remainingCalls: Infinity, 
              message: '永久激活码，无需消耗次数' 
            };
          }
          
          if (activationData.remainingCalls > 0) {
            activationData.remainingCalls -= 1;
            const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
            fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
            
            return { 
              success: true, 
              remainingCalls: activationData.remainingCalls,
              message: '离线模式：成功消耗一次调用' 
            };
          } else {
            return { 
              success: false, 
              remainingCalls: 0,
              message: '离线模式：剩余调用次数已用尽' 
            };
          }
        }
      } catch (localError) {
        console.error('离线消耗调用次数失败:', localError);
      }
      
      return { 
        success: false, 
        message: '无法连接到激活服务器，请检查网络连接后重试。' 
      };
    }
  }
  
  /**
   * 检查是否为ultra版本
   */
  public async isUltraVersion(): Promise<boolean> {
    try {
      // 检查激活文件是否存在
      if (!fs.existsSync(this.activationFilePath)) {
        return false;
      }

      // 读取激活文件内容并解密
      const encryptedData = fs.readFileSync(this.activationFilePath, 'utf-8');
      const activationDataStr = this.decryptData(encryptedData);
      const activationData = JSON.parse(activationDataStr);

      // 兼容性处理：为旧版本数据添加默认planType字段
      if (!activationData.planType) {
        activationData.planType = 'standard';
      }

      // 检查是否为ultra版本
      return activationData.planType === 'ultra';
    } catch (error) {
      console.error('检查ultra版本状态出错:', error);
      return false;
    }
  }

  /**
   * 获取剩余调用次数
   */
  public async getRemainingCalls(): Promise<{ success: boolean; remainingCalls?: number; isPermanent?: boolean; planType?: string; message: string }> {
    try {
      // 检查激活文件是否存在
      if (!fs.existsSync(this.activationFilePath)) {
        return { success: false, message: '应用未激活' };
      }
      
      // 读取激活文件内容并解密
      const encryptedData = fs.readFileSync(this.activationFilePath, 'utf-8');
      const activationDataStr = this.decryptData(encryptedData);
      const activationData = JSON.parse(activationDataStr);

      // 兼容性处理：为旧版本数据添加默认planType字段
      if (!activationData.planType) {
        activationData.planType = 'standard';
      }
      
      // 向激活服务器发送请求
      const response = await axios.post(`${ACTIVATION_SERVER_URL}/verify`, {
        activationId: activationData.activationId,
        machineId: this.machineId
      });
      
      if (response.data.success && response.data.valid) {
        // 更新本地存储的剩余调用次数和版本信息
        let needUpdate = false;
        if (activationData.remainingCalls !== response.data.remainingCalls) {
          activationData.remainingCalls = response.data.remainingCalls;
          needUpdate = true;
        }
        if (activationData.isPermanent !== response.data.isPermanent) {
          activationData.isPermanent = response.data.isPermanent;
          needUpdate = true;
        }
        if (response.data.planType && activationData.planType !== response.data.planType) {
          activationData.planType = response.data.planType;
          needUpdate = true;
        }
        if (needUpdate) {
          const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
          fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
        }

        return {
          success: true,
          remainingCalls: response.data.remainingCalls,
          isPermanent: response.data.isPermanent,
          planType: activationData.planType,
          message: '成功获取剩余调用次数'
        };
      } else {
        return { 
          success: false, 
          message: response.data.message || '验证失败' 
        };
      }
    } catch (error) {
      console.error('获取剩余调用次数失败:', error);
      
      // 如果在线请求失败，尝试从本地获取作为备用方案
      try {
        if (fs.existsSync(this.activationFilePath)) {
          const encryptedData = fs.readFileSync(this.activationFilePath, 'utf-8');
          const activationDataStr = this.decryptData(encryptedData);
          const activationData = JSON.parse(activationDataStr);

          // 兼容性处理：为旧版本数据添加默认planType字段
          if (!activationData.planType) {
            activationData.planType = 'standard';
            // 更新本地存储以包含planType字段
            const updatedEncryptedData = this.encryptData(JSON.stringify(activationData));
            fs.writeFileSync(this.activationFilePath, updatedEncryptedData);
          }

          return {
            success: true,
            remainingCalls: activationData.remainingCalls,
            isPermanent: activationData.isPermanent,
            planType: activationData.planType,
            message: '离线模式：从本地获取剩余调用次数'
          };
        }
      } catch (localError) {
        console.error('离线获取调用次数失败:', localError);
      }
      
      return { 
        success: false, 
        message: '无法连接到激活服务器，请检查网络连接后重试。' 
      };
    }
  }
} 