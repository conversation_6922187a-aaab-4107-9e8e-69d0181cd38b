// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, BrowserWindow, dialog } from "electron"
import { configHelper } from "./ConfigHelper"
import { customPromptsHelper } from "./CustomPromptsHelper"
import { logHelper } from "./LogHelper"
// Interface for OpenRouter API requests
interface OpenRouterMessage {
  role: string;
  content: string | Array<{
    type: string;
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

interface OpenRouterResponse {
  id: string;
  choices: Array<{
    message: {
      content: string;
      role: string;
      reasoning: string;
    };
    finish_reason: string;
  }>;
}

// Interface for Zhipu and Custom API requests (OpenAI compatible)
interface ApiMessage {
  role: string;
  content: string | Array<{
    type: string;
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

interface ApiResponse {
  id: string;
  choices: Array<{
    message: {
      content: string;
      role: string;
      reasoning_content: string;
    };
    finish_reason: string;
  }>;
}

export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openrouterApiKey: string | null = null
  private zhipuApiKey: string | null = null
  private zhipuBaseUrl: string = "https://open.bigmodel.cn/api/paas/v4"
  private customApiKey: string | null = null
  private customBaseUrl: string | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null
  private currentAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()

    // Initialize AI client based on config
    this.initializeAIClient();

    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient();
    });
  }

  /**
   * 解析智谱AI深度思考模型的响应内容，提取推理部分和实际回答部分
   * @param content 原始响应内容
   * @returns {thoughts: string, actualContent: string}
   */
  private parseZhipuThinkingContent(content: string): { thoughts: string; actualContent: string } {
    // 匹配 <think>...</think> 标签中的内容
    const thinkRegex = /<think>([\s\S]*?)<\/think>/i;
    const match = content.match(thinkRegex);

    if (match) {
      const thoughts = match[1].trim(); // 提取推理内容
      const actualContent = content.replace(thinkRegex, '').trim(); // 移除推理部分，保留实际回答
      return { thoughts, actualContent };
    }

    // 如果没有找到 <think> 标签，返回原内容作为实际回答
    return { thoughts: "无推理思考", actualContent: content };
  }
  
  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient(): void {
    try {
      const config = configHelper.loadConfig();

      if (config.apiKey) {
        if (config.apiProvider === "openrouter") {
          this.openrouterApiKey = config.apiKey;
          this.zhipuApiKey = null;
          this.customApiKey = null;
          this.customBaseUrl = null;
          console.log("OpenRouter API key set successfully");
        } else if (config.apiProvider === "zhipu") {
          this.zhipuApiKey = config.apiKey;
          this.openrouterApiKey = null;
          this.customApiKey = null;
          this.customBaseUrl = null;
          console.log("Zhipu AI API key set successfully");
        } else if (config.apiProvider === "custom" && config.baseUrl) {
          this.customApiKey = config.apiKey;
          this.customBaseUrl = config.baseUrl;
          this.openrouterApiKey = null;
          this.zhipuApiKey = null;
          console.log("Custom API configured with baseURL:", config.baseUrl);
        } else {
          this.openrouterApiKey = null;
          this.zhipuApiKey = null;
          this.customApiKey = null;
          this.customBaseUrl = null;
          console.warn("Invalid configuration for API provider");
        }
      } else {
        this.openrouterApiKey = null;
        this.zhipuApiKey = null;
        this.customApiKey = null;
        this.customBaseUrl = null;
        console.warn("No API key available, AI client not initialized");
      }
    } catch (error) {
      console.error("Failed to initialize AI client:", error);
      this.openrouterApiKey = null;
      this.zhipuApiKey = null;
      this.customApiKey = null;
      this.customBaseUrl = null;
    }
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getCredits(): Promise<number> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return 999 // Unlimited credits in this version

    try {
      await this.waitForInitialization(mainWindow)
      return 999 // Always return sufficient credits to work
    } catch (error) {
      console.error("Error getting credits:", error)
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig();
      if (config.language) {
        return config.language;
      }
      
      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language;
          }
        } catch (err) {
          console.warn("Could not get language from window", err);
        }
      }
      
      // Default fallback
      return "python";
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  /**
   * 获取用户权限信息
   */
  private async getUserPermissions(): Promise<{hasUltraPermission: boolean}> {
    try {
      if (!this.deps.activationHelper) {
        return { hasUltraPermission: false };
      }

      const isUltra = await this.deps.activationHelper.isUltraVersion();

      return { hasUltraPermission: isUltra };
    } catch (error) {
      console.error('获取用户权限信息失败:', error);
      return { hasUltraPermission: false };
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();
    
    // 首先检查激活状态，确保这个检查在任何其他操作之前进行
    console.log("正在检查激活状态...");
    
    // 先主动获取最新的激活状态，而不是只依赖 this.deps.isActivated
    let isCurrentlyActivated = false;
    try {
      if (this.deps.activationHelper) {
        // 主动刷新激活状态
        isCurrentlyActivated = await this.deps.activationHelper.isActivated();
        console.log("刷新后的激活状态:", isCurrentlyActivated);
        
        // 更新 deps 中的状态
        this.deps.isActivated = isCurrentlyActivated;
      }
    } catch (error) {
      console.error("主动检查激活状态时发生错误:", error);
    }
    
    console.log("最终使用的激活状态:", this.deps.isActivated);
    console.log("activationHelper:", this.deps.activationHelper ? "已初始化" : "未初始化");
    
    // 新增：检查激活状态并消耗调用次数
    try {
      // 使用刷新后的激活状态进行检查
      if (!this.deps.isActivated || !this.deps.activationHelper) {
        console.log("应用未激活或激活系统未初始化");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
        );
        return;
      }
      
      console.log("开始消耗调用次数...");
      // 直接调用激活辅助类消耗调用次数
      const consumeResult = await this.deps.activationHelper.consumeCall();
      console.log("消耗调用次数结果:", consumeResult);
      
      if (!consumeResult.success) {
        console.log("调用次数消耗失败:", consumeResult.message);
        
        // 如果是因为调用次数用尽，发送特定事件
        if (consumeResult.message.includes('用尽') || consumeResult.message.includes('expired')) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.OUT_OF_CREDITS
          );
        } else if (consumeResult.message.includes('未激活')) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
          );
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.ACTIVATION_ERROR,
            consumeResult.message
          );
        }
        return;
      }
      
      // 更新界面显示剩余调用次数（如果需要的话）
      if (consumeResult.remainingCalls !== undefined) {
        mainWindow.webContents.send(
          "remaining-calls-updated", 
          { remainingCalls: consumeResult.remainingCalls }
        );
      }
    } catch (error) {
      console.error("检查激活状态出错:", error);
      // 出错时也显示激活屏幕
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
      );
      return;
    }

    // First verify we have a valid AI client
    console.log("检查 AI 客户端状态...");
    if (!this.openrouterApiKey && !this.zhipuApiKey && !this.customApiKey) {
      console.log("API 客户端未设置，尝试重新初始化");
      this.initializeAIClient();

      if (!this.openrouterApiKey && !this.zhipuApiKey && !this.customApiKey) {
        console.error("API 客户端未初始化");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }
    
    console.log("AI 客户端和激活状态均正常，继续处理...");

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)
      
      // Check if the queue is empty
      if (!screenshotQueue || screenshotQueue.length === 0) {
        console.log("No screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter(path => fs.existsSync(path));
      if (existingScreenshots.length === 0) {
        console.log("Screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map(async (path) => {
            try {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);
        
        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data");
        }

        const result = await this.processScreenshotsHelper(validScreenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)
      
      // Check if the extra queue is empty
      if (!extraScreenshotQueue || extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        
        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter(path => fs.existsSync(path));
      if (existingExtraScreenshots.length === 0) {
        console.log("Extra screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }
      
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];
        
        const screenshots = await Promise.all(
          allPaths.map(async (path) => {
            try {
              if (!fs.existsSync(path)) {
                console.warn(`Screenshot file does not exist: ${path}`);
                return null;
              }
              
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )
        
        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);
        
        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data for debugging");
        }
        
        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  // 新增：直接解答模式处理方法
  public async processDirectAnswer(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();

    console.log("开始处理直接解答模式");

    // 检查激活状态
    console.log("正在检查激活状态...");

    let isCurrentlyActivated = false;
    try {
      if (this.deps.activationHelper) {
        isCurrentlyActivated = await this.deps.activationHelper.isActivated();
        console.log("刷新后的激活状态:", isCurrentlyActivated);
        this.deps.isActivated = isCurrentlyActivated;
      }
    } catch (error) {
      console.error("主动检查激活状态时发生错误:", error);
    }

    console.log("最终使用的激活状态:", this.deps.isActivated);

    // 检查激活状态和消耗调用次数
    try {
      if (!this.deps.isActivated) {
        console.log("应用未激活，无法处理截图");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
        );
        return;
      }

      console.log("开始消耗调用次数...");
      const consumeResult = await this.deps.activationHelper.consumeCall();
      console.log("消耗调用次数结果:", consumeResult);

      if (!consumeResult.success) {
        console.log("调用次数消耗失败:", consumeResult.message);

        if (consumeResult.message.includes('用尽') || consumeResult.message.includes('expired')) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.OUT_OF_CREDITS
          );
        } else if (consumeResult.message.includes('未激活')) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
          );
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.ACTIVATION_ERROR,
            consumeResult.message
          );
        }
        return;
      }

      if (consumeResult.remainingCalls !== undefined) {
        mainWindow.webContents.send(
          "remaining-calls-updated",
          { remainingCalls: consumeResult.remainingCalls }
        );
      }
    } catch (error) {
      console.error("检查激活状态出错:", error);
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
      );
      return;
    }

    // 检查API客户端
    console.log("检查 AI 客户端状态...");
    if (!this.openrouterApiKey && !this.zhipuApiKey && !this.customApiKey) {
      console.log("API 客户端未设置，尝试重新初始化");
      this.initializeAIClient();

      if (!this.openrouterApiKey && !this.zhipuApiKey && !this.customApiKey) {
        console.error("API 客户端未初始化");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }

    // 检查当前视图和状态，决定使用直接解答还是直接调试
    const currentView = this.deps.getView();
    console.log("当前视图:", currentView);

    // 如果当前在solutions视图，使用直接调试模式
    if (currentView === "solutions") {
      console.log("当前在solutions视图，切换到直接调试模式");
      return this.processDirectDebug();
    }

    console.log("使用直接解答模式");

    // 获取当前视图的截图
    let screenshots: Array<{ path: string; data: string }> = [];

    if (currentView === "queue") {
      const queue = this.deps.getScreenshotQueue();
      if (queue.length === 0) {
        console.log("没有截图可处理");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS
        );
        return;
      }

      screenshots = await Promise.all(
        queue.map(async (path) => ({
          path,
          data: await this.deps.getImagePreview(path)
        }))
      );
    } else {
      const extraQueue = this.deps.getExtraScreenshotQueue();
      if (extraQueue.length === 0) {
        console.log("没有额外截图可处理");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS
        );
        return;
      }

      screenshots = await Promise.all(
        extraQueue.map(async (path) => ({
          path,
          data: await this.deps.getImagePreview(path)
        }))
      );
    }

    // 创建取消令牌
    this.currentAbortController = new AbortController();
    const signal = this.currentAbortController.signal;

    try {
      // 发送开始事件，触发加载状态
      if (mainWindow) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START);
      }

      // 直接使用问题提取模型进行解答
      await this.processDirectAnswerHelper(screenshots, signal);
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log("直接解答处理被取消");
        return;
      }
      console.error("直接解答处理失败:", error);
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.PROCESSING_ERROR,
        error.message || "处理失败"
      );
    }
  }

  // 新增：直接调试模式处理方法
  public async processDirectDebug(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();

    // 检查激活状态
    console.log("正在检查激活状态...");

    let isCurrentlyActivated = false;
    try {
      if (this.deps.activationHelper) {
        isCurrentlyActivated = await this.deps.activationHelper.isActivated();
        console.log("刷新后的激活状态:", isCurrentlyActivated);
        this.deps.isActivated = isCurrentlyActivated;
      }
    } catch (error) {
      console.error("主动检查激活状态时发生错误:", error);
    }

    // 检查激活状态和消耗调用次数
    try {
      if (!this.deps.isActivated) {
        console.log("应用未激活，无法处理截图");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
        );
        return;
      }

      console.log("开始消耗调用次数...");
      const consumeResult = await this.deps.activationHelper.consumeCall();
      console.log("消耗调用次数结果:", consumeResult);

      if (!consumeResult.success) {
        console.log("调用次数消耗失败:", consumeResult.message);

        if (consumeResult.message.includes('用尽') || consumeResult.message.includes('expired')) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.OUT_OF_CREDITS
          );
        } else if (consumeResult.message.includes('未激活')) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
          );
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.ACTIVATION_ERROR,
            consumeResult.message
          );
        }
        return;
      }

      if (consumeResult.remainingCalls !== undefined) {
        mainWindow.webContents.send(
          "remaining-calls-updated",
          { remainingCalls: consumeResult.remainingCalls }
        );
      }
    } catch (error) {
      console.error("检查激活状态出错:", error);
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.ACTIVATION_REQUIRED
      );
      return;
    }

    // 检查API客户端
    console.log("检查 AI 客户端状态...");
    if (!this.openrouterApiKey && !this.zhipuApiKey && !this.customApiKey) {
      console.log("API 客户端未设置，尝试重新初始化");
      this.initializeAIClient();

      if (!this.openrouterApiKey && !this.zhipuApiKey && !this.customApiKey) {
        console.error("API 客户端未初始化");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }

    // 获取所有截图（包括queue和extra）
    const queueScreenshots = this.deps.getScreenshotQueue();
    const extraScreenshots = this.deps.getExtraScreenshotQueue();
    const allScreenshotPaths = [...queueScreenshots, ...extraScreenshots];

    if (allScreenshotPaths.length === 0) {
      console.log("没有截图可处理");
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS
      );
      return;
    }

    const screenshots = await Promise.all(
      allScreenshotPaths.map(async (path) => ({
        path,
        data: await this.deps.getImagePreview(path)
      }))
    );

    // 创建取消令牌
    this.currentAbortController = new AbortController();
    const signal = this.currentAbortController.signal;

    try {
      // 发送调试开始事件，触发加载状态
      if (mainWindow) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START);
      }

      // 直接使用调试模型进行分析
      await this.processDirectDebugHelper(screenshots, signal);
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log("直接调试处理被取消");
        return;
      }
      console.error("直接调试处理失败:", error);
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.PROCESSING_ERROR,
        error.message || "处理失败"
      );
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // Step 1: Extract problem info using AI Vision API (OpenAI or Gemini)
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Analyzing problem from screenshots...",
          progress: 20
        });
      }

      let problemInfo;

      // Support all three API providers
      if (config.apiProvider === "openrouter") {
        if (!this.openrouterApiKey) {
          return {
            success: false,
            error: "OpenRouter API 密钥未配置。请检查您的设置。"
          };
        }
      } else if (config.apiProvider === "zhipu") {
        if (!this.zhipuApiKey) {
          return {
            success: false,
            error: "智谱AI API密钥未配置。请检查您的设置。"
          };
        }
      } else if (config.apiProvider === "custom") {
        // 验证Ultra版本权限
        if (!this.deps.activationHelper) {
          return {
            success: false,
            error: "激活系统未初始化。"
          };
        }

        const isUltra = await this.deps.activationHelper.isUltraVersion();
        if (!isUltra) {
          return {
            success: false,
            error: "自定义API功能需要Ultra版本，请升级后使用。"
          };
        }

        if (!this.customApiKey || !this.customBaseUrl) {
          return {
            success: false,
            error: "自定义API配置不完整。请检查您的设置。"
          };
        }
      } else {
        return {
          success: false,
          error: "不支持的API提供商。"
        };
      }

      // Common logic for all providers
      if (config.apiProvider === "openrouter" || config.apiProvider === "zhipu" || config.apiProvider === "custom") {

        try {
          // 根据语言设置选择合适的系统提示
          let systemPrompt = "";
          let userPrompt = "";

          // 选择对应语言的提示词
          if (config.promptLanguage === "english") {
            // 英文提示词
            systemPrompt = "You are a problem analysis assistant. Please analyze the screenshots of the problem and extract all relevant information. First identify the problem type (algorithm coding problem, scenario Q&A, short answer, multiple choice, or true/false), then extract specific content. Return the information in JSON format with the following fields: question_type, problem_statement. And depending on the type, provide these fields: 1. For algorithm problems: include constraints, example_input, example_output. 2. For scenario Q&A: include scenario_context. 3. For multiple choice: include options (array). 4. For true/false and short answer: no additional fields needed. Return only structured JSON without any other text.";

            userPrompt = `Please extract problem details from these screenshots, returning in JSON format. Correctly identifying the problem type is crucial. If it's a programming problem, we'll be using ${language} as our preferred programming language.`;
          } else {
            // 中文提示词（默认）
            systemPrompt = "你是一个题目分析助手。请分析截图中的题目并提取所有相关信息。首先确定题目类型（算法编程题、场景问答题、简答题、选择题或判断题），然后提取具体内容。请以JSON格式返回信息，必须包含以下字段：question_type（题目类型）, problem_statement（问题陈述）。以及根据题型提供以下字段：\n1. 算法编程题：包含constraints（约束条件）, example_input（示例输入）, example_output（示例输出）\n2. 场景问答题：包含scenario_context（场景上下文）\n3. 选择题：包含options（选项数组）\n4. 判断题和简答题：无需额外字段\n请仅返回结构化的JSON，不要包含其他文本。";

            userPrompt = `请从这些截图中提取题目详情，以JSON格式返回。正确识别题目类型非常重要。如果是编程题，我们将使用的首选编程语言是${language}。`;
          }

          // 创建消息结构
          const messages = [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: userPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image_url",
                  image_url: { url: `data:image/png;base64,${data}` }
                }))
              ]
            }
          ];

          let responseData: any;
          let responseContent: string;
          let requestLogFilename: string;

          if (config.apiProvider === "openrouter") {
            // 记录 API 请求
            requestLogFilename = logHelper.logApiRequest(
              "openrouter",
              "提取问题",
              config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free",
              {
                model: config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free",
                messages: messages
              }
            );

            const response = await axios.default.post(
              "https://openrouter.ai/api/v1/chat/completions",
              {
                model: config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free",
                messages: messages,
                max_tokens: 4000,
                temperature: 0.2
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.openrouterApiKey}`,
                  "Content-Type": "application/json",
                  "HTTP-Referer": "https://github.com"
                },
                signal
              }
            );

            responseData = response.data as OpenRouterResponse;
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从 OpenRouter API 收到空响应");
            }
            responseContent = responseData.choices[0].message.content;
          } else if (config.apiProvider === "zhipu") {
            // 记录 API 请求
            requestLogFilename = logHelper.logApiRequest(
              "zhipu",
              "提取问题",
              config.extractionModel || "glm-4v-plus-0111",
              {
                model: config.extractionModel || "glm-4v-plus-0111",
                messages: messages
              }
            );

            const response = await axios.default.post(
              `${this.zhipuBaseUrl}/chat/completions`,
              {
                model: config.extractionModel || "glm-4v-plus-0111",
                messages: messages,
                max_tokens: 4000,
                temperature: 0.2
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.zhipuApiKey}`,
                  "Content-Type": "application/json"
                },
                signal
              }
            );

            responseData = response.data as ApiResponse;
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从智谱AI收到空响应");
            }
            responseContent = responseData.choices[0].message.content;
          } else if (config.apiProvider === "custom") {
            // 记录 API 请求
            requestLogFilename = logHelper.logApiRequest(
              "custom",
              "提取问题",
              config.extractionModel || "gpt-4-vision-preview",
              {
                model: config.extractionModel || "gpt-4-vision-preview",
                messages: messages
              }
            );

            const response = await axios.default.post(
              `${this.customBaseUrl}/chat/completions`,
              {
                model: config.extractionModel || "gpt-4-vision-preview",
                messages: messages,
                max_tokens: 4000,
                temperature: 0.2
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.customApiKey}`,
                  "Content-Type": "application/json"
                },
                signal
              }
            );

            responseData = response.data as ApiResponse;
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从自定义API收到空响应");
            }
            responseContent = responseData.choices[0].message.content;
          } else {
            throw new Error("不支持的API提供商");
          }

          // 记录 API 响应
          const responseText = responseContent;

          // 处理可能将 JSON 包含在 markdown 代码块中的情况
          const jsonText = responseText.replace(/```json|```/g, '').trim();

          try {
            problemInfo = JSON.parse(jsonText);

            // 记录解析结果
            logHelper.logApiResponse(requestLogFilename, responseData, problemInfo);
          } catch (parseError) {
            logHelper.logError(config.apiProvider, "提取问题-JSON解析", {
              error: parseError,
              content: jsonText
            });
            throw new Error(`解析 ${config.apiProvider} API 响应的 JSON 失败`);
          }
        } catch (error: any) {
          logHelper.logError(config.apiProvider, "提取问题", error);

          if (error.response) {
            if (error.response.status === 401) {
              return {
                success: false,
                error: `${config.apiProvider} API 密钥无效。请检查您的设置。`
              };
            } else if (error.response.status === 402) {
              return {
                success: false,
                error: `${config.apiProvider} API 您的账户或 API 密钥的余额不足，请充值后再重试。`
              };
            } else if (error.response.status === 429) {
              return {
                success: false,
                error: `${config.apiProvider} API 对${config.extractionModel}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`
              };
            } else if (error.response.status === 404) {
              return {
                success: false,
                error: `${config.apiProvider} API平台 可能已将${config.extractionModel}模型下架，没有可用的端点了。请换模型。`
              };
            }
          }

          return {
            success: false,
            error: error.message || `通过 ${config.apiProvider} API 提取问题失败。请检查您的 API 密钥或稍后再试。`
          };
        }
      }

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        });
      }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo);

      // Send first success event
      if (mainWindow) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Generate solutions after successful extraction
        const solutionsResult = await this.generateSolutionsHelper(signal);
        if (solutionsResult.success) {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();

          // Final progress update
          mainWindow.webContents.send("processing-status", {
            message: "Solution generated successfully",
            progress: 100
          });

          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch (error: any) {
      // If the request was cancelled, don't retry
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      // Handle API errors specifically
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if (error?.response?.status === 500) {
        return {
          success: false,
          error: "API server error. Please try again later."
        };
      }

      console.error("API Error Details:", error);
      return {
        success: false,
        error: error.message || "Failed to process screenshots. Please try again."
      };
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Creating optimal solution with detailed explanations...",
          progress: 60
        });
      }

      // 确定题目类型并创建相应的提示词
      let promptText = "";
      const questionType = problemInfo.question_type || "算法编程题"; // 默认为算法编程题

      // 根据语言设置选择提示词语言
      const useEnglishPrompts = config.promptLanguage === "english";

      // 根据题型选择不同的提示词模板
      if (useEnglishPrompts) {
        // 英文提示词
        switch(questionType) {
          case "算法编程题":
          case "algorithm coding problem":
            promptText = `
Generate a detailed solution for the following programming problem:

Problem Statement:
${problemInfo.problem_statement}

Constraints:
${problemInfo.constraints ? JSON.stringify(problemInfo.constraints, null, 2) : "No specific constraints provided."}

Example Input:
${problemInfo.example_input ? JSON.stringify(problemInfo.example_input, null, 2) : "No example input provided."}

Example Output:
${problemInfo.example_output ? JSON.stringify(problemInfo.example_output, null, 2) : "No example output provided."}

Programming language: ${language}

Your response should follow this format:
1. Code: Clean, optimized implementation in ${language}
2. Approach: Key insights and reasoning behind your approach
3. Time Complexity: O(X), with detailed explanation (at least 2 sentences)
4. Space Complexity: O(X), with detailed explanation (at least 2 sentences)

For complexity explanations, please be detailed. For example: "Time Complexity: O(n) because we only need to traverse the array once. This is optimal as we need to check each element at least once to find the solution." or "Space Complexity: O(n) because in the worst case, we need to store all elements in the hash table. The extra space scales linearly with input size."

Your solution should be efficient, clearly commented, and handle edge cases.
`;
            break;

          case "场景问答题":
          case "scenario Q&A":
            promptText = `
Provide a comprehensive solution for the following scenario-based question:

Problem Statement:
${problemInfo.problem_statement}

Scenario Context:
${problemInfo.scenario_context || "No additional scenario context provided."}

Please provide:
1. Detailed problem analysis: Clear identification of core requirements and goals
2. Solution approach: Analysis of technical challenges and specific solutions for each
3. Architecture overview (if appropriate): If needed, outline the solution architecture showing main components and their interactions
4. Summary: Key advantages of your solution, potential challenges, and risk mitigation strategies

Your answer should be comprehensive but concise, avoiding redundancy and focusing on the core issues.
If appropriate, include brief code examples, architectural diagrams, or pseudocode in ${language} to illustrate your solution.
`;
            break;

          case "简答题":
          case "short answer":
            promptText = `
Please provide a detailed answer to the following question:

Question:
${problemInfo.problem_statement}

Please include:
1. Comprehensive answer: Clearly and accurately addressing the core content
2. Key concept explanations: Explaining important concepts mentioned in the answer
3. Code example (if applicable): A brief code example in ${language} to illustrate your point
4. Best practices: What should be considered when applying this in practice

Your answer should be comprehensive but concise, avoiding redundancy and focusing on the core issues.
`;
            break;

          case "选择题":
          case "multiple choice":
            promptText = `
Please answer the following multiple choice question:

Question:
${problemInfo.problem_statement}

Options:
${problemInfo.options ? JSON.stringify(problemInfo.options, null, 2) : "Option list not provided."}

Please include:
1. Correct answer: Clearly indicate your chosen option
2. Justification: Detailed explanation of why this option is correct
3. Analysis of other options: Brief explanation of why other options are incorrect
4. Related knowledge: Additional relevant information about the topic

Your answer should be clear, well-reasoned, and analyzed from a professional perspective.
`;
            break;

          case "判断题":
          case "true/false":
            promptText = `
Please answer the following true/false question:

Question:
${problemInfo.problem_statement}

Please provide:
1. Judgment result: Clearly state "True" or "False"
2. Justification: Detailed explanation of your reasoning
3. Clarification: If the original statement is partially correct or incorrect, explain the specific situation
4. Related knowledge: Important information related to the question

Your answer should be clear, accurate, and avoid ambiguous statements.
`;
            break;

          default:
            // Default prompt for other types of questions
            promptText = `
Please provide a detailed answer to the following question:

Question:
${problemInfo.problem_statement}

Please provide a comprehensive and accurate answer including necessary explanations and analysis.
`;
        }
      } else {
        // 中文提示词（保留原来的中文提示词）
        switch(questionType) {
          case "算法编程题":
            promptText = `
为以下编程问题生成详细的解决方案：

问题陈述：
${problemInfo.problem_statement}

约束条件：
${problemInfo.constraints ? JSON.stringify(problemInfo.constraints, null, 2) : "没有提供具体约束条件。"}

示例输入：
${problemInfo.example_input ? JSON.stringify(problemInfo.example_input, null, 2) : "没有提供示例输入。"}

示例输出：
${problemInfo.example_output ? JSON.stringify(problemInfo.example_output, null, 2) : "没有提供示例输出。"}

编程语言: ${language}

我需要回复的格式如下：
1. 代码：一个在${language}中干净、优化的实现
2. 思路解析：列出关键思路和你采用该方法的原因
3. 时间复杂度：O(X)，并提供详细解释（至少2句话）
4. 空间复杂度：O(X)，并提供详细解释（至少2句话）

对于复杂度解释，请详细说明。例如："时间复杂度：O(n)，因为我们只需要遍历数组一次。这是最优的，因为我们至少需要检查每个元素一次才能找到解决方案。" 或 "空间复杂度：O(n)，因为在最坏情况下，我们需要在哈希表中存储所有元素。额外空间与输入大小成线性关系。"

你的解决方案应该高效、注释清晰，并处理边界情况。
`;
            break;

          case "场景问答题":
            promptText = `
为以下场景问答题提供全面的解决方案：

问题陈述：
${problemInfo.problem_statement}

场景上下文：
${problemInfo.scenario_context || "没有提供额外的场景上下文。"}

请提供：
1. 详细分析问题：明确问题的核心需求和目标
2. 问题的解决方案：分析问题中的技术难点，针对每个难点提供具体的解决思路
3. 架构展示（如果你觉得有必要）：如果你觉得有必要，可以根据解决方案，提供一份解决方案架构展示图，说明主要组件及其交互方式
4. 总结：方案的主要优势，可能的挑战和风险应对策略

回答应当全面但简洁，避免冗余内容，直击问题核心。
如果你觉得有必要，请在适当位置提供简要的代码示例、架构图示意或伪代码来说明你的方案，代码需使用${language}语言。
`;
            break;

          case "简答题":
            promptText = `
请针对以下简答题提供详细解答：

问题：
${problemInfo.problem_statement}

请提供：
1. 详细解答：清晰、准确地回答问题的核心内容
2. 关键概念解释：解释回答中涉及的重要概念
3. 代码示例（如适用）：用${language}提供一个简短的代码示例来说明你的观点
4. 最佳实践建议：在实际应用中应该注意什么

回答应当全面但简洁，避免冗余内容，直击问题核心。
`;
            break;

          case "选择题":
            promptText = `
请回答以下选择题：

问题：
${problemInfo.problem_statement}

选项：
${problemInfo.options ? JSON.stringify(problemInfo.options, null, 2) : "未提供选项列表。"}

请提供：
1. 正确答案：明确指出你选择的选项
2. 选择依据：详细解释为什么这个选项是正确的
3. 其他选项分析：简要说明为什么其他选项不正确
4. 相关知识点补充：补充与问题相关的重要知识点

回答应当清晰、有理有据，并从专业角度分析问题。
`;
            break;

          case "判断题":
            promptText = `
请回答以下判断题：

问题：
${problemInfo.problem_statement}

请提供：
1. 判断结果：明确指出"正确"或"错误"
2. 判断依据：详细解释你做出这一判断的理由
3. 补充说明：如果原陈述有部分正确或错误，请解释清楚具体情况
4. 相关知识点：补充与问题相关的重要知识

回答应当清晰、准确，避免模糊不清的表述。
`;
            break;

          default:
            // 默认使用算法编程题的提示词
            promptText = `
为以下问题提供详细解答：

问题：
${problemInfo.problem_statement}

请提供全面、准确的解答，并包含必要的解释和分析。
`;
        }
      }

      let responseContent = "";

      // Support all three API providers
      if (config.apiProvider === "openrouter") {
        if (!this.openrouterApiKey) {
          return {
            success: false,
            error: "OpenRouter API 密钥未配置。请检查您的设置。"
          };
        }
      } else if (config.apiProvider === "zhipu") {
        if (!this.zhipuApiKey) {
          return {
            success: false,
            error: "智谱AI API密钥未配置。请检查您的设置。"
          };
        }
      } else if (config.apiProvider === "custom") {
        // 验证Ultra版本权限
        if (!this.deps.activationHelper) {
          return {
            success: false,
            error: "激活系统未初始化。"
          };
        }

        const isUltra = await this.deps.activationHelper.isUltraVersion();
        if (!isUltra) {
          return {
            success: false,
            error: "自定义API功能需要Ultra版本，请升级后使用。"
          };
        }

        if (!this.customApiKey || !this.customBaseUrl) {
          return {
            success: false,
            error: "自定义API配置不完整。请检查您的设置。"
          };
        }
      } else {
        return {
          success: false,
          error: "不支持的API提供商。"
        };
      }

      // Common logic for all providers
      if (config.apiProvider === "openrouter" || config.apiProvider === "zhipu" || config.apiProvider === "custom") {

        try {
          // 定义英文和中文系统提示词
          let englishSystemPrompt = "You are a professional coding interview solution assistant. Please provide the best answer based on the question type. For coding problems, provide a clear, optimal code solution; for scenario Q&A, analyze from multiple perspectives and provide a systematic solution; for multiple choice and true/false questions, give clear answers with analysis; for short answer questions, provide concise and comprehensive answers. Please respond in English.";

          let chineseSystemPrompt = "你是一个专业的程序员面试题目解答助手。请根据题目类型提供最佳解答。对于编程题，提供清晰、最优的代码解决方案；对于场景问答题，从多个角度分析并提供系统性的解决方案；对于选择题和判断题，给出明确的答案和分析依据；对于简答题，提供简明全面的解答。请用中文回复。";

          // 根据语言设置选择系统提示词
          let systemPrompt = config.promptLanguage === "english" ? englishSystemPrompt : chineseSystemPrompt;

          // 使用标准的消息格式
          const messages = [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: promptText
            }
          ];

          let responseData: any;
          let thoughts: string = "无推理思考";
          let requestLogFilename: string;

          if (config.apiProvider === "openrouter") {
            const apiRequestBody = {
              model: config.solutionModel || "z-ai/glm-4.5-air:free",
              messages: messages,
              max_tokens: 18000,
              temperature: 0.7,
              reasoning: {
                enabled: config.openrouterReasoningEnabled || false
              }
            };

            // 记录 API 请求
            requestLogFilename = logHelper.logApiRequest(
                "openrouter",
                "生成解决方案",
                config.solutionModel,
                apiRequestBody
            );

            const response = await axios.default.post(
                "https://openrouter.ai/api/v1/chat/completions",
                apiRequestBody,
                {
                  headers: {
                    "Authorization": `Bearer ${this.openrouterApiKey}`,
                    "Content-Type": "application/json",
                    "HTTP-Referer": "https://github.com"
                  },
                  signal
                }
            );

            responseData = response.data as OpenRouterResponse;

            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从 OpenRouter API 收到空响应");
            }

            responseContent = responseData.choices[0].message.content;
            thoughts = responseData.choices[0].message.reasoning || "无推理思考";
          } else if (config.apiProvider === "zhipu") {
            // 记录 API 请求
            requestLogFilename = logHelper.logApiRequest(
                "zhipu",
                "生成解决方案",
                config.solutionModel || "GLM-4.5-Flash",
                {
                  model: config.solutionModel || "GLM-4.5-Flash",
                  messages: messages
                }
            );

            const response = await axios.default.post(
              `${this.zhipuBaseUrl}/chat/completions`,
              {
                model: config.solutionModel || "GLM-4.5-Flash",
                messages: messages,
                temperature: 0.7,
                thinking: {
                  type: config.zhipuThinkingEnabled ? "enabled" : "disabled"
                }
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.zhipuApiKey}`,
                  "Content-Type": "application/json"
                },
                signal
              }
            );

            responseData = response.data as ApiResponse;
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从智谱AI收到空响应");
            }

            const rawContent = responseData.choices[0].message.content;
            // 解析智谱AI深度思考模型的响应内容
            const parsed = this.parseZhipuThinkingContent(rawContent);
            responseContent = parsed.actualContent;
            thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || parsed.thoughts;
          } else if (config.apiProvider === "custom") {
            // 记录 API 请求
            requestLogFilename = logHelper.logApiRequest(
                "custom",
                "生成解决方案",
                config.solutionModel || "gpt-4-turbo",
                {
                  model: config.solutionModel || "gpt-4-turbo",
                  messages: messages
                }
            );

            const response = await axios.default.post(
              `${this.customBaseUrl}/chat/completions`,
              {
                model: config.solutionModel || "gpt-4-turbo",
                messages: messages,
                temperature: 0.7
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.customApiKey}`,
                  "Content-Type": "application/json"
                },
                signal
              }
            );

            responseData = response.data as ApiResponse;
            thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || "无推理思考";
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从自定义API收到空响应");
            }

            responseContent = responseData.choices[0].message.content;
          } else {
            throw new Error("不支持的API提供商");
          }

          const formattedResponse = {
            code: responseContent,
            thoughts: thoughts,
            time_complexity: '',
            space_complexity: ''
          };

          // 记录 API 响应和解析结果
          logHelper.logApiResponse(requestLogFilename, responseData, formattedResponse);
          return { success: true, data: formattedResponse };
        } catch (error: any) {
          logHelper.logError(config.apiProvider, "生成解决方案", error);

          if (error.response) {
            if (error.response.status === 401) {
              return {
                success: false,
                error: `${config.apiProvider} API 密钥无效。请检查您的设置。`
              };
            } else if (error.response.status === 402) {
              return {
                success: false,
                error: `${config.apiProvider} API 您的账户或 API 密钥的余额不足，请充值后再重试。`
              };
            } else if (error.response.status === 429) {
              return {
                success: false,
                error: `${config.apiProvider} API 对${config.solutionModel}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`
              };
            } else if (error.response.status === 404) {
              return {
                success: false,
                error: `${config.apiProvider} API平台 可能已将${config.solutionModel}模型下架，没有可用的端点了。请换模型。`
              };
            }
          }

          return {
            success: false,
            error: error.message || `通过 ${config.apiProvider} API 生成解决方案失败。请检查您的 API 密钥或稍后再试。`
          };
        }
      }

    } catch (error: any) {
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      // Handle API errors specifically
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if (error?.response?.status === 500) {
        return {
          success: false,
          error: "API server error. Please try again later."
        };
      }

      console.error("API Error Details:", error);
      return {
        success: false,
        error: error.message || "Failed to process screenshots. Please try again."
      };
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();
      let debugContent = "";
      let thoughts = "无推理思考";

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        });
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // Support all three API providers
      if (config.apiProvider === "openrouter") {
        if (!this.openrouterApiKey) {
          return {
            success: false,
            error: "OpenRouter API 密钥未配置。请检查您的设置。"
          };
        }
      } else if (config.apiProvider === "zhipu") {
        if (!this.zhipuApiKey) {
          return {
            success: false,
            error: "智谱AI API密钥未配置。请检查您的设置。"
          };
        }
      } else if (config.apiProvider === "custom") {
        // 验证Ultra版本权限
        if (!this.deps.activationHelper) {
          return {
            success: false,
            error: "激活系统未初始化。"
          };
        }

        const isUltra = await this.deps.activationHelper.isUltraVersion();
        if (!isUltra) {
          return {
            success: false,
            error: "自定义API功能需要Ultra版本，请升级后使用。"
          };
        }

        if (!this.customApiKey || !this.customBaseUrl) {
          return {
            success: false,
            error: "自定义API配置不完整。请检查您的设置。"
          };
        }
      } else {
        return {
          success: false,
          error: "不支持的API提供商。"
        };
      }
        
        try {
          // 根据语言设置选择合适的调试提示词
          let systemPrompt = "";
          let userPrompt = "";

          // 选择对应语言的提示词
          if (config.promptLanguage === "english") {
            // 英文调试提示词
            systemPrompt = "You are a professional programming assistant helping to debug and improve solutions. Please analyze screenshots containing error messages, incorrect outputs, or test cases and provide detailed debugging help. Please respond in English.";

            userPrompt = `I'm solving this programming problem: "${problemInfo.problem_statement}" in ${language}. I need help debugging or improving my solution. Please analyze these screenshots and provide:

1. Issues Identified: List each issue as a bullet point with clear explanation
2. Specific Improvements and Corrections: List specific code changes needed
3. Optimizations: List any performance optimizations if applicable
4. Explanation of Changes: Provide a clear explanation of why the changes are needed
5. Key Points: Summary of the most important takeaways

If including code examples, use proper markdown code blocks with language specification (e.g. \`\`\`${language}).`;
          } else {
            // 中文调试提示词（默认）
            systemPrompt = "你是一个专业的编程助手，帮助调试和改进解决方案。请分析截图中的代码和错误，提供详细的调试帮助。请用中文回复。";

            userPrompt = `
你是一个专业的编程助手，帮助调试和改进解决方案。请分析这些包含错误消息、不正确输出或测试用例的截图，并提供详细的调试帮助。

我正在解决这个编程问题: "${problemInfo.problem_statement}"，使用 ${language} 语言。我需要帮助调试或改进我的解决方案。

你的回复必须遵循以下确切结构，使用这些章节标题:
### 已识别的问题
- 以项目符号形式列出每个问题，并提供清晰的解释

### 具体改进和更正
- 以项目符号形式列出需要的具体代码更改

### 优化建议
- 列出任何适用的性能优化建议

### 所需更改的解释
在此提供为什么需要这些更改的清晰解释

### 关键点总结
- 总结最重要的要点，使用项目符号

如果包含代码示例，请使用适当的 markdown 代码块并指定语言（例如 \`\`\`${language}）。
`;
          }

          const messages = [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: userPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image_url",
                  image_url: { url: `data:image/png;base64,${data}` }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: `使用 ${config.apiProvider} 分析代码并生成调试反馈...`,
              progress: 60
            });
          }

          let responseData: any;
          let requestLogFilename: string;

          if (config.apiProvider === "openrouter") {
            // 记录调试请求信息
            requestLogFilename = logHelper.logApiRequest(
              "openrouter",
              "调试分析",
              config.debuggingModel || "mistralai/mistral-small-3.2-24b-instruct:free",
              {
                model: config.debuggingModel || "mistralai/mistral-small-3.2-24b-instruct:free",
                messages: messages
              }
            );

            const response = await axios.default.post(
              "https://openrouter.ai/api/v1/chat/completions",
              {
                model: config.debuggingModel || "mistralai/mistral-small-3.2-24b-instruct:free",
                messages: messages,
                temperature: 0.5
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.openrouterApiKey}`,
                  "Content-Type": "application/json",
                  "HTTP-Referer": "https://github.com"
                },
                signal
              }
            );

            responseData = response.data as OpenRouterResponse;

            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从 OpenRouter API 收到空响应");
            }

            debugContent = responseData.choices[0].message.content;
            thoughts = responseData.choices[0].message.reasoning || "无推理思考";
          } else if (config.apiProvider === "zhipu") {
            // 记录调试请求信息
            requestLogFilename = logHelper.logApiRequest(
              "zhipu",
              "调试分析",
              config.debuggingModel || "glm-4v-plus-0111",
              {
                model: config.debuggingModel || "glm-4v-plus-0111",
                messages: messages
              }
            );

            const response = await axios.default.post(
              `${this.zhipuBaseUrl}/chat/completions`,
              {
                model: config.debuggingModel || "glm-4v-plus-0111",
                messages: messages,
                temperature: 0.5
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.zhipuApiKey}`,
                  "Content-Type": "application/json"
                },
                signal
              }
            );

            responseData = response.data as ApiResponse;
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从智谱AI收到空响应");
            }

            const rawDebugContent = responseData.choices[0].message.content;
            // 解析智谱AI深度思考模型的响应内容
            const parsed = this.parseZhipuThinkingContent(rawDebugContent);
            debugContent = parsed.actualContent;
            thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || parsed.thoughts;
          } else if (config.apiProvider === "custom") {
            // 记录调试请求信息
            requestLogFilename = logHelper.logApiRequest(
              "custom",
              "调试分析",
              config.debuggingModel || "gpt-4-vision-preview",
              {
                model: config.debuggingModel || "gpt-4-vision-preview",
                messages: messages
              }
            );

            const response = await axios.default.post(
              `${this.customBaseUrl}/chat/completions`,
              {
                model: config.debuggingModel || "gpt-4-vision-preview",
                messages: messages,
                temperature: 0.5
              },
              {
                headers: {
                  "Authorization": `Bearer ${this.customApiKey}`,
                  "Content-Type": "application/json"
                },
                signal
              }
            );

            responseData = response.data as ApiResponse;
            if (!responseData.choices || responseData.choices.length === 0) {
              throw new Error("从自定义API收到空响应");
            }

            debugContent = responseData.choices[0].message.content;
            thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || "无推理思考";
          } else {
            throw new Error("不支持的API提供商");
          }

          let formattedDebugContent = debugContent;

          const parsedResult = {
            code: "",
            debug_analysis: formattedDebugContent,
            thoughts: thoughts,
            time_complexity: "N/A - 调试模式",
            space_complexity: "N/A - 调试模式"
          };

          // 记录 API 响应和解析结果
          logHelper.logApiResponse(requestLogFilename, responseData, parsedResult);
          return { success: true, data: parsedResult };
        } catch (error: any) {
          logHelper.logError(config.apiProvider, "调试分析", error);

          if (error.response) {
            if (error.response.status === 401) {
              return {
                success: false,
                error: `${config.apiProvider} API 密钥无效。请检查您的设置。`
              };
            } else if (error.response.status === 402) {
              return {
                success: false,
                error: `${config.apiProvider} API 您的账户或 API 密钥的余额不足，请充值后再重试。`
              };
            } else if (error.response.status === 429) {
              return {
                success: false,
                error: `${config.apiProvider} API 对${config.debuggingModel}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`
              };
            } else if (error.response.status === 404) {
              return {
                success: false,
                error: `${config.apiProvider} API平台 可能已将${config.debuggingModel}模型下架，没有可用的端点了。请换模型。`
              };
            }
          }

          return {
            success: false,
            error: error.message || `通过 ${config.apiProvider} API 处理调试请求失败。请检查您的 API 密钥或稍后再试。`
          };
        }
    } catch (error: any) {
      // 记录错误
      logHelper.logError("debug", "调试分析", error);

      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "处理被用户取消。"
        };
      }
      
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "API 密钥无效。请检查您的设置。"
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "API 速率限制已超出或余额不足。请稍后再试。"
        };
      }
      
      return {
        success: false,
        error: error.message || "处理调试请求失败。请检查您的 API 密钥或稍后再试。"
      };
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    this.deps.setHasDebugged(false)

    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }

  // 在ProcessingHelper类中添加streamChat方法
  public async streamChat(
    messages: any[],
    signal: AbortSignal,
    eventSourceId: string,
    mainWindow: BrowserWindow | null
  ): Promise<void> {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      
      // 记录基本信息，减少冗余日志
      console.log("语音流式请求开始 - 模型:", config.solutionModel);
      
      // 确保API客户端已配置
      if (config.apiProvider === "openrouter") {
        if (!this.openrouterApiKey) {
          // 尝试重新初始化
          this.initializeAIClient();

          if (!this.openrouterApiKey) {
            if (mainWindow) {
              mainWindow.webContents.send(`${eventSourceId}-error`, "OpenRouter API 密钥未配置，请在设置中配置");
              mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.API_KEY_INVALID);
            }
            throw new Error("OpenRouter API 密钥未配置");
          }
        }
      } else if (config.apiProvider === "zhipu") {
        if (!this.zhipuApiKey) {
          // 尝试重新初始化
          this.initializeAIClient();

          if (!this.zhipuApiKey) {
            if (mainWindow) {
              mainWindow.webContents.send(`${eventSourceId}-error`, "智谱AI API密钥未配置，请在设置中配置");
              mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.API_KEY_INVALID);
            }
            throw new Error("智谱AI API密钥未配置");
          }
        }
      } else if (config.apiProvider === "custom") {
        // 验证Ultra版本权限
        if (!this.deps.activationHelper) {
          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, "激活系统未初始化");
          }
          throw new Error("激活系统未初始化");
        }

        const isUltra = await this.deps.activationHelper.isUltraVersion();
        if (!isUltra) {
          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, "自定义API功能需要Ultra版本，请升级后使用");
          }
          throw new Error("自定义API功能需要Ultra版本，请升级后使用");
        }

        if (!this.customApiKey || !this.customBaseUrl) {
          // 尝试重新初始化
          this.initializeAIClient();

          if (!this.customApiKey || !this.customBaseUrl) {
            if (mainWindow) {
              mainWindow.webContents.send(`${eventSourceId}-error`, "自定义API配置不完整，请在设置中配置");
              mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.API_KEY_INVALID);
            }
            throw new Error("自定义API配置不完整");
          }
        }
      } else {
        if (mainWindow) {
          mainWindow.webContents.send(`${eventSourceId}-error`, "不支持的API提供商");
        }
        throw new Error("不支持的API提供商");
      }
      
      // 转换消息格式为OpenRouter API所需格式
      const formattedMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));
      
      // 获取用户权限信息并使用统一的提示词选择逻辑
      const { hasUltraPermission } = await this.getUserPermissions();

      const systemPrompt = customPromptsHelper.getFinalPrompt(
        ['voiceStreaming', 'solution', 'systemPrompt'],
        config.promptLanguage === 'english' ? 'english' : 'chinese',
        { language },
        hasUltraPermission
      );
      const systemMessage = {
        role: "system",
        content: systemPrompt
      };
      
      const apiMessages = [systemMessage, ...formattedMessages];
      
      let requestLogFilename: string;

      if (config.apiProvider === "openrouter") {
        // OpenRouter 流式处理
        const apiRequestBody = {
          model: config.solutionModel || "z-ai/glm-4.5-air:free",
          messages: apiMessages,
          stream: true,  // 启用流式传输
          temperature: 0.7,
          reasoning: {
            enabled: config.openrouterReasoningEnabled || false
          }
        };

        // 使用logHelper记录API请求
        requestLogFilename = logHelper.logApiRequest(
          "openrouter",
          "语音流式对话",
          config.solutionModel || "z-ai/glm-4.5-air:free",
          apiRequestBody
        );

        try {
          // 使用axios替代fetch，与其他模型请求保持一致
          const axiosConfig = {
            method: 'post',
            url: 'https://openrouter.ai/api/v1/chat/completions',
            headers: {
              'Authorization': `Bearer ${this.openrouterApiKey}`,
              'Content-Type': 'application/json',
              'HTTP-Referer': 'https://github.com'
            },
            data: {
              ...apiRequestBody
            },
            responseType: 'stream' as const,
            signal: signal
          };

          // 发起请求
          console.log(`发送OpenRouter流式请求: ${eventSourceId}`);
          const response = await axios.default.request(axiosConfig);
          console.log(`收到OpenRouter响应: ${eventSourceId}, 状态: ${response.status}`);

          if (response.status !== 200) {
            console.error(`OpenRouter API请求失败: ${response.status}`);
            if (mainWindow) {
              mainWindow.webContents.send(`${eventSourceId}-error`, `API请求失败: ${response.status}`);
            }
            throw new Error(`API请求失败: ${response.status}`);
          }

          await this.handleOpenRouterStream(response, eventSourceId, mainWindow, requestLogFilename);
        } catch (error: any) {
          console.error(`OpenRouter流式请求错误: ${eventSourceId}`, error);

          // 处理HTTP状态码错误，提供友好的错误信息
          let friendlyErrorMessage = "";
          if (error.response) {
            const status = error.response.status;
            const modelName = config.solutionModel || "z-ai/glm-4.5-air:free";

            if (status === 401) {
              friendlyErrorMessage = "OpenRouter API 密钥无效。请检查您的设置。";
            } else if (status === 402) {
              friendlyErrorMessage = `OpenRouter API 您的账户或 API 密钥的余额不足，请充值后再重试。`;
            } else if (status === 429) {
              friendlyErrorMessage = `OpenRouter API 对${modelName}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`;
            } else if (status === 404) {
              friendlyErrorMessage = `OpenRouter API平台 可能已将${modelName}模型下架，没有可用的端点了。请换模型。`;
            } else if (status === 500) {
              friendlyErrorMessage = "OpenRouter API 服务器内部错误。请稍后重试。";
            } else if (status === 502 || status === 503 || status === 504) {
              friendlyErrorMessage = "OpenRouter API 服务暂时不可用。请稍后重试。";
            } else {
              friendlyErrorMessage = `OpenRouter API 请求失败 (状态码: ${status})。请稍后重试。`;
            }
          } else {
            friendlyErrorMessage = error.message || "OpenRouter流式请求失败";
          }

          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, friendlyErrorMessage);
          }
          throw new Error(friendlyErrorMessage);
        }
      } else if (config.apiProvider === "zhipu") {
        // 智谱AI流式处理
        const apiRequestBody = {
          model: config.solutionModel || "GLM-4.5-Flash",
          messages: apiMessages,
          stream: true,  // 启用流式传输
          temperature: 0.7,
          thinking: {
            type: config.zhipuThinkingEnabled ? "enabled" : "disabled"
          }
        };

        requestLogFilename = logHelper.logApiRequest(
          "zhipu",
          "语音流式对话",
          config.solutionModel || "GLM-4.5-Flash",
          apiRequestBody
        );

        try {
          console.log(`发送智谱AI流式请求: ${eventSourceId}`);

          const axiosConfig = {
            method: 'post',
            url: `${this.zhipuBaseUrl}/chat/completions`,
            headers: {
              'Authorization': `Bearer ${this.zhipuApiKey}`,
              'Content-Type': 'application/json'
            },
            data: apiRequestBody,
            responseType: 'stream' as const,
            signal: signal
          };

          const response = await axios.default.request(axiosConfig);
          console.log(`收到智谱AI响应: ${eventSourceId}, 状态: ${response.status}`);

          if (response.status !== 200) {
            console.error(`智谱AI API请求失败: ${response.status}`);
            if (mainWindow) {
              mainWindow.webContents.send(`${eventSourceId}-error`, `API请求失败: ${response.status}`);
            }
            throw new Error(`API请求失败: ${response.status}`);
          }

          await this.handleZhipuStream(response, eventSourceId, mainWindow, requestLogFilename);
        } catch (error: any) {
          console.error(`智谱AI流式请求错误: ${eventSourceId}`, error);

          // 处理HTTP状态码错误，提供友好的错误信息
          let friendlyErrorMessage = "";
          if (error.response) {
            const status = error.response.status;
            const modelName = config.solutionModel || "GLM-4.5-Flash";

            if (status === 401) {
              friendlyErrorMessage = "智谱AI API密钥无效。请检查您的设置。";
            } else if (status === 402) {
              friendlyErrorMessage = `智谱AI API 您的账户或 API 密钥的余额不足，请充值后再重试。`;
            } else if (status === 429) {
              friendlyErrorMessage = `智谱AI API 对${modelName}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`;
            } else if (status === 404) {
              friendlyErrorMessage = `智谱AI API平台 可能已将${modelName}模型下架，没有可用的端点了。请换模型。`;
            } else if (status === 500) {
              friendlyErrorMessage = "智谱AI API 服务器内部错误。请稍后重试。";
            } else if (status === 502 || status === 503 || status === 504) {
              friendlyErrorMessage = "智谱AI API 服务暂时不可用。请稍后重试。";
            } else {
              friendlyErrorMessage = `智谱AI API 请求失败 (状态码: ${status})。请稍后重试。`;
            }
          } else {
            friendlyErrorMessage = error.message || "智谱AI流式请求失败";
          }

          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, friendlyErrorMessage);
          }
          throw new Error(friendlyErrorMessage);
        }
      } else if (config.apiProvider === "custom") {
        // 自定义API流式处理
        const apiRequestBody = {
          model: config.solutionModel || "gpt-4-turbo",
          messages: apiMessages,
          stream: true,  // 启用流式传输
          temperature: 0.7
        };

        requestLogFilename = logHelper.logApiRequest(
          "custom",
          "语音流式对话",
          config.solutionModel || "gpt-4-turbo",
          apiRequestBody
        );

        try {
          console.log(`发送自定义API流式请求: ${eventSourceId}`);

          const axiosConfig = {
            method: 'post',
            url: `${this.customBaseUrl}/chat/completions`,
            headers: {
              'Authorization': `Bearer ${this.customApiKey}`,
              'Content-Type': 'application/json'
            },
            data: apiRequestBody,
            responseType: 'stream' as const,
            signal: signal
          };

          const response = await axios.default.request(axiosConfig);
          console.log(`收到自定义API响应: ${eventSourceId}, 状态: ${response.status}`);

          if (response.status !== 200) {
            console.error(`自定义API请求失败: ${response.status}`);
            if (mainWindow) {
              mainWindow.webContents.send(`${eventSourceId}-error`, `API请求失败: ${response.status}`);
            }
            throw new Error(`API请求失败: ${response.status}`);
          }

          await this.handleCustomAPIStream(response, eventSourceId, mainWindow, requestLogFilename);
        } catch (error: any) {
          console.error(`自定义API流式请求错误: ${eventSourceId}`, error);

          // 处理HTTP状态码错误，提供友好的错误信息
          let friendlyErrorMessage = "";
          if (error.response) {
            const status = error.response.status;
            const modelName = config.solutionModel || "gpt-4-turbo";

            if (status === 401) {
              friendlyErrorMessage = "自定义API密钥无效。请检查您的设置。";
            } else if (status === 402) {
              friendlyErrorMessage = `自定义API 您的账户或 API 密钥的余额不足，请充值后再重试。`;
            } else if (status === 429) {
              friendlyErrorMessage = `自定义API 对${modelName}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`;
            } else if (status === 404) {
              friendlyErrorMessage = `自定义API平台 可能已将${modelName}模型下架，没有可用的端点了。请换模型。`;
            } else if (status === 500) {
              friendlyErrorMessage = "自定义API 服务器内部错误。请稍后重试。";
            } else if (status === 502 || status === 503 || status === 504) {
              friendlyErrorMessage = "自定义API 服务暂时不可用。请稍后重试。";
            } else {
              friendlyErrorMessage = `自定义API 请求失败 (状态码: ${status})。请稍后重试。`;
            }
          } else {
            friendlyErrorMessage = error.message || "自定义API流式请求失败";
          }

          if (mainWindow) {
            mainWindow.webContents.send(`${eventSourceId}-error`, friendlyErrorMessage);
          }
          throw new Error(friendlyErrorMessage);
        }
      } else {
        throw new Error("不支持的API提供商");
      }
    } catch (error: any) {
      console.error(`流式聊天错误: ${eventSourceId}`, error);
      if (mainWindow) {
        mainWindow.webContents.send(`${eventSourceId}-error`, error.message || "流式聊天失败");
      }
      throw error;
    }
  }

  private async handleOpenRouterStream(
    response: any,
    eventSourceId: string,
    mainWindow: BrowserWindow | null,
    requestLogFilename: string
  ): Promise<void> {
    // 处理流式响应
    let buffer = '';
    let fullResponse = '';
    let fullReasoning = '';

    // 设置响应数据处理
    response.data.on('data', (chunk: Buffer) => {
      const chunkText = chunk.toString('utf-8');

      buffer += chunkText;

      // 处理可能的多行数据
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";  // 保留最后一行可能不完整的数据

      for (const line of lines) {
        if (!line.trim() || line.trim() === "data: [DONE]") {
          continue;
        }

        try {
          // 从行中提取JSON
          const match = line.match(/data: (.+)/);
          if (!match) continue;

          const json = JSON.parse(match[1]);

          // 提取增量内容和推理内容
          if (json.choices && json.choices[0].delta) {
            const delta = json.choices[0].delta;

            // 处理回答内容
            if (delta.content) {
              const token = delta.content;
              fullResponse += token; // 累积完整响应

              // token发送到渲染进程
              if (mainWindow && !mainWindow.isDestroyed()) {
                try {
                  mainWindow.webContents.send(`${eventSourceId}-message`, { type: 'content', token });
                } catch (err) {
                  console.error(`发送content token到前端失败: ${err}`);
                }
              }
            }

            // 处理推理内容
            if (delta.reasoning) {
              const reasoningToken = delta.reasoning;
              fullReasoning += reasoningToken; // 累积完整推理

              // 推理token发送到渲染进程
              if (mainWindow && !mainWindow.isDestroyed()) {
                try {
                  mainWindow.webContents.send(`${eventSourceId}-message`, { type: 'reasoning', token: reasoningToken });
                } catch (err) {
                  console.error(`发送reasoning token到前端失败: ${err}`);
                }
              }
            }
          }
        } catch (e) {
          console.warn(`解析流式响应行失败: ${line.substring(0, 100)}...`);
        }
      }
    });

    // 监听流结束事件
    response.data.on('end', () => {
      // 使用logHelper记录完整的API响应
      logHelper.logApiResponse(requestLogFilename, "语音流式对话", {
        success: true,
        reasoning: fullReasoning,
        content: fullResponse
      });

      if (mainWindow && !mainWindow.isDestroyed()) {
        // 确保发送完成事件
        try {
          console.log(`发送done事件, ID: ${eventSourceId}`);
          mainWindow.webContents.send(`${eventSourceId}-done`);
        } catch (err) {
          console.error(`发送done事件失败: ${err}`);
        }
      }
    });

    // 监听错误事件
    response.data.on('error', (err: Error) => {
      console.error(`流式响应错误, ID: ${eventSourceId}, 错误: ${err.message}`);

      // 记录错误
      logHelper.logError("openrouter", "语音流式对话", {
        error: err.message,
        eventSourceId
      });

      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(`${eventSourceId}-error`, `流式读取错误: ${err.message}`);
      }
    });
  }

  private async handleZhipuStream(
    response: any,
    eventSourceId: string,
    mainWindow: BrowserWindow | null,
    requestLogFilename: string
  ): Promise<void> {
    // 处理智谱AI流式响应
    let buffer = '';
    let fullResponse = '';
    let fullReasoning = '';

    // 设置响应数据处理
    response.data.on('data', (chunk: Buffer) => {
      const chunkText = chunk.toString('utf-8');

      buffer += chunkText;

      // 处理可能的多行数据
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";  // 保留最后一行可能不完整的数据

      for (const line of lines) {
        if (!line.trim() || line.trim() === "data: [DONE]") {
          continue;
        }

        try {
          // 从行中提取JSON
          const match = line.match(/data: (.+)/);
          if (!match) continue;

          const json = JSON.parse(match[1]);

          // 提取增量内容
          if (json.choices && json.choices[0].delta) {
            const delta = json.choices[0].delta;

            // 处理回答内容
            if (delta.content) {
              const token = delta.content;
              fullResponse += token; // 累积完整响应

              // token发送到渲染进程
              if (mainWindow && !mainWindow.isDestroyed()) {
                try {
                  mainWindow.webContents.send(`${eventSourceId}-message`, { type: 'content', token });
                } catch (err) {
                  console.error(`发送content token到前端失败: ${err}`);
                }
              }
            }

            // 智谱AI可能有推理内容字段，根据实际API响应格式调整
            if (delta.reasoning_content) {
              const reasoningToken = delta.reasoning_content;
              fullReasoning += reasoningToken; // 累积完整推理

              // 推理token发送到渲染进程
              if (mainWindow && !mainWindow.isDestroyed()) {
                try {
                  mainWindow.webContents.send(`${eventSourceId}-message`, { type: 'reasoning', token: reasoningToken });
                } catch (err) {
                  console.error(`发送reasoning token到前端失败: ${err}`);
                }
              }
            }
          }
        } catch (e) {
          console.warn(`解析智谱AI流式响应行失败: ${line.substring(0, 100)}...`);
        }
      }
    });

    // 监听流结束事件
    response.data.on('end', () => {
      // 使用logHelper记录完整的API响应
      logHelper.logApiResponse(requestLogFilename, "智谱AI语音流式对话", {
        success: true,
        reasoning: fullReasoning,
        content: fullResponse
      });

      if (mainWindow && !mainWindow.isDestroyed()) {
        // 确保发送完成事件
        try {
          console.log(`发送done事件, ID: ${eventSourceId}`);
          mainWindow.webContents.send(`${eventSourceId}-done`);
        } catch (err) {
          console.error(`发送done事件失败: ${err}`);
        }
      }
    });

    // 监听错误事件
    response.data.on('error', (err: Error) => {
      console.error(`智谱AI流式响应错误, ID: ${eventSourceId}, 错误: ${err.message}`);

      // 记录错误
      logHelper.logError("zhipu", "语音流式对话", {
        error: err.message,
        eventSourceId
      });

      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(`${eventSourceId}-error`, `流式读取错误: ${err.message}`);
      }
    });
  }

  private async handleCustomAPIStream(
    response: any,
    eventSourceId: string,
    mainWindow: BrowserWindow | null,
    requestLogFilename: string
  ): Promise<void> {
    // 处理自定义API流式响应
    let buffer = '';
    let fullResponse = '';
    let fullReasoning = '';

    // 设置响应数据处理
    response.data.on('data', (chunk: Buffer) => {
      const chunkText = chunk.toString('utf-8');

      buffer += chunkText;

      // 处理可能的多行数据
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";  // 保留最后一行可能不完整的数据

      for (const line of lines) {
        if (!line.trim() || line.trim() === "data: [DONE]") {
          continue;
        }

        try {
          // 从行中提取JSON
          const match = line.match(/data: (.+)/);
          if (!match) continue;

          const json = JSON.parse(match[1]);

          // 提取增量内容
          if (json.choices && json.choices[0].delta) {
            const delta = json.choices[0].delta;

            // 处理回答内容
            if (delta.content) {
              const token = delta.content;
              fullResponse += token; // 累积完整响应

              // token发送到渲染进程
              if (mainWindow && !mainWindow.isDestroyed()) {
                try {
                  mainWindow.webContents.send(`${eventSourceId}-message`, { type: 'content', token });
                } catch (err) {
                  console.error(`发送content token到前端失败: ${err}`);
                }
              }
            }

            // 自定义API可能有推理内容字段，根据实际API响应格式调整
            if (delta.reasoning || delta.reasoning_content) {
              const reasoningToken = delta.reasoning || delta.reasoning_content;
              fullReasoning += reasoningToken; // 累积完整推理

              // 推理token发送到渲染进程
              if (mainWindow && !mainWindow.isDestroyed()) {
                try {
                  mainWindow.webContents.send(`${eventSourceId}-message`, { type: 'reasoning', token: reasoningToken });
                } catch (err) {
                  console.error(`发送reasoning token到前端失败: ${err}`);
                }
              }
            }
          }
        } catch (e) {
          console.warn(`解析自定义API流式响应行失败: ${line.substring(0, 100)}...`);
        }
      }
    });

    // 监听流结束事件
    response.data.on('end', () => {
      // 使用logHelper记录完整的API响应
      logHelper.logApiResponse(requestLogFilename, "自定义API语音流式对话", {
        success: true,
        reasoning: fullReasoning,
        content: fullResponse
      });

      if (mainWindow && !mainWindow.isDestroyed()) {
        // 确保发送完成事件
        try {
          console.log(`发送done事件, ID: ${eventSourceId}`);
          mainWindow.webContents.send(`${eventSourceId}-done`);
        } catch (err) {
          console.error(`发送done事件失败: ${err}`);
        }
      }
    });

    // 监听错误事件
    response.data.on('error', (err: Error) => {
      console.error(`自定义API流式响应错误, ID: ${eventSourceId}, 错误: ${err.message}`);

      // 记录错误
      logHelper.logError("custom", "语音流式对话", {
        error: err.message,
        eventSourceId
      });

      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send(`${eventSourceId}-error`, `流式读取错误: ${err.message}`);
      }
    });
  }

  // 新增：直接解答模式的helper方法
  private async processDirectAnswerHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // 准备图片数据
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // 发送模拟的问题提取事件，让UI显示正确的加载状态
      if (mainWindow) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, {
          problem_statement: "正在使用直接解答模式分析图片..."
        });

        // 更新进度状态
        mainWindow.webContents.send("processing-status", {
          message: "正在分析图片并生成答案...",
          progress: 20
        });
      }

      // 获取用户权限信息并使用统一的提示词选择逻辑
      const { hasUltraPermission } = await this.getUserPermissions();

      const directAnswerPrompt = customPromptsHelper.getFinalPrompt(
        ['directAnswer', 'extraction', 'userPrompt'],
        config.promptLanguage === 'english' ? 'english' : 'chinese',
        { language },
        hasUltraPermission
      );

      // 构建API消息
      const messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: directAnswerPrompt
            },
            ...imageDataList.map(imageData => ({
              type: "image_url",
              image_url: {
                url: imageData
              }
            }))
          ]
        }
      ];

      let responseContent = "";
      let thoughts = "无推理思考";
      let responseData: any;
      let requestLogFilename = "";

      // 根据API提供商调用相应的API
      if (config.apiProvider === "openrouter") {
        requestLogFilename = logHelper.logApiRequest(
          "openrouter",
          "直接解答",
          config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free",
          {
            model: config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free",
            messages: messages
          }
        );

        const response = await axios.default.post(
          "https://openrouter.ai/api/v1/chat/completions",
          {
            model: config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free",
            messages: messages,
            temperature: 0.75,
            reasoning: {
              enabled: config.openrouterReasoningEnabled || false
            }
          },
          {
            headers: {
              "Authorization": `Bearer ${this.openrouterApiKey}`,
              "Content-Type": "application/json",
              "HTTP-Referer": "https://github.com"
            },
            signal
          }
        );

        responseData = response.data;
        if (!responseData.choices || responseData.choices.length === 0) {
          throw new Error("从 OpenRouter API 收到空响应");
        }
        responseContent = responseData.choices[0].message.content;
        thoughts = responseData.choices[0].message.reasoning || "无推理思考";
      } else if (config.apiProvider === "zhipu") {
        requestLogFilename = logHelper.logApiRequest(
          "zhipu",
          "直接解答",
          config.extractionModel || "glm-4v-plus-0111",
          {
            model: config.extractionModel || "glm-4v-plus-0111",
            messages: messages
          }
        );

        const response = await axios.default.post(
          `${this.zhipuBaseUrl}/chat/completions`,
          {
            model: config.extractionModel || "glm-4v-plus-0111",
            messages: messages,
            temperature: 0.75,
            thinking: {
              type: config.zhipuThinkingEnabled ? "enabled" : "disabled"
            }
          },
          {
            headers: {
              "Authorization": `Bearer ${this.zhipuApiKey}`,
              "Content-Type": "application/json"
            },
            signal
          }
        );

        responseData = response.data;
        if (!responseData.choices || responseData.choices.length === 0) {
          throw new Error("从智谱AI收到空响应");
        }
        const rawContent = responseData.choices[0].message.content;
        // 解析智谱AI深度思考模型的响应内容
        const parsed = this.parseZhipuThinkingContent(rawContent);
        responseContent = parsed.actualContent;
        thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || parsed.thoughts;
      } else if (config.apiProvider === "custom") {
        requestLogFilename = logHelper.logApiRequest(
          "custom",
          "直接解答",
          config.extractionModel || "gpt-4-vision-preview",
          {
            model: config.extractionModel || "gpt-4-vision-preview",
            messages: messages
          }
        );

        const response = await axios.default.post(
          `${this.customBaseUrl}/chat/completions`,
          {
            model: config.extractionModel || "gpt-4-vision-preview",
            messages: messages,
            temperature: 0.75
          },
          {
            headers: {
              "Authorization": `Bearer ${this.customApiKey}`,
              "Content-Type": "application/json"
            },
            signal
          }
        );

        responseData = response.data;
        if (!responseData.choices || responseData.choices.length === 0) {
          throw new Error("从自定义API收到空响应");
        }
        responseContent = responseData.choices[0].message.content;
        thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || "无推理思考";
      } else {
        throw new Error("不支持的API提供商");
      }

      // 更新进度状态
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "答案生成完成",
          progress: 100
        });
      }

      // 构建响应格式
      const formattedResponse = {
        code: responseContent,
        thoughts: thoughts,
        time_complexity: '',
        space_complexity: ''
      };

      // 记录API响应
      logHelper.logApiResponse(requestLogFilename, responseData, formattedResponse);

      // 发送成功事件
      if (mainWindow) {
        // 发送解决方案成功事件
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          formattedResponse
        );

        // 设置视图为solutions（如果当前在queue视图）
        const currentView = this.deps.getView();
        if (currentView === "queue") {
          this.deps.setView("solutions");
        }
      }

    } catch (error: any) {
      console.error("直接解答处理失败:", error);

      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        let errorMessage = "处理失败";

        if (error.response) {
          if (error.response.status === 401) {
            errorMessage = `${config.apiProvider} API 密钥无效。请检查您的设置。`;
          } else if (error.response.status === 402) {
            errorMessage = `${config.apiProvider} API 密钥无效。您的账户或 API 密钥的余额不足，请充值后再重试。`;
          } else if (error.response.status === 429) {
            errorMessage = `${config.apiProvider} API 对${config.extractionModel}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`;
          } else if (error.response.status === 404) {
            errorMessage = `${config.apiProvider} API平台 可能已将${config.extractionModel}模型下架，没有可用的端点了。请换模型。`;
          } else if (error.response.status >= 500) {
            errorMessage = error.message || `通过 ${config.apiProvider} API 处理请求失败。请检查您的 API 密钥或稍后再试。`;
          }
        }

        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROCESSING_ERROR,
          errorMessage
        );
      }

      throw error;
    }
  }

  // 新增：直接调试模式的helper方法
  private async processDirectDebugHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // 准备图片数据
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // 更新进度状态
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "正在分析所有截图进行调试...",
          progress: 30
        });
      }

      // 获取用户权限信息并使用统一的提示词选择逻辑
      const { hasUltraPermission } = await this.getUserPermissions();

      const directDebugPrompt = customPromptsHelper.getFinalPrompt(
        ['directAnswer', 'debugging', 'userPrompt'],
        config.promptLanguage === 'english' ? 'english' : 'chinese',
        { language },
        hasUltraPermission
      );

      // 构建API消息
      const messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: directDebugPrompt
            },
            ...imageDataList.map(imageData => ({
              type: "image_url",
              image_url: {
                url: imageData
              }
            }))
          ]
        }
      ];

      let debugContent = "";
      let thoughts = "无推理思考";
      let responseData: any;
      let requestLogFilename = "";

      // 根据API提供商调用相应的API
      if (config.apiProvider === "openrouter") {
        requestLogFilename = logHelper.logApiRequest(
          "openrouter",
          "直接调试",
          config.debuggingModel || "qwen/qwen2.5-vl-32b-instruct:free",
          {
            model: config.debuggingModel || "qwen/qwen2.5-vl-32b-instruct:free",
            messages: messages
          }
        );

        const response = await axios.default.post(
          "https://openrouter.ai/api/v1/chat/completions",
          {
            model: config.debuggingModel || "qwen/qwen2.5-vl-32b-instruct:free",
            messages: messages,
            temperature: 0.75,
            reasoning: {
              enabled: config.openrouterReasoningEnabled || false
            }
          },
          {
            headers: {
              "Authorization": `Bearer ${this.openrouterApiKey}`,
              "Content-Type": "application/json",
              "HTTP-Referer": "https://github.com"
            },
            signal
          }
        );

        responseData = response.data;
        if (!responseData.choices || responseData.choices.length === 0) {
          throw new Error("从 OpenRouter API 收到空响应");
        }
        debugContent = responseData.choices[0].message.content;
        thoughts = responseData.choices[0].message.reasoning || "无推理思考";
      } else if (config.apiProvider === "zhipu") {
        requestLogFilename = logHelper.logApiRequest(
          "zhipu",
          "直接调试",
          config.debuggingModel || "glm-4v-plus-0111",
          {
            model: config.debuggingModel || "glm-4v-plus-0111",
            messages: messages
          }
        );

        const response = await axios.default.post(
          `${this.zhipuBaseUrl}/chat/completions`,
          {
            model: config.debuggingModel || "glm-4v-plus-0111",
            messages: messages,
            temperature: 0.75,
            thinking: {
              type: config.zhipuThinkingEnabled ? "enabled" : "disabled"
            }
          },
          {
            headers: {
              "Authorization": `Bearer ${this.zhipuApiKey}`,
              "Content-Type": "application/json"
            },
            signal
          }
        );

        responseData = response.data;
        if (!responseData.choices || responseData.choices.length === 0) {
          throw new Error("从智谱AI收到空响应");
        }

        const rawContent = responseData.choices[0].message.content;
        const parsed = this.parseZhipuThinkingContent(rawContent);
        debugContent = parsed.actualContent;
        thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || parsed.thoughts;
      } else if (config.apiProvider === "custom") {
        requestLogFilename = logHelper.logApiRequest(
          "custom",
          "直接调试",
          config.debuggingModel || "gpt-4-vision-preview",
          {
            model: config.debuggingModel || "gpt-4-vision-preview",
            messages: messages
          }
        );

        const response = await axios.default.post(
          `${this.customBaseUrl}/chat/completions`,
          {
            model: config.debuggingModel || "gpt-4-vision-preview",
            messages: messages,
            temperature: 0.75
          },
          {
            headers: {
              "Authorization": `Bearer ${this.customApiKey}`,
              "Content-Type": "application/json"
            },
            signal
          }
        );

        responseData = response.data;
        if (!responseData.choices || responseData.choices.length === 0) {
          throw new Error("从自定义API收到空响应");
        }
        debugContent = responseData.choices[0].message.content;
        thoughts = responseData.choices[0].message.reasoning_content || responseData.choices[0].message.reasoning || "无推理思考";
      } else {
        throw new Error("不支持的API提供商");
      }

      // 更新进度状态
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "调试分析完成",
          progress: 100
        });
      }

      // 构建响应格式
      const parsedResult = {
        code: "",
        debug_analysis: debugContent,
        thoughts: thoughts,
        time_complexity: "N/A - 调试模式",
        space_complexity: "N/A - 调试模式"
      };

      // 记录API响应
      logHelper.logApiResponse(requestLogFilename, responseData, parsedResult);

      // 发送成功事件
      if (mainWindow) {
        // 发送调试成功事件
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
          parsedResult
        );

        // 设置调试状态
        this.deps.setHasDebugged(true);
      }

    } catch (error: any) {
      console.error("直接调试处理失败:", error);

      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        let errorMessage = "调试处理失败";

        if (error.response) {
          if (error.response.status === 401) {
            errorMessage = `${config.apiProvider} API 密钥无效。请检查您的设置。`;
          } else if (error.response.status === 402) {
            errorMessage = `${config.apiProvider} API 密钥无效。您的账户或 API 密钥的余额不足，请充值后再重试。`;
          } else if (error.response.status === 429) {
            errorMessage = `${config.apiProvider} API 对${config.debuggingModel}模型已暂时被上游厂商限制速率，或您的余额不足。请换模型或稍后重试。`;
          } else if (error.response.status === 404) {
            errorMessage = `${config.apiProvider} API平台 可能已将${config.debuggingModel}模型下架，没有可用的端点了。请换模型。`;
          } else if (error.response.status >= 500) {
            errorMessage = error.message || `通过 ${config.apiProvider} API 处理请求失败。请检查您的 API 密钥或稍后再试。`;
          }
        }

        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
          errorMessage
        );
      }

      throw error;
    }
  }

}
