// CheatSheetConfigHelper.ts
import fs from "node:fs"
import path from "node:path"
import { app } from "electron"

export interface CheatSheetDocument {
  id: string;
  name: string;
  type: 'markdown' | 'pdf' | 'text';
  filePath: string; // 文件路径（必需）
  size: number; // File size in bytes
  createdAt: number;
  updatedAt: number;
}

export interface CheatSheetConfig {
  documents: CheatSheetDocument[];
  localDirectories: string[]; // 配置的本地文件目录
  maxFileSize: number; // 最大文件大小限制（字节）
  currentDocumentIndex: number; // 当前查看的文档索引
}

export class CheatSheetConfigHelper {
  private configPath: string;
  private defaultConfig: CheatSheetConfig = {
    documents: [],
    localDirectories: [],
    maxFileSize: 20 * 1024 * 1024, // 20MB
    currentDocumentIndex: 0
  };

  constructor() {
    try {
      if (process.env.NODE_ENV === "development") {
        app.setPath("userData", path.join(app.getPath('appData'), 'Electron'));
      }
      this.configPath = path.join(app.getPath('userData'), 'cheatsheet_config.json');
      console.log('CheatSheet config path:', this.configPath);
    } catch (err) {
      console.warn('Could not access user data path, using fallback');
      this.configPath = path.join(process.cwd(), 'cheatsheet_config.json');
    }
    
    this.ensureConfigExists();
  }

  /**
   * 确保配置文件存在
   */
  private ensureConfigExists(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig);
      }
    } catch (err) {
      console.error("Error ensuring cheatsheet config exists:", err);
    }
  }

  /**
   * 加载配置
   */
  public loadConfig(): CheatSheetConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const config = JSON.parse(configData);
        return {
          ...this.defaultConfig,
          ...config
        };
      }
      
      this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    } catch (err) {
      console.error("Error loading cheatsheet config:", err);
      return this.defaultConfig;
    }
  }

  /**
   * 保存配置
   */
  public saveConfig(config: CheatSheetConfig): void {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (err) {
      console.error("Error saving cheatsheet config:", err);
    }
  }

  /**
   * 检查文档是否已存在（基于文件路径）
   */
  public documentExists(filePath?: string, name?: string): boolean {
    if (!filePath && !name) return false;

    const config = this.loadConfig();
    return config.documents.some(doc => {
      if (filePath && doc.filePath === filePath) return true;
      if (name && doc.name === name && !doc.filePath) return true;
      return false;
    });
  }

  /**
   * 添加文档
   */
  public addDocument(document: Omit<CheatSheetDocument, 'id' | 'createdAt' | 'updatedAt'>): CheatSheetDocument {
    const config = this.loadConfig();

    // 检查是否已存在相同的文档
    if (this.documentExists(document.filePath, document.name)) {
      throw new Error('文档已存在');
    }

    const newDocument: CheatSheetDocument = {
      ...document,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    config.documents.push(newDocument);
    this.saveConfig(config);
    return newDocument;
  }

  /**
   * 更新文档
   */
  public updateDocument(id: string, updates: Partial<CheatSheetDocument>): boolean {
    const config = this.loadConfig();
    const index = config.documents.findIndex(doc => doc.id === id);
    
    if (index === -1) {
      return false;
    }
    
    config.documents[index] = {
      ...config.documents[index],
      ...updates,
      updatedAt: Date.now()
    };
    
    this.saveConfig(config);
    return true;
  }

  /**
   * 删除文档
   */
  public deleteDocument(id: string): boolean {
    const config = this.loadConfig();
    const index = config.documents.findIndex(doc => doc.id === id);
    
    if (index === -1) {
      return false;
    }
    
    config.documents.splice(index, 1);
    
    // 调整当前文档索引
    if (config.currentDocumentIndex >= config.documents.length) {
      config.currentDocumentIndex = Math.max(0, config.documents.length - 1);
    }
    
    this.saveConfig(config);
    return true;
  }

  /**
   * 获取所有文档
   */
  public getDocuments(): CheatSheetDocument[] {
    const config = this.loadConfig();
    return config.documents;
  }

  /**
   * 获取当前文档
   */
  public getCurrentDocument(): CheatSheetDocument | null {
    const config = this.loadConfig();
    if (config.documents.length === 0) {
      return null;
    }
    
    const index = Math.min(config.currentDocumentIndex, config.documents.length - 1);
    return config.documents[index] || null;
  }

  /**
   * 设置当前文档索引
   */
  public setCurrentDocumentIndex(index: number): void {
    const config = this.loadConfig();
    if (index >= 0 && index < config.documents.length) {
      config.currentDocumentIndex = index;
      this.saveConfig(config);
    }
  }

  /**
   * 下一个文档
   */
  public nextDocument(): CheatSheetDocument | null {
    const config = this.loadConfig();
    if (config.documents.length === 0) {
      return null;
    }
    
    const nextIndex = (config.currentDocumentIndex + 1) % config.documents.length;
    config.currentDocumentIndex = nextIndex;
    this.saveConfig(config);
    return config.documents[nextIndex];
  }

  /**
   * 上一个文档
   */
  public previousDocument(): CheatSheetDocument | null {
    const config = this.loadConfig();
    if (config.documents.length === 0) {
      return null;
    }
    
    const prevIndex = config.currentDocumentIndex === 0 
      ? config.documents.length - 1 
      : config.currentDocumentIndex - 1;
    config.currentDocumentIndex = prevIndex;
    this.saveConfig(config);
    return config.documents[prevIndex];
  }

  /**
   * 添加本地目录
   */
  public addLocalDirectory(directory: string): void {
    const config = this.loadConfig();
    if (!config.localDirectories.includes(directory)) {
      config.localDirectories.push(directory);
      this.saveConfig(config);
    }
  }

  /**
   * 删除本地目录
   */
  public removeLocalDirectory(directory: string): void {
    const config = this.loadConfig();
    const index = config.localDirectories.indexOf(directory);
    if (index !== -1) {
      config.localDirectories.splice(index, 1);
      this.saveConfig(config);
    }
  }

  /**
   * 获取本地目录列表
   */
  public getLocalDirectories(): string[] {
    const config = this.loadConfig();
    return config.localDirectories;
  }

  /**
   * 验证文件大小
   */
  public validateFileSize(size: number): boolean {
    const config = this.loadConfig();
    return size <= config.maxFileSize;
  }

  /**
   * 获取最大文件大小
   */
  public getMaxFileSize(): number {
    const config = this.loadConfig();
    return config.maxFileSize;
  }
}

// Export a singleton instance
export const cheatSheetConfigHelper = new CheatSheetConfigHelper();
