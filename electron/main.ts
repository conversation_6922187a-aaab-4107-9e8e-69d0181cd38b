import { app, BrowserWindow, screen, shell, ipcMain } from "electron"
import path from "path"
import fs from "fs"
import { initializeIpcHandlers } from "./ipcHandlers"
import { ProcessingHelper } from "./ProcessingHelper"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { ShortcutsHelper } from "./shortcuts"
import { initAutoUpdater } from "./autoUpdater"
import { configHelper } from "./ConfigHelper"
import { ActivationHelper } from "./ActivationHelper"
import { CheatSheetHelper } from "./CheatSheetHelper"
import { logHelper } from "./LogHelper"
import * as dotenv from "dotenv"

// Constants
const isDev = process.env.NODE_ENV === "development"

// 初始化日志系统
console.log("初始化日志系统...")
// 加载环境变量，确保环境变量优先于配置文件
loadEnvVariables()
// 加载配置，获取日志设置
const config = configHelper.loadConfig()
// 只有在日志启用的情况下才重定向控制台日志到文件
if (config.loggingEnabled) {
  logHelper.redirectConsoleToLog()
  console.log(`应用启动: ${new Date().toISOString()}`)
  console.log(`运行环境: ${isDev ? "开发模式" : "生产模式"}`)
  console.log(`日志功能: 已启用`)
} else {
  console.log("日志功能已关闭，不会记录日志到文件")
}

// Application State
const state = {
  // Window management properties
  mainWindow: null as BrowserWindow | null,
  isWindowVisible: false,
  isMouseClickThrough: false, // 新增：鼠标穿透状态
  windowPosition: null as { x: number; y: number } | null,
  windowSize: null as { width: number; height: number } | null,
  screenWidth: 0,
  screenHeight: 0,
  step: 0,
  currentX: 0,
  currentY: 0,

  // Application helpers
  screenshotHelper: null as ScreenshotHelper | null,
  shortcutsHelper: null as ShortcutsHelper | null,
  processingHelper: null as ProcessingHelper | null,
  cheatSheetHelper: null as CheatSheetHelper | null,

  // View and state management
  view: "queue" as "queue" | "solutions" | "debug" | "cheatsheet",
  problemInfo: null as any,
  hasDebugged: false,

  // 语音相关状态
  isVoiceChatMode: false,

  // 激活状态管理
  isActivated: false,
  activationHelper: null as ActivationHelper | null,

  // Processing events
  PROCESSING_EVENTS: {
    UNAUTHORIZED: "processing-unauthorized",
    NO_SCREENSHOTS: "processing-no-screenshots",
    OUT_OF_CREDITS: "out-of-credits",
    API_KEY_INVALID: "api-key-invalid",
    INITIAL_START: "initial-start",
    PROBLEM_EXTRACTED: "problem-extracted",
    SOLUTION_SUCCESS: "solution-success",
    INITIAL_SOLUTION_ERROR: "solution-error",
    DEBUG_START: "debug-start",
    DEBUG_SUCCESS: "debug-success",
    DEBUG_ERROR: "debug-error",
    ACTIVATION_REQUIRED: "activation-required", // 新增激活事件
    ACTIVATION_SUCCESS: "activation-success",
    ACTIVATION_ERROR: "activation-error",
    PROCESSING_ERROR: "processing-error" // 新增通用处理错误事件
  } as const
}

// Add interfaces for helper classes
export interface IProcessingHelperDeps {
  getScreenshotHelper: () => ScreenshotHelper | null
  getMainWindow: () => BrowserWindow | null
  getView: () => "queue" | "solutions" | "debug" | "cheatsheet"
  setView: (view: "queue" | "solutions" | "debug" | "cheatsheet") => void
  getProblemInfo: () => any
  setProblemInfo: (info: any) => void
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  clearQueues: () => void
  takeScreenshot: () => Promise<string>
  getImagePreview: (filepath: string) => Promise<string>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  setHasDebugged: (value: boolean) => void
  getHasDebugged: () => boolean
  PROCESSING_EVENTS: typeof state.PROCESSING_EVENTS
  activationHelper: ActivationHelper | null
  isActivated: boolean
}

export interface IShortcutsHelperDeps {
  getMainWindow: () => BrowserWindow | null
  takeScreenshot: () => Promise<string>
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: ProcessingHelper | null
  clearQueues: () => void
  getView: () => "queue" | "solutions" | "debug" | "cheatsheet"
  setView: (view: "queue" | "solutions" | "debug" | "cheatsheet") => void
  isVisible: () => boolean
  toggleMainWindow: () => void
  toggleMouseClickThrough: () => void // 新增：切换鼠标穿透状态
  moveWindowLeft: () => void
  moveWindowRight: () => void
  moveWindowUp: () => void
  moveWindowDown: () => void
  isVoiceChatMode: boolean
}

export interface IIpcHandlerDeps {
  getMainWindow: () => BrowserWindow | null
  setWindowDimensions: (width: number, height: number) => void
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  getImagePreview: (filepath: string) => Promise<string>
  processingHelper: ProcessingHelper | null
  PROCESSING_EVENTS: typeof state.PROCESSING_EVENTS
  takeScreenshot: () => Promise<string>
  getView: () => "queue" | "solutions" | "debug" | "cheatsheet"
  toggleMainWindow: () => void
  toggleMouseClickThrough: () => void // 新增：切换鼠标穿透状态
  clearQueues: () => void
  setView: (view: "queue" | "solutions" | "debug" | "cheatsheet") => void
  moveWindowLeft: () => void
  moveWindowRight: () => void
  moveWindowUp: () => void
  moveWindowDown: () => void
  activationHelper: ActivationHelper | null
  isActivated: boolean
  isVoiceChatMode: boolean
}

// Initialize helpers
function initializeHelpers() {
  state.screenshotHelper = new ScreenshotHelper(state.view)
  state.activationHelper = new ActivationHelper()
  state.cheatSheetHelper = new CheatSheetHelper()
  state.processingHelper = new ProcessingHelper({
    getScreenshotHelper,
    getMainWindow,
    getView,
    setView,
    getProblemInfo,
    setProblemInfo,
    getScreenshotQueue,
    getExtraScreenshotQueue,
    clearQueues,
    takeScreenshot,
    getImagePreview,
    deleteScreenshot,
    setHasDebugged,
    getHasDebugged,
    PROCESSING_EVENTS: state.PROCESSING_EVENTS,
    activationHelper: state.activationHelper,
    isActivated: state.isActivated
  } as IProcessingHelperDeps)
  state.shortcutsHelper = new ShortcutsHelper({
    getMainWindow,
    takeScreenshot,
    getImagePreview,
    processingHelper: state.processingHelper,
    clearQueues,
    getView,
    setView,
    isVisible: () => state.isWindowVisible,
    toggleMainWindow,
    toggleMouseClickThrough, // 新增：传递鼠标穿透切换函数
    moveWindowLeft: () =>
      moveWindowHorizontal((x) =>
        Math.max(-(state.windowSize?.width || 0) / 2, x - state.step)
      ),
    moveWindowRight: () =>
      moveWindowHorizontal((x) =>
        Math.min(
          state.screenWidth - (state.windowSize?.width || 0) / 2,
          x + state.step
        )
      ),
    moveWindowUp: () => moveWindowVertical((y) => y - state.step),
    moveWindowDown: () => moveWindowVertical((y) => y + state.step),
    isVoiceChatMode: state.isVoiceChatMode
  } as IShortcutsHelperDeps)
}

// Auth callback handler

// Register the interview-coder protocol
if (process.platform === "darwin") {
  app.setAsDefaultProtocolClient("interview-coder")
} else {
  app.setAsDefaultProtocolClient("interview-coder", process.execPath, [
    path.resolve(process.argv[1] || "")
  ])
}

// Handle the protocol. In this case, we choose to show an Error Box.
if (process.defaultApp && process.argv.length >= 2) {
  app.setAsDefaultProtocolClient("interview-coder", process.execPath, [
    path.resolve(process.argv[1])
  ])
}

// Force Single Instance Lock
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  app.quit()
} else {
  app.on("second-instance", (event, commandLine) => {
    // Someone tried to run a second instance, we should focus our window.
    if (state.mainWindow) {
      if (state.mainWindow.isMinimized()) state.mainWindow.restore()
      state.mainWindow.focus()

      // Protocol handler removed - no longer using auth callbacks
    }
  })
}

// Auth callback removed as we no longer use Supabase authentication

// Window management functions
async function createWindow(): Promise<void> {
  if (state.mainWindow) {
    if (state.mainWindow.isMinimized()) state.mainWindow.restore()
    state.mainWindow.focus()
    return
  }

  const primaryDisplay = screen.getPrimaryDisplay()
  const workArea = primaryDisplay.workAreaSize
  state.screenWidth = workArea.width
  state.screenHeight = workArea.height
  state.step = 60
  state.currentY = 50

  const windowSettings: Electron.BrowserWindowConstructorOptions = {
    width: 800,
    height: 600,
    minWidth: 750,
    minHeight: 550,
    x: state.currentX,
    y: 50,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: isDev
        ? path.join(__dirname, "../dist-electron/preload.js")
        : path.join(__dirname, "preload.js"),
      scrollBounce: true,
      enableWebSQL: false,
      // 添加媒体权限支持
      allowRunningInsecureContent: false,
      experimentalFeatures: true
    },
    show: true,
    frame: false,
    transparent: true,
    fullscreenable: false,
    hasShadow: false,
    opacity: 1.0,  // Start with full opacity
    backgroundColor: "#00000000",
    focusable: true,
    skipTaskbar: true,
    type: "panel",
    paintWhenInitiallyHidden: true,
    titleBarStyle: "hidden",
    enableLargerThanScreen: true,
    movable: true,
    resizable: true
  }

  state.mainWindow = new BrowserWindow(windowSettings)

  // Add more detailed logging for window events
  state.mainWindow.webContents.on("did-finish-load", () => {
    console.log("Window finished loading")
  })
  state.mainWindow.webContents.on(
    "did-fail-load",
    async (event, errorCode, errorDescription) => {
      console.error("Window failed to load:", errorCode, errorDescription)
      if (isDev) {
        // In development, retry loading after a short delay
        console.log("Retrying to load development server...")
        setTimeout(() => {
          state.mainWindow?.loadURL("http://localhost:54321").catch((error) => {
            console.error("Failed to load dev server on retry:", error)
          })
        }, 1000)
      }
    }
  )

  if (isDev) {
    // In development, load from the dev server
    console.log("Loading from development server: http://localhost:54321")
    state.mainWindow.loadURL("http://localhost:54321").catch((error) => {
      console.error("Failed to load dev server, falling back to local file:", error)
      // Fallback to local file if dev server is not available
      const indexPath = path.join(__dirname, "../dist/index.html")
      console.log("Falling back to:", indexPath)
      if (fs.existsSync(indexPath)) {
        state.mainWindow.loadFile(indexPath)
      } else {
        console.error("Could not find index.html in dist folder")
      }
    })
  } else {
    // In production, load from the built files
    const indexPath = path.join(__dirname, "../dist/index.html")
    console.log("Loading production build:", indexPath)
    
    if (fs.existsSync(indexPath)) {
      state.mainWindow.loadFile(indexPath)
    } else {
      console.error("Could not find index.html in dist folder")
    }
  }

  // Configure window behavior
  state.mainWindow.webContents.setZoomFactor(1)
  if (isDev) {
    state.mainWindow.webContents.openDevTools()
  }
  state.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log("Attempting to open URL:", url)
    try {
      const parsedURL = new URL(url);
      const hostname = parsedURL.hostname;
      const allowedHosts = ["google.com", "supabase.co"];
      if (allowedHosts.includes(hostname) || hostname.endsWith(".google.com") || hostname.endsWith(".supabase.co")) {
        shell.openExternal(url);
        return { action: "deny" }; // Do not open this URL in a new Electron window
      }
    } catch (error) {
      console.error("Invalid URL %d in setWindowOpenHandler: %d" , url , error);
      return { action: "deny" }; // Deny access as URL string is malformed or invalid
    }
    return { action: "allow" };
  })

  // 处理权限请求（包括麦克风权限）
  state.mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
    console.log('权限请求:', permission);

    // 允许媒体相关权限用于语音识别
    switch (permission) {
      case 'media':
        console.log('允许媒体权限');
        callback(true);
        break;
      case 'geolocation':
        console.log('拒绝地理位置权限');
        callback(false);
        break;
      case 'notifications':
        console.log('允许通知权限');
        callback(true);
        break;
      default:
        console.log('默认允许权限:', permission);
        callback(true); // 默认允许其他权限
        break;
    }
  });

  // Enhanced screen capture resistance
  state.mainWindow.setContentProtection(true)

  state.mainWindow.setVisibleOnAllWorkspaces(true, {
    visibleOnFullScreen: true
  })
  state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1)

  // Additional screen capture resistance settings
  if (process.platform === "darwin") {
    // Prevent window from being captured in screenshots
    state.mainWindow.setHiddenInMissionControl(true)
    state.mainWindow.setWindowButtonVisibility(false)
    state.mainWindow.setBackgroundColor("#00000000")

    // Prevent window from being included in window switcher
    state.mainWindow.setSkipTaskbar(true)

    // Disable window shadow
    state.mainWindow.setHasShadow(false)
  }

  // Prevent the window from being captured by screen recording
  state.mainWindow.webContents.setBackgroundThrottling(false)
  state.mainWindow.webContents.setFrameRate(60)

  // Set up window listeners
  state.mainWindow.on("move", handleWindowMove)
  state.mainWindow.on("resize", handleWindowResize)
  state.mainWindow.on("closed", handleWindowClosed)

  // Initialize window state
  const bounds = state.mainWindow.getBounds()
  state.windowPosition = { x: bounds.x, y: bounds.y }
  state.windowSize = { width: bounds.width, height: bounds.height }
  state.currentX = bounds.x
  state.currentY = bounds.y
  state.isWindowVisible = true
  
  // Set opacity based on user preferences or hide initially
  // Ensure the window is visible for the first launch or if opacity > 0.1
  const savedOpacity = configHelper.getOpacity();
  console.log(`Initial opacity from config: ${savedOpacity}`);
  
  // Always make sure window is shown first
  state.mainWindow.showInactive(); // Use showInactive for consistency
  
  if (savedOpacity <= 0.1) {
    console.log('Initial opacity too low, setting to 0 and hiding window');
    state.mainWindow.setOpacity(0);
    state.isWindowVisible = false;
  } else {
    console.log(`Setting initial opacity to ${savedOpacity}`);
    state.mainWindow.setOpacity(savedOpacity);
    state.isWindowVisible = true;
  }
}

function handleWindowMove(): void {
  if (!state.mainWindow) return
  const bounds = state.mainWindow.getBounds()
  state.windowPosition = { x: bounds.x, y: bounds.y }
  state.currentX = bounds.x
  state.currentY = bounds.y
}

function handleWindowResize(): void {
  if (!state.mainWindow) return
  const bounds = state.mainWindow.getBounds()
  state.windowSize = { width: bounds.width, height: bounds.height }
}

function handleWindowClosed(): void {
  // Clear any pending dimension update timeout to prevent errors
  if (dimensionUpdateTimeout) {
    clearTimeout(dimensionUpdateTimeout)
    dimensionUpdateTimeout = null
  }

  state.mainWindow = null
  state.isWindowVisible = false
  state.windowPosition = null
  state.windowSize = null
}

// Window visibility functions
function hideMainWindow(): void {
  if (!state.mainWindow?.isDestroyed()) {
    const bounds = state.mainWindow.getBounds();
    state.windowPosition = { x: bounds.x, y: bounds.y };
    state.windowSize = { width: bounds.width, height: bounds.height };
    // 隐藏时永远设置为鼠标穿透
    state.mainWindow.setIgnoreMouseEvents(true, { forward: true });
    state.mainWindow.setOpacity(0);
    state.isWindowVisible = false;
    console.log('Window hidden, opacity set to 0, mouse events ignored');
  }
}

function showMainWindow(): void {
  if (!state.mainWindow?.isDestroyed()) {
    if (state.windowPosition && state.windowSize) {
      state.mainWindow.setBounds({
        ...state.windowPosition,
        ...state.windowSize
      });
    }
    // 显示时根据当前鼠标穿透状态设置
    state.mainWindow.setIgnoreMouseEvents(state.isMouseClickThrough, { forward: true });
    state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1);
    state.mainWindow.setVisibleOnAllWorkspaces(true, {
      visibleOnFullScreen: true
    });
    state.mainWindow.setContentProtection(true);
    state.mainWindow.setOpacity(0); // Set opacity to 0 before showing
    state.mainWindow.showInactive(); // Use showInactive instead of show+focus
    state.mainWindow.setOpacity(configHelper.getOpacity()); // Then set opacity to 1 after showing
    state.isWindowVisible = true;
    console.log(`Window shown with showInactive(), opacity set to ${configHelper.getOpacity()}, mouse click through: ${state.isMouseClickThrough}`);
  }
}

function toggleMainWindow(): void {
  console.log(`Toggling window. Current state: ${state.isWindowVisible ? 'visible' : 'hidden'}`);
  if (state.isWindowVisible) {
    hideMainWindow();
  } else {
    showMainWindow();
  }
}

// 新增：切换鼠标穿透状态的函数
function toggleMouseClickThrough(): void {
  if (!state.mainWindow?.isDestroyed()) {
    // 只有在窗口可见时才能切换鼠标穿透状态
    if (state.isWindowVisible) {
      state.isMouseClickThrough = !state.isMouseClickThrough;
      state.mainWindow.setIgnoreMouseEvents(state.isMouseClickThrough, { forward: true });
      console.log(`Mouse click through toggled: ${state.isMouseClickThrough ? 'enabled' : 'disabled'}`);

      // 通知渲染进程状态变化
      state.mainWindow.webContents.send('mouse-click-through-changed', state.isMouseClickThrough);
    } else {
      console.log('Cannot toggle mouse click through when window is hidden');
    }
  }
}

// Window movement functions
function moveWindowHorizontal(updateFn: (x: number) => number): void {
  if (!state.mainWindow) return
  state.currentX = updateFn(state.currentX)
  state.mainWindow.setPosition(
    Math.round(state.currentX),
    Math.round(state.currentY)
  )
}

function moveWindowVertical(updateFn: (y: number) => number): void {
  if (!state.mainWindow) return

  const newY = updateFn(state.currentY)
  // Allow window to go 2/3 off screen in either direction
  const maxUpLimit = (-(state.windowSize?.height || 0) * 2) / 3
  const maxDownLimit =
    state.screenHeight + ((state.windowSize?.height || 0) * 2) / 3

  // Log the current state and limits
  console.log({
    newY,
    maxUpLimit,
    maxDownLimit,
    screenHeight: state.screenHeight,
    windowHeight: state.windowSize?.height,
    currentY: state.currentY
  })

  // Only update if within bounds
  if (newY >= maxUpLimit && newY <= maxDownLimit) {
    state.currentY = newY
    state.mainWindow.setPosition(
      Math.round(state.currentX),
      Math.round(state.currentY)
    )
  }
}

// Window dimension functions with smart debounce for better responsiveness
let dimensionUpdateTimeout: NodeJS.Timeout | null = null
let lastDimensions = { width: 0, height: 0 }
let lastUpdateTime = 0

function setWindowDimensions(width: number, height: number): void {
  if (!state.mainWindow?.isDestroyed()) {
    // Clear existing timeout
    if (dimensionUpdateTimeout) {
      clearTimeout(dimensionUpdateTimeout)
    }

    // Only update if dimensions changed significantly (more than 8px for better responsiveness)
    const widthDiff = Math.abs(width - lastDimensions.width)
    const heightDiff = Math.abs(height - lastDimensions.height)

    if (widthDiff < 8 && heightDiff < 8) {
      return // Skip update if change is too small
    }

    const now = Date.now()
    const timeSinceLastUpdate = now - lastUpdateTime

    // Smart debounce: shorter delay for recent updates (responsive), longer for old updates (stable)
    const debounceTime = timeSinceLastUpdate < 300 ? 60 : 120 // 60ms for responsiveness, 120ms for stability

    // Debounce the actual window resize
    dimensionUpdateTimeout = setTimeout(() => {
      if (!state.mainWindow?.isDestroyed()) {
        const [currentX, currentY] = state.mainWindow.getPosition()
        const primaryDisplay = screen.getPrimaryDisplay()
        const workArea = primaryDisplay.workAreaSize

        // 计算最大宽度为屏幕宽度的60%
        const maxWidth = Math.floor(workArea.width * 0.6)
        // 计算最大高度为屏幕高度的80%
        const maxHeight = Math.floor(workArea.height * 0.8)

        // 确保窗口宽度至少为800，高度至少为600
        const finalWidth = Math.max(Math.min(width + 32, maxWidth), 800)
        const finalHeight = Math.max(Math.min(height + 48, maxHeight), 600)

        // 确保窗口不会超出屏幕边界
        const finalX = Math.min(currentX, workArea.width - finalWidth)

        // Update last dimensions and time
        lastDimensions = { width, height }
        lastUpdateTime = Date.now()

        state.mainWindow.setBounds({
          x: finalX,
          y: currentY,
          width: finalWidth,
          height: finalHeight
        })
      }
    }, debounceTime)
  }
}

// Environment setup
function loadEnvVariables() {
  if (isDev) {
    console.log("Loading env variables from:", path.join(process.cwd(), ".env"))
    dotenv.config({ path: path.join(process.cwd(), ".env") })
  } else {
    console.log(
      "Loading env variables from:",
      path.join(process.resourcesPath, ".env")
    )
    dotenv.config({ path: path.join(process.resourcesPath, ".env") })
  }
  // 添加调试日志
  console.log("Loaded environment variables:")
  console.log(process.resourcesPath)
  console.log(path.join(process.resourcesPath, ".env"))
  console.log({
    ACTIVATION_SERVER_URL: process.env.ACTIVATION_SERVER_URL
  })
  console.log("Environment variables loaded for open-source version")
}

// Initialize application
async function initializeApp() {
  try {
    console.log("初始化应用...")
    // Create the browser window
    await createWindow()

    // Initialize helpers
    initializeHelpers()

    // Initialize activation handlers
    initializeActivationHandlers()

    // 预初始化音频系统（性能优化）
    await preInitializeAudioSystems()

    // Setup all IPC handlers
    initializeIpcHandlers({
      getMainWindow,
      setWindowDimensions,
      getScreenshotQueue,
      getExtraScreenshotQueue,
      deleteScreenshot,
      getImagePreview,
      processingHelper: state.processingHelper,
      PROCESSING_EVENTS: state.PROCESSING_EVENTS,
      takeScreenshot,
      getView,
      toggleMainWindow,
      toggleMouseClickThrough, // 新增：传递鼠标穿透切换函数
      clearQueues,
      setView,
      moveWindowLeft: () =>
        moveWindowHorizontal((x) =>
          Math.max(-(state.windowSize?.width || 0) / 2, x - state.step)
        ),
      moveWindowRight: () =>
        moveWindowHorizontal((x) =>
          Math.min(
            state.screenWidth - (state.windowSize?.width || 0) / 2,
            x + state.step
          )
        ),
      moveWindowUp: () => moveWindowVertical((y) => y - state.step),
      moveWindowDown: () => moveWindowVertical((y) => y + state.step),
      activationHelper: state.activationHelper,
      isActivated: state.isActivated,
      isVoiceChatMode: state.isVoiceChatMode
    } as IIpcHandlerDeps)

    // Setup global shortcuts
    state.shortcutsHelper?.registerGlobalShortcuts()

    // 检查截图功能可用性（仅在Windows上）
    if (process.platform === 'win32' && state.screenshotHelper) {
      console.log("检查Windows截图功能可用性...");
      try {
        const diagnostics = await state.screenshotHelper.getScreenshotDiagnostics();
        console.log("截图功能诊断结果:", diagnostics);

        if (!diagnostics.powerShellInfo.available) {
          console.warn("PowerShell不可用，截图功能可能受限");
          console.warn("PowerShell错误:", diagnostics.powerShellInfo.error);
        } else {
          console.log(`PowerShell可用: ${diagnostics.powerShellInfo.path} (版本: ${diagnostics.powerShellInfo.version})`);
        }

        if (!diagnostics.screenshotDesktopAvailable) {
          console.warn("screenshot-desktop库不可用");
        }

        if (!diagnostics.tempDirWritable) {
          console.warn("临时目录不可写:", diagnostics.tempDirPath);
        }
      } catch (error) {
        console.error("截图功能诊断失败:", error);
      }
    }

    // 检查应用激活状态
    if (state.activationHelper) {
      console.log("检查应用激活状态...");
      try {
        state.isActivated = await state.activationHelper.isActivated()
        console.log("激活状态检查结果:", state.isActivated);

        // 如果应用未激活，向渲染进程发送激活消息
        if (!state.isActivated && state.mainWindow) {
          console.log("应用未激活，发送激活消息");

          // 直接发送激活消息，不需要等待 did-finish-load
          state.mainWindow.webContents.send('activation-required');
          state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_REQUIRED);

          // 同时也在页面加载完成后发送激活消息，以确保渲染进程能够接收到
          state.mainWindow.webContents.on('did-finish-load', () => {
            console.log("页面加载完成，再次发送激活消息");
            if (state.mainWindow && !state.isActivated) {
              state.mainWindow.webContents.send('activation-required');
              state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_REQUIRED);
            }
          });
        } else {
          console.log("应用已激活，无需发送激活消息");
        }
      } catch (error) {
        console.error("检查激活状态失败:", error);
        // 如果检查失败，假设未激活，并发送激活消息
        state.isActivated = false;
        if (state.mainWindow) {
          state.mainWindow.webContents.send('activation-required');
          state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_REQUIRED);
        }
      }
    } else {
      console.warn("激活助手未初始化!");
    }
  } catch (error) {
    console.error("Failed to initialize application:", error)
    app.quit()
  }
}

/**
 * 预初始化音频系统（应用启动时调用）
 */
async function preInitializeAudioSystems() {
  try {
    console.log("开始预初始化音频系统...")

    if (!state.mainWindow) {
      console.warn("主窗口未创建，跳过音频系统预初始化")
      return
    }

    // 并行执行多个预初始化任务
    const initTasks = [
      // 预初始化系统音频捕获
      initializeSystemAudioPreload(),
      // 预检查音频权限
      preCheckAudioPermissions(),
      // 预加载音频源
      preloadAudioSources()
    ]

    await Promise.allSettled(initTasks)
    console.log("音频系统预初始化完成")
  } catch (error) {
    console.warn("音频系统预初始化失败:", error)
  }
}

/**
 * 预初始化系统音频捕获
 */
async function initializeSystemAudioPreload() {
  try {
    const { audioCacheManager } = await import('./AudioCacheManager')

    // 使用缓存管理器预初始化
    await audioCacheManager.preInitialize()
    console.log("系统音频捕获预加载完成")
  } catch (error) {
    console.warn("系统音频捕获预加载失败:", error)
  }
}

/**
 * 预检查音频权限
 */
async function preCheckAudioPermissions() {
  try {
    // 这里可以添加更多权限预检查逻辑
    console.log("音频权限预检查完成")
  } catch (error) {
    console.warn("音频权限预检查失败:", error)
  }
}

/**
 * 预加载音频源
 */
async function preloadAudioSources() {
  try {
    // 这个函数现在由 audioCacheManager 处理，保留为兼容性
    console.log("音频源预加载由缓存管理器处理")
  } catch (error) {
    console.warn("音频源预加载失败:", error)
  }
}

// Auth callback handling removed - no longer needed
app.on("open-url", (event, url) => {
  console.log("open-url event received:", url)
  event.preventDefault()
})

// Handle second instance (removed auth callback handling)
app.on("second-instance", (event, commandLine) => {
  console.log("second-instance event received:", commandLine)
  
  // Focus or create the main window
  if (!state.mainWindow) {
    createWindow()
  } else {
    if (state.mainWindow.isMinimized()) state.mainWindow.restore()
    state.mainWindow.focus()
  }
})

// Prevent multiple instances of the app
if (!app.requestSingleInstanceLock()) {
  app.quit()
} else {
  app.on("window-all-closed", () => {
    // Clear any pending timeouts before quitting
    if (dimensionUpdateTimeout) {
      clearTimeout(dimensionUpdateTimeout)
      dimensionUpdateTimeout = null
    }

    if (process.platform !== "darwin") {
      app.quit()
      state.mainWindow = null
    }
  })
}

app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// State getter/setter functions
function getMainWindow(): BrowserWindow | null {
  return state.mainWindow
}

function getView(): "queue" | "solutions" | "debug" | "cheatsheet" {
  return state.view
}

function setView(view: "queue" | "solutions" | "debug" | "cheatsheet"): void {
  state.view = view
  state.screenshotHelper?.setView(view)
}

function getScreenshotHelper(): ScreenshotHelper | null {
  return state.screenshotHelper
}

function getProblemInfo(): any {
  return state.problemInfo
}

function setProblemInfo(problemInfo: any): void {
  state.problemInfo = problemInfo
}

function getScreenshotQueue(): string[] {
  return state.screenshotHelper?.getScreenshotQueue() || []
}

function getExtraScreenshotQueue(): string[] {
  return state.screenshotHelper?.getExtraScreenshotQueue() || []
}

function clearQueues(): void {
  state.screenshotHelper?.clearQueues()
  state.problemInfo = null
  setView("queue")
}

async function takeScreenshot(): Promise<string> {
  if (!state.mainWindow) throw new Error("No main window available")
  return (
    state.screenshotHelper?.takeScreenshot(
      () => hideMainWindow(),
      () => showMainWindow()
    ) || ""
  )
}

async function getImagePreview(filepath: string): Promise<string> {
  return state.screenshotHelper?.getImagePreview(filepath) || ""
}

async function deleteScreenshot(
  path: string
): Promise<{ success: boolean; error?: string }> {
  return (
    state.screenshotHelper?.deleteScreenshot(path) || {
      success: false,
      error: "Screenshot helper not initialized"
    }
  )
}

function setHasDebugged(value: boolean): void {
  state.hasDebugged = value
}

function getHasDebugged(): boolean {
  return state.hasDebugged
}

// 添加 IPC 处理器用于激活流程和语音模式
function initializeActivationHandlers() {
  ipcMain.handle('check-activation', async () => {
    console.log("检查激活状态被调用");
    if (!state.activationHelper) {
      console.log("激活助手未初始化，返回未激活状态");
      return { activated: false };
    }
    
    try {
      const isActivated = await state.activationHelper.isActivated();
      console.log("激活状态检查结果:", isActivated);
      
      // 更新全局状态
      state.isActivated = isActivated;
      
      if (!isActivated && state.mainWindow) {
        console.log("应用未激活，发送激活必要消息");
        state.mainWindow.webContents.send('activation-required');
        state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_REQUIRED);
      }
      
      return { activated: isActivated };
    } catch (error) {
      console.error("检查激活状态出错:", error);
      // 出错时返回未激活
      if (state.mainWindow) {
        state.mainWindow.webContents.send('activation-required');
        state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_REQUIRED);
      }
      return { activated: false, error: String(error) };
    }
  });

  ipcMain.handle('activate-app', async (_, activationCode: string) => {
    console.log("激活应用被调用，激活码:", activationCode);
    if (!state.activationHelper) {
      console.log("激活助手未初始化，激活失败");
      return { success: false, message: '激活系统未初始化' };
    }
    
    try {
      const result = await state.activationHelper.activate(activationCode);
      console.log("激活结果:", result);
      
      if (result.success) {
        // 更新激活状态
        state.isActivated = true;
        
        // 发送激活成功事件
        if (state.mainWindow) {
          console.log("发送激活成功事件");
          state.mainWindow.webContents.send('activation-success');
          state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_SUCCESS);
        }
      } else if (state.mainWindow) {
        // 发送激活错误事件
        console.log("发送激活错误事件:", result.message);
        state.mainWindow.webContents.send('activation-error', result.message);
        state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_ERROR, result.message);
      }
      
      return result;
    } catch (error) {
      console.error("激活过程出错:", error);
      if (state.mainWindow) {
        state.mainWindow.webContents.send('activation-error', String(error));
        state.mainWindow.webContents.send(state.PROCESSING_EVENTS.ACTIVATION_ERROR, String(error));
      }
      return { success: false, message: String(error) };
    }
  });
  
  // 添加获取剩余调用次数的处理器
  ipcMain.handle('get-remaining-calls', async () => {
    console.log("获取剩余调用次数被调用");
    if (!state.activationHelper) {
      console.log("激活助手未初始化，获取剩余调用次数失败");
      return { success: false, message: '激活系统未初始化' };
    }
    
    try {
      // 先检查激活状态
      const isActivated = await state.activationHelper.isActivated();
      if (!isActivated) {
        console.log("应用未激活，无法获取剩余调用次数");
        return { success: false, message: '应用未激活' };
      }
      
      // 获取剩余调用次数
      const remainingCalls = await state.activationHelper.getRemainingCalls();
      console.log("剩余调用次数:", remainingCalls);
      
      return {
        success: true,
        remainingCalls: remainingCalls.remainingCalls,
        isPermanent: remainingCalls.isPermanent,
        planType: remainingCalls.planType
      };
    } catch (error) {
      console.error("获取剩余调用次数出错:", error);
      return { success: false, message: String(error) };
    }
  });
  
  // 添加Ultra版本检查的处理器
  ipcMain.handle('is-ultra-version', async () => {
    console.log("检查Ultra版本被调用");
    if (!state.activationHelper) {
      console.log("激活助手未初始化，返回非Ultra版本");
      return false;
    }

    try {
      return await state.activationHelper.isUltraVersion();
    } catch (error) {
      console.error("检查Ultra版本失败:", error);
      return false;
    }
  });

  // 添加消耗调用次数的处理器
  ipcMain.handle('consume-call', async () => {
    console.log("消耗调用次数被调用");
    if (!state.activationHelper) {
      console.log("激活助手未初始化，消耗调用次数失败");
      return { success: false, message: '激活系统未初始化' };
    }
    
    try {
      // 先检查激活状态
      const isActivated = await state.activationHelper.isActivated();
      if (!isActivated) {
        console.log("应用未激活，无法消耗调用次数");
        return { success: false, message: '应用未激活' };
      }
      
      // 消耗调用次数
      const result = await state.activationHelper.consumeCall();
      console.log("消耗调用次数结果:", result);
      
      // 如果成功消耗了调用次数，通知前端更新剩余次数显示
      if (result.success && result.remainingCalls !== undefined && state.mainWindow) {
        state.mainWindow.webContents.send(
          "remaining-calls-updated", 
          { remainingCalls: result.remainingCalls }
        );
      }
      
      return result;
    } catch (error) {
      console.error("消耗调用次数出错:", error);
      return { success: false, message: String(error) };
    }
  });
}

// Export state and functions for other modules
export {
  state,
  createWindow,
  hideMainWindow,
  showMainWindow,
  toggleMainWindow,
  toggleMouseClickThrough, // 新增：导出鼠标穿透切换函数
  setWindowDimensions,
  moveWindowHorizontal,
  moveWindowVertical,
  getMainWindow,
  getView,
  setView,
  getScreenshotHelper,
  getProblemInfo,
  setProblemInfo,
  getScreenshotQueue,
  getExtraScreenshotQueue,
  clearQueues,
  takeScreenshot,
  getImagePreview,
  deleteScreenshot,
  setHasDebugged,
  getHasDebugged
}

// Clean up function to be called before app quits
function cleanupBeforeQuit(): void {
  console.log("Cleaning up before application quit...")

  // Clear any pending dimension update timeout
  if (dimensionUpdateTimeout) {
    clearTimeout(dimensionUpdateTimeout)
    dimensionUpdateTimeout = null
    console.log("Cleared dimension update timeout")
  }

  // Cancel any ongoing processing
  if (state.processingHelper) {
    state.processingHelper.cancelOngoingRequests()
    console.log("Cancelled ongoing processing requests")
  }

  // Clear window state
  state.mainWindow = null
  state.isWindowVisible = false
  state.windowPosition = null
  state.windowSize = null

  console.log("Cleanup completed")
}

// Add cleanup handlers for various quit scenarios
app.on("before-quit", (event) => {
  console.log("App before-quit event triggered")
  cleanupBeforeQuit()
})

app.on("will-quit", (event) => {
  console.log("App will-quit event triggered")
  cleanupBeforeQuit()
})

app.whenReady().then(initializeApp)
