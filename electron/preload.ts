console.log("Preload script starting...")
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron"
const { shell } = require("electron")

export const PROCESSING_EVENTS = {
  //global states
  UNAUTHORIZED: "procesing-unauthorized",
  NO_SCREENSHOTS: "processing-no-screenshots",
  OUT_OF_CREDITS: "out-of-credits",
  API_KEY_INVALID: "api-key-invalid",

  //states for generating the initial solution
  INITIAL_START: "initial-start",
  PROBLEM_EXTRACTED: "problem-extracted",
  SOLUTION_SUCCESS: "solution-success",
  INITIAL_SOLUTION_ERROR: "solution-error",
  RESET: "reset",

  //states for processing the debugging
  DEBUG_START: "debug-start",
  DEBUG_SUCCESS: "debug-success",
  DEBUG_ERROR: "debug-error",
  
  // 激活相关状态
  ACTIVATION_REQUIRED: "activation-required",
  ACTIVATION_SUCCESS: "activation-success",
  ACTIVATION_ERROR: "activation-error",
  PROCESSING_ERROR: "processing-error", // 新增通用处理错误事件
  
  // 语音相关状态
  VOICE_CHAT_MODE_CHANGED: "voice-chat-mode-changed",
  TOGGLE_VOICE_RECORDING: "toggle-voice-recording"
} as const

// At the top of the file
console.log("Preload script is running")

const electronAPI = {
  // Original methods
  openSubscriptionPortal: async (authData: { id: string; email: string }) => {
    return ipcRenderer.invoke("open-subscription-portal", authData)
  },
  openSettingsPortal: () => ipcRenderer.invoke("open-settings-portal"),
  updateContentDimensions: (dimensions: { width: number; height: number }) =>
    ipcRenderer.invoke("update-content-dimensions", dimensions),
  clearStore: () => ipcRenderer.invoke("clear-store"),
  getScreenshots: () => ipcRenderer.invoke("get-screenshots"),
  deleteScreenshot: (path: string) =>
    ipcRenderer.invoke("delete-screenshot", path),
  toggleMainWindow: async () => {
    console.log("toggleMainWindow called from preload")
    try {
      const result = await ipcRenderer.invoke("toggle-window")
      console.log("toggle-window result:", result)
      return result
    } catch (error) {
      console.error("Error in toggleMainWindow:", error)
      throw error
    }
  },
  // 新增：鼠标穿透控制API
  toggleMouseClickThrough: async () => {
    console.log("toggleMouseClickThrough called from preload")
    try {
      const result = await ipcRenderer.invoke("toggle-mouse-click-through")
      console.log("toggle-mouse-click-through result:", result)
      return result
    } catch (error) {
      console.error("Error in toggleMouseClickThrough:", error)
      throw error
    }
  },
  // Event listeners
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => {
    const subscription = (_: any, data: { path: string; preview: string }) =>
      callback(data)
    ipcRenderer.on("screenshot-taken", subscription)
    return () => {
      ipcRenderer.removeListener("screenshot-taken", subscription)
    }
  },
  onResetView: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("reset-view", subscription)
    return () => {
      ipcRenderer.removeListener("reset-view", subscription)
    }
  },
  onSolutionStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.INITIAL_START, subscription)
    }
  },
  onDebugStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_START, subscription)
    }
  },
  onDebugSuccess: (callback: (data: any) => void) => {
    ipcRenderer.on("debug-success", (_event, data) => callback(data))
    return () => {
      ipcRenderer.removeListener("debug-success", (_event, data) =>
        callback(data)
      )
    }
  },
  onDebugError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    }
  },
  onSolutionError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
        subscription
      )
    }
  },
  onProcessingNoScreenshots: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.NO_SCREENSHOTS, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.NO_SCREENSHOTS, subscription)
    }
  },
  onOutOfCredits: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.OUT_OF_CREDITS, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.OUT_OF_CREDITS, subscription)
    }
  },
  onProblemExtracted: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.PROBLEM_EXTRACTED, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.PROBLEM_EXTRACTED,
        subscription
      )
    }
  },
  onSolutionSuccess: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.SOLUTION_SUCCESS, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.SOLUTION_SUCCESS,
        subscription
      )
    }
  },
  onUnauthorized: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    }
  },
  // External URL handler
  openLink: (url: string) => shell.openExternal(url),
  triggerScreenshot: () => ipcRenderer.invoke("trigger-screenshot"),
  triggerProcessScreenshots: () =>
    ipcRenderer.invoke("trigger-process-screenshots"),
  triggerDirectAnswer: () => ipcRenderer.invoke("trigger-direct-answer"),
  triggerDirectDebug: () => ipcRenderer.invoke("trigger-direct-debug"),
  triggerReset: () => ipcRenderer.invoke("trigger-reset"),
  triggerMoveLeft: () => ipcRenderer.invoke("trigger-move-left"),
  triggerMoveRight: () => ipcRenderer.invoke("trigger-move-right"),
  triggerMoveUp: () => ipcRenderer.invoke("trigger-move-up"),
  triggerMoveDown: () => ipcRenderer.invoke("trigger-move-down"),
  onSubscriptionUpdated: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("subscription-updated", subscription)
    return () => {
      ipcRenderer.removeListener("subscription-updated", subscription)
    }
  },
  onSubscriptionPortalClosed: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("subscription-portal-closed", subscription)
    return () => {
      ipcRenderer.removeListener("subscription-portal-closed", subscription)
    }
  },
  onReset: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.RESET, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.RESET, subscription)
    }
  },
  startUpdate: () => ipcRenderer.invoke("start-update"),
  installUpdate: () => ipcRenderer.invoke("install-update"),
  onUpdateAvailable: (callback: (info: any) => void) => {
    const subscription = (_: any, info: any) => callback(info)
    ipcRenderer.on("update-available", subscription)
    return () => {
      ipcRenderer.removeListener("update-available", subscription)
    }
  },
  onUpdateDownloaded: (callback: (info: any) => void) => {
    const subscription = (_: any, info: any) => callback(info)
    ipcRenderer.on("update-downloaded", subscription)
    return () => {
      ipcRenderer.removeListener("update-downloaded", subscription)
    }
  },
  decrementCredits: () => ipcRenderer.invoke("decrement-credits"),
  onCreditsUpdated: (callback: (credits: number) => void) => {
    const subscription = (_event: any, credits: number) => callback(credits)
    ipcRenderer.on("credits-updated", subscription)
    return () => {
      ipcRenderer.removeListener("credits-updated", subscription)
    }
  },
  getPlatform: () => process.platform,
  
  // New methods for OpenRouter API integration
  getConfig: () => ipcRenderer.invoke("get-config"),
  updateConfig: (config: { apiKey?: string; model?: string; language?: string; opacity?: number; loggingEnabled?: boolean; theme?: "dark" | "light"; openrouterReasoningEnabled?: boolean; zhipuThinkingEnabled?: boolean }) =>
    ipcRenderer.invoke("update-config", config),
  validateModel: (modelId: string, apiKey: string, requiresImages?: boolean) =>
    ipcRenderer.invoke("validate-model", modelId, apiKey, requiresImages),
  onShowSettings: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("show-settings-dialog", subscription)
    return () => {
      ipcRenderer.removeListener("show-settings-dialog", subscription)
    }
  },
  checkApiKey: () => ipcRenderer.invoke("check-api-key"),
  validateApiKey: (apiKey: string) => 
    ipcRenderer.invoke("validate-api-key", apiKey),
  openExternalUrl: (url: string) =>
    ipcRenderer.invoke("openExternal", url),
  onApiKeyInvalid: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.API_KEY_INVALID, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.API_KEY_INVALID, subscription)
    }
  },
  onToggleThoughtsVisibility: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("toggle-thoughts-visibility", subscription)
    return () => {
      ipcRenderer.removeListener("toggle-thoughts-visibility", subscription)
    }
  },
  removeListener: (eventName: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(eventName, callback)
  },
  onDeleteLastScreenshot: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("delete-last-screenshot", subscription)
    return () => {
      ipcRenderer.removeListener("delete-last-screenshot", subscription)
    }
  },
  deleteLastScreenshot: () => ipcRenderer.invoke("delete-last-screenshot"),
  
  // 激活相关API
  checkActivation: () => ipcRenderer.invoke("check-activation"),
  activateApp: (activationCode: string) => 
    ipcRenderer.invoke("activate-app", activationCode),
  getRemainingCalls: () => ipcRenderer.invoke("get-remaining-calls"),
  consumeCall: () => ipcRenderer.invoke("consume-call"),
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
  },
  onActivationRequired: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on(PROCESSING_EVENTS.ACTIVATION_REQUIRED, subscription);
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.ACTIVATION_REQUIRED, subscription);
    };
  },
  onActivationSuccess: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on(PROCESSING_EVENTS.ACTIVATION_SUCCESS, subscription);
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.ACTIVATION_SUCCESS, subscription);
    };
  },
  onActivationError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error);
    ipcRenderer.on(PROCESSING_EVENTS.ACTIVATION_ERROR, subscription);
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.ACTIVATION_ERROR, subscription);
    };
  },
  onRemainingCallsUpdated: (callback: (data: { remainingCalls: number }) => void) => {
    const subscription = (_: any, data: { remainingCalls: number }) => callback(data);
    ipcRenderer.on("remaining-calls-updated", subscription);
    return () => {
      ipcRenderer.removeListener("remaining-calls-updated", subscription);
    };
  },
  onVoicePermissionRequired: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on("voice-permission-required", subscription);
    return () => {
      ipcRenderer.removeListener("voice-permission-required", subscription);
    };
  },
  onVoiceRecordingPermissionRequired: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on("voice-recording-permission-required", subscription);
    return () => {
      ipcRenderer.removeListener("voice-recording-permission-required", subscription);
    };
  },
  onDirectAnswerPermissionRequired: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on("direct-answer-permission-required", subscription);
    return () => {
      ipcRenderer.removeListener("direct-answer-permission-required", subscription);
    };
  },
  // 语音模式相关API
  toggleVoiceChatMode: async () => {
    return ipcRenderer.invoke("toggle-voice-chat-mode");
  },
  streamChat: async (messages: any[], callback: (data: { type: 'content' | 'reasoning', token: string }) => void) => {
    return new Promise<any>((resolve, reject) => {
      const eventSourceId = `stream-${Date.now()}`;
      console.log(`创建流式聊天请求，ID: ${eventSourceId}, 消息数量: ${messages.length}`);
      
      // 清除可能已存在的旧事件监听器
      try {
        ipcRenderer.removeAllListeners(`${eventSourceId}-message`);
        ipcRenderer.removeAllListeners(`${eventSourceId}-done`);
        ipcRenderer.removeAllListeners(`${eventSourceId}-error`);
      } catch (err) {
        console.error('清除旧事件监听器失败:', err);
      }
      
      // 创建一个自定义的事件源对象
      const eventSource = {
        addEventListener: (event: string, handler: (event: any) => void) => {
          console.log(`添加事件监听器: ${event}`);
          
          if (event === 'message') {
            // 使用on而不是once，确保能接收多个消息
            ipcRenderer.on(`${eventSourceId}-message`, (_event, data) => {
              try {
                // 处理新的数据结构：{ type: 'content' | 'reasoning', token: string }
                if (data && typeof data === 'object' && data.type && data.token) {
                  // console.log(`收到消息token: "${data.token.substring(0, 20)}${data.token.length > 20 ? '...' : ''}", 类型: ${data.type}, 长度: ${data.token.length}`);

                  // 先调用回调，确保状态更新
                  callback(data);

                  // 然后触发事件处理
                  handler({ data: data.token });
                } else if (data && typeof data === 'string') {
                  // 兼容旧的字符串格式
                  callback({ type: 'content', token: data } as { type: 'content' | 'reasoning', token: string });
                  handler({ data: data });
                } else {
                  console.error(`收到无效数据, 类型: ${typeof data}`, data);
                }
              } catch (err) {
                console.error("处理消息token时出错:", err);
              }
            });
          } else if (event === 'done') {
            ipcRenderer.once(`${eventSourceId}-done`, () => {
              console.log('收到done事件，流式响应完成');
              try {
                handler({});
              } catch (err) {
                console.error("处理done事件时出错:", err);
              } finally {
                // 清理事件监听器
                setTimeout(() => {
                  try {
                    ipcRenderer.removeAllListeners(`${eventSourceId}-message`);
                    ipcRenderer.removeAllListeners(`${eventSourceId}-error`);
                    console.log(`清理事件监听器完成: ${eventSourceId}`);
                  } catch (err) {
                    console.error("清理事件监听器时出错:", err);
                  }
                }, 500); // 延迟清理，确保所有消息都被处理
              }
            });
          } else if (event === 'error') {
            ipcRenderer.once(`${eventSourceId}-error`, (_event, error) => {
              console.error(`收到error事件: ${error}`);
              try {
                handler(error);
              } catch (err) {
                console.error("处理error事件时出错:", err);
              } finally {
                // 清理事件监听器
                ipcRenderer.removeAllListeners(`${eventSourceId}-message`);
                ipcRenderer.removeAllListeners(`${eventSourceId}-done`);
              }
            });
          }
        }
      };
      
      // 发起流式调用请求
      ipcRenderer.invoke("stream-chat", { messages, eventSourceId })
        .then(() => {
          console.log(`stream-chat IPC调用成功返回`);
          resolve(eventSource);
        })
        .catch(error => {
          console.error(`stream-chat IPC调用失败: ${error}`);
          
          // 确保清理所有事件监听器
          ipcRenderer.removeAllListeners(`${eventSourceId}-message`);
          ipcRenderer.removeAllListeners(`${eventSourceId}-done`);
          ipcRenderer.removeAllListeners(`${eventSourceId}-error`);
          
          reject(error);
        });
    });
  },
  
  // 语音模式事件监听
  onVoiceChatModeChanged: (callback: (isVoiceChatMode: boolean) => void) => {
    const subscription = (_: any, isVoiceChatMode: boolean) => callback(isVoiceChatMode);
    ipcRenderer.on('voice-chat-mode-changed', subscription);
    return () => {
      ipcRenderer.removeListener('voice-chat-mode-changed', subscription);
    };
  },
  onToggleVoiceRecording: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('toggle-voice-recording', subscription);
    return () => {
      ipcRenderer.removeListener('toggle-voice-recording', subscription);
    };
  },
  onHandleCtrlEnter: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('handle-ctrl-enter', subscription);
    return () => {
      ipcRenderer.removeListener('handle-ctrl-enter', subscription);
    };
  },
  onHandleCtrlShiftEnter: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('handle-ctrl-shift-enter', subscription);
    return () => {
      ipcRenderer.removeListener('handle-ctrl-shift-enter', subscription);
    };
  },
  // 新增：鼠标穿透状态变化监听
  onMouseClickThroughChanged: (callback: (isClickThrough: boolean) => void) => {
    const subscription = (_: any, isClickThrough: boolean) => callback(isClickThrough);
    ipcRenderer.on('mouse-click-through-changed', subscription);
    return () => {
      ipcRenderer.removeListener('mouse-click-through-changed', subscription);
    };
  },

  // Voice configuration APIs
  getVoiceConfig: () => ipcRenderer.invoke("get-voice-config"),
  updateVoiceConfig: (config: any) => ipcRenderer.invoke("update-voice-config", config),
  testVoiceCredentials: (provider: string, credentials: any) =>
    ipcRenderer.invoke("test-voice-credentials", provider, credentials),
  getVoiceProviderConfig: (provider: string) =>
    ipcRenderer.invoke("get-voice-provider-config", provider),
  isVoiceProviderConfigured: (provider: string) =>
    ipcRenderer.invoke("is-voice-provider-configured", provider),

  // Voice configuration event listeners
  onVoiceConfigUpdated: (callback: (config: any) => void) => {
    const subscription = (_: any, config: any) => callback(config);
    ipcRenderer.on('voice-config-updated', subscription);
    return () => {
      ipcRenderer.removeListener('voice-config-updated', subscription);
    };
  },

  // 通义Gummy WebSocket APIs
  gummyConnect: (config: any, language?: string) =>
    ipcRenderer.invoke("gummy-connect", config, language),
  getOrCreateGummyHandler: (sourceIdentifier: string, config: any, language?: string) =>
    ipcRenderer.invoke("get-or-create-gummy-handler", sourceIdentifier, config, language),
  gummyCheckAndReconnect: (sourceIdentifier?: string) =>
    ipcRenderer.invoke("gummy-check-and-reconnect", sourceIdentifier),
  gummyStartTask: (sourceIdentifier?: string) =>
    ipcRenderer.invoke("gummy-start-task", sourceIdentifier),
  gummyFinishTask: (sourceIdentifier?: string) =>
    ipcRenderer.invoke("gummy-finish-task", sourceIdentifier),
  gummyDisconnect: () =>
    ipcRenderer.invoke("gummy-disconnect"),
  gummySendAudio: (audioData: ArrayBuffer, sourceIdentifier?: string) =>
    ipcRenderer.invoke("gummy-send-audio", audioData, sourceIdentifier),

  // 兼容旧的API
  gummyStartRecognition: (config: any, language?: string) =>
    ipcRenderer.invoke("gummy-start-recognition", config, language),
  gummyStopRecognition: () =>
    ipcRenderer.invoke("gummy-stop-recognition"),
  onGummyResult: (callback: (transcription: any) => void) => {
    const subscription = (_: any, transcription: any) => callback(transcription);
    ipcRenderer.on("gummy-result", subscription);
    return () => {
      ipcRenderer.removeListener("gummy-result", subscription);
    };
  },
  onGummyTaskStarted: (callback: (data?: any) => void) => {
    const subscription = (_: any, data: any) => callback(data);
    ipcRenderer.on("gummy-task-started", subscription);
    return () => {
      ipcRenderer.removeListener("gummy-task-started", subscription);
    };
  },
  onGummyTaskFinished: (callback: (data?: any) => void) => {
    const subscription = (_: any, data: any) => callback(data);
    ipcRenderer.on("gummy-task-finished", subscription);
    return () => {
      ipcRenderer.removeListener("gummy-task-finished", subscription);
    };
  },
  onGummyError: (callback: (errorData: any) => void) => {
    const subscription = (_: any, errorData: any) => callback(errorData);
    ipcRenderer.on("gummy-error", subscription);
    return () => {
      ipcRenderer.removeListener("gummy-error", subscription);
    };
  },
  onGummyEnd: (callback: (endData?: any) => void) => {
    const subscription = (_: any, endData: any) => callback(endData);
    ipcRenderer.on("gummy-end", subscription);
    return () => {
      ipcRenderer.removeListener("gummy-end", subscription);
    };
  },

  // Ultra版本检查
  isUltraVersion: () => ipcRenderer.invoke("is-ultra-version"),

  // 自定义提示词配置API
  getCustomPromptsConfig: () => ipcRenderer.invoke("get-custom-prompts-config"),
  updateCustomPromptsConfig: (config: any) => ipcRenderer.invoke("update-custom-prompts-config", config),
  getDefaultCustomPromptsConfig: (language?: 'chinese' | 'english') => ipcRenderer.invoke("get-default-custom-prompts-config", language),

  // 小抄相关API
  cheatSheetGetDocuments: () => ipcRenderer.invoke("cheatsheet-get-documents"),
  cheatSheetGetCurrentDocument: () => ipcRenderer.invoke("cheatsheet-get-current-document"),
  cheatSheetCreateMarkdown: (name: string, content: string) => ipcRenderer.invoke("cheatsheet-create-markdown", name, content),
  cheatSheetSelectFile: () => ipcRenderer.invoke("cheatsheet-select-file"),
  cheatSheetAddLocalFile: (filePath: string) => ipcRenderer.invoke("cheatsheet-add-local-file", filePath),
  cheatSheetGetDocumentContent: (document: any) => ipcRenderer.invoke("cheatsheet-get-document-content", document),
  cheatSheetSearch: (document: any, searchTerm: string) => ipcRenderer.invoke("cheatsheet-search", document, searchTerm),
  cheatSheetNextDocument: () => ipcRenderer.invoke("cheatsheet-next-document"),
  cheatSheetPreviousDocument: () => ipcRenderer.invoke("cheatsheet-previous-document"),
  cheatSheetSetCurrentDocument: (index: number) => ipcRenderer.invoke("cheatsheet-set-current-document", index),
  cheatSheetDeleteDocument: (id: string) => ipcRenderer.invoke("cheatsheet-delete-document", id),
  cheatSheetUpdateDocument: (id: string, updates: any) => ipcRenderer.invoke("cheatsheet-update-document", id, updates),
  cheatSheetSelectDirectory: () => ipcRenderer.invoke("cheatsheet-select-directory"),
  cheatSheetScanDirectory: (directoryPath: string) => ipcRenderer.invoke("cheatsheet-scan-directory", directoryPath),
  cheatSheetAddLocalDirectory: (directory: string) => ipcRenderer.invoke("cheatsheet-add-local-directory", directory),
  cheatSheetRemoveLocalDirectory: (directory: string) => ipcRenderer.invoke("cheatsheet-remove-local-directory", directory),
  cheatSheetGetLocalDirectories: () => ipcRenderer.invoke("cheatsheet-get-local-directories"),

  // 系统操作
  openExternal: (path: string) => ipcRenderer.invoke("open-external", path),
  getImageDataUrl: (imagePath: string) => ipcRenderer.invoke("get-image-data-url", imagePath),
  readPDFFile: (pdfPath: string) => ipcRenderer.invoke("read-pdf-file", pdfPath),

  // 系统音频捕获相关API
  checkSystemAudioPermission: () => ipcRenderer.invoke("check-system-audio-permission"),
  getSystemAudioSources: () => ipcRenderer.invoke("get-system-audio-sources"),
  preInitializeSystemAudio: (options?: any) => ipcRenderer.invoke("pre-initialize-system-audio", options),
  startSystemAudioCapture: (options?: any) => ipcRenderer.invoke("start-system-audio-capture", options),
  stopSystemAudioCapture: () => ipcRenderer.invoke("stop-system-audio-capture"),
  clearSystemAudioCache: () => ipcRenderer.invoke("clear-system-audio-cache"),
  isSystemAudioCapturing: () => ipcRenderer.invoke("is-system-audio-capturing"),
  getSystemAudioConfig: () => ipcRenderer.invoke("get-system-audio-config"),

  // 处理来自渲染进程的系统音频数据
  handleSystemAudioData: (audioData: ArrayBuffer) => ipcRenderer.invoke("handle-system-audio-data", audioData),

  // 系统音频事件监听
  onSystemAudioData: (callback: (audioData: ArrayBuffer) => void) => {
    const subscription = (_event: any, audioData: ArrayBuffer) => callback(audioData);
    ipcRenderer.on("system-audio-data", subscription);
    return () => ipcRenderer.removeListener("system-audio-data", subscription);
  },
  onSystemAudioError: (callback: (error: string) => void) => {
    const subscription = (_event: any, error: string) => callback(error);
    ipcRenderer.on("system-audio-error", subscription);
    return () => ipcRenderer.removeListener("system-audio-error", subscription);
  },
  onSystemAudioStarted: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on("system-audio-started", subscription);
    return () => ipcRenderer.removeListener("system-audio-started", subscription);
  },
  onSystemAudioStopped: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on("system-audio-stopped", subscription);
    return () => ipcRenderer.removeListener("system-audio-stopped", subscription);
  },

  // 热词表管理 APIs
  createVocabularyTable: (name: string, hotWords: any[]) =>
    ipcRenderer.invoke("create-vocabulary-table", name, hotWords),
  updateVocabularyTable: (id: string, updates: any) =>
    ipcRenderer.invoke("update-vocabulary-table", id, updates),
  deleteVocabularyTable: (id: string) =>
    ipcRenderer.invoke("delete-vocabulary-table", id),
  setActiveVocabularyTable: (id: string | undefined) =>
    ipcRenderer.invoke("set-active-vocabulary-table", id),
  getVocabularyTables: () =>
    ipcRenderer.invoke("get-vocabulary-tables"),
  getActiveVocabularyTable: () =>
    ipcRenderer.invoke("get-active-vocabulary-table"),
  syncVocabularyToCloud: (tableId: string) =>
    ipcRenderer.invoke("sync-vocabulary-to-cloud", tableId),
  deleteVocabularyFromCloud: (tableId: string) =>
    ipcRenderer.invoke("delete-vocabulary-from-cloud", tableId),

}

// Before exposing the API
console.log(
  "About to expose electronAPI with methods:",
  Object.keys(electronAPI)
)

// Expose the API
contextBridge.exposeInMainWorld("electronAPI", electronAPI)

console.log("electronAPI exposed to window")

// Add this focus restoration handler
ipcRenderer.on("restore-focus", () => {
  // Try to focus the active element if it exists
  const activeElement = document.activeElement as HTMLElement
  if (activeElement && typeof activeElement.focus === "function") {
    activeElement.focus()
  }
})

// 添加快捷键触发的语音模式切换处理
ipcRenderer.on("execute-toggle-voice-chat-mode", async () => {
  console.log("接收到语音模式切换指令");
  try {
    // 直接调用toggle-voice-chat-mode处理程序
    const result = await ipcRenderer.invoke("toggle-voice-chat-mode");
    console.log("语音模式切换结果:", result);
  } catch (error) {
    console.error("语音模式切换失败:", error);
  }
});

// 小抄模式相关事件处理
ipcRenderer.on("open-cheatsheet-mode", () => {
  console.log("接收到打开小抄模式指令");
  window.dispatchEvent(new CustomEvent("open-cheatsheet-mode"));
});

ipcRenderer.on("cheatsheet-next", () => {
  console.log("接收到小抄下一页指令");
  window.dispatchEvent(new CustomEvent("cheatsheet-next"));
});

ipcRenderer.on("cheatsheet-previous", () => {
  console.log("接收到小抄上一页指令");
  window.dispatchEvent(new CustomEvent("cheatsheet-previous"));
});

// Remove auth-callback handling - no longer needed
