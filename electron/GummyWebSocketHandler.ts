import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';

export interface GummyConfig {
  apiKey: string;
  hostUrl?: string;
}

export interface GummyTranscription {
  sentence_id: number;
  begin_time: number;
  end_time: number;
  text: string;
  words?: any[];
  sentence_end: boolean;
  source_identifier?: string; // 添加音频源标识
}

export interface GummyWebSocketOptions {
  config: GummyConfig;
  language?: string;
  vocabularyId?: string; // 热词表ID
  sourceIdentifier?: string; // 音频源标识
  onResult?: (transcription: GummyTranscription) => void;
  onError?: (error: string) => void;
  onEnd?: () => void;
}

export class GummyWebSocketHandler extends EventEmitter {
  private ws: WebSocket | null = null;
  private taskId: string = '';
  private config: GummyConfig;
  private language: string;
  private vocabularyId?: string; // 热词表ID
  private isConnected: boolean = false;
  private isTaskRunning: boolean = false;
  private connectionTimeout: ReturnType<typeof setTimeout> | null = null;
  private readonly CONNECTION_TIMEOUT = 60000; // 60秒超时
  private options: GummyWebSocketOptions; // 保存原始选项以便重连
  private sourceIdentifier: string; // 音频源标识

  constructor(options: GummyWebSocketOptions) {
    super();
    this.options = options; // 保存完整选项
    this.config = options.config;
    this.language = options.language || 'zh';
    this.vocabularyId = options.vocabularyId;
    this.sourceIdentifier = options.sourceIdentifier || 'microphone';

    if (options.onResult) {
      this.on('result', options.onResult);
    }
    if (options.onError) {
      this.on('error', options.onError);
    }
    if (options.onEnd) {
      this.on('end', options.onEnd);
    }
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = this.config.hostUrl || 'wss://dashscope.aliyuncs.com/api-ws/v1/inference';

        // 创建WebSocket连接，设置请求头
        this.ws = new WebSocket(wsUrl, {
          headers: {
            'Authorization': `bearer ${this.config.apiKey}`,
            'X-DashScope-DataInspection': 'enable'
          }
        });

        this.ws.on('open', () => {
          console.log('通义Gummy WebSocket连接已建立');
          this.isConnected = true;
          this.resetConnectionTimeout();
          resolve();
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          try {
            const response = JSON.parse(data.toString());
            this.handleMessage(response);
          } catch (error) {
            console.error(`解析通义Gummy响应失败 [${this.sourceIdentifier}]:`, error);

            // 通过回调触发错误事件，确保携带 sourceIdentifier
            if (this.options.onError) {
              this.options.onError('解析语音识别响应失败');
            } else {
              // 兼容旧版本，如果没有 onError 回调则使用 emit
              this.emit('error', '解析语音识别响应失败');
            }
          }
        });

        this.ws.on('error', (error) => {
          console.error(`通义Gummy WebSocket错误 [${this.sourceIdentifier}]:`, error);

          // 通过回调触发事件，确保携带 sourceIdentifier
          if (this.options.onError) {
            this.options.onError('WebSocket连接错误');
          } else {
            // 兼容旧版本，如果没有 onError 回调则使用 emit
            this.emit('error', 'WebSocket连接错误');
          }
          reject(error);
        });

        this.ws.on('close', (code, reason) => {
          console.log(`通义Gummy WebSocket连接关闭 [${this.sourceIdentifier}]:`, code, reason.toString());
          this.isConnected = false;
          this.isTaskRunning = false;
          this.clearConnectionTimeout();

          // 通过回调触发事件，确保携带 sourceIdentifier
          if (this.options.onEnd) {
            this.options.onEnd();
          } else {
            // 兼容旧版本，如果没有 onEnd 回调则使用 emit
            this.emit('end');
          }
        });

      } catch (error) {
        console.error('创建通义Gummy WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  startTask(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接，无法启动任务');
      return;
    }

    if (this.isTaskRunning) {
      console.warn('任务已在运行中，跳过启动');
      return;
    }

    // 生成任务ID
    this.taskId = uuidv4().replace(/-/g, '');
    this.isTaskRunning = true;

    // 清除连接超时，因为有新任务开始
    this.clearConnectionTimeout();

    // 发送run-task指令
    const parameters: any = {
      sample_rate: 16000,
      format: 'pcm',
      source_language: this.language,
      transcription_enabled: true,
      translation_enabled: false
    };

    // 如果有热词表ID，添加到参数中
    if (this.vocabularyId) {
      parameters.vocabulary_id = this.vocabularyId;
      console.log('使用热词表ID:', this.vocabularyId);
    }

    const runTaskMessage = {
      header: {
        action: 'run-task',
        task_id: this.taskId,
        streaming: 'duplex'
      },
      payload: {
        task_group: 'audio',
        task: 'asr',
        function: 'recognition',
        model: 'gummy-realtime-v1',
        parameters,
        input: {}
      }
    };

    console.log('发送run-task指令，任务ID:', this.taskId);
    this.ws.send(JSON.stringify(runTaskMessage));
  }

  private handleMessage(response: any): void {
    switch (response.header.event) {
      case 'task-started':
        console.log('通义Gummy任务开始，任务ID:', response.header.task_id);
        this.emit('task-started');
        break;

      case 'result-generated':
        if (response.payload?.output?.transcription) {
          const transcription = response.payload.output.transcription;

          if (transcription.text) {
            // 添加音频源标识到识别结果中
            transcription.source_identifier = this.sourceIdentifier;
            this.emit('result', transcription);
          }
        }
        break;

      case 'task-finished':
        console.log('通义Gummy任务完成，任务ID:', response.header.task_id);
        this.isTaskRunning = false;
        this.emit('task-finished');
        // 任务完成后不断开连接，而是重置超时计时器
        this.resetConnectionTimeout();
        break;

      case 'task-failed':
        console.error(`通义Gummy任务失败 [${this.sourceIdentifier}]:`, response.header.error_message);
        this.isTaskRunning = false;

        // 通过回调触发错误事件，确保携带 sourceIdentifier
        if (this.options.onError) {
          this.options.onError(`语音识别失败: ${response.header.error_message}`);
        } else {
          // 兼容旧版本，如果没有 onError 回调则使用 emit
          this.emit('error', `语音识别失败: ${response.header.error_message}`);
        }

        // 任务失败时断开连接，因为连接无法继续复用
        this.disconnect();
        break;
    }
  }

  sendAudioData(audioData: Buffer): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(audioData);
    }
  }

  finishTask(): void {
    if (!this.isTaskRunning) {
      console.warn('没有正在运行的任务，跳过结束任务');
      return;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const finishTaskMessage = {
        header: {
          action: 'finish-task',
          task_id: this.taskId,
          streaming: 'duplex'
        },
        payload: {
          input: {}
        }
      };

      console.log('发送finish-task指令，任务ID:', this.taskId);
      this.ws.send(JSON.stringify(finishTaskMessage));
    }
  }

  disconnect(): void {
    this.clearConnectionTimeout();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.isTaskRunning = false;
  }

  isConnectedToService(): boolean {
    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
  }

  isTaskActive(): boolean {
    return this.isTaskRunning;
  }

  // 重新连接方法
  async reconnect(): Promise<boolean> {
    try {
      console.log('尝试重新建立通义Gummy连接...');

      // 先断开现有连接
      this.disconnect();

      // 重新建立连接
      await this.connect();

      console.log('通义Gummy重连成功');
      return true;
    } catch (error) {
      console.error('通义Gummy重连失败:', error);
      return false;
    }
  }

  // 重置连接超时计时器
  private resetConnectionTimeout(): void {
    this.clearConnectionTimeout();
    this.connectionTimeout = setTimeout(() => {
      console.log('通义Gummy连接超时，自动断开');
      this.disconnect();
    }, this.CONNECTION_TIMEOUT);
  }

  // 清除连接超时计时器
  private clearConnectionTimeout(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }
}
