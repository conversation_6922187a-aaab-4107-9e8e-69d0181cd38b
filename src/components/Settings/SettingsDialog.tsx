import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  Di<PERSON>Trigger,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Select, SelectOption } from "../ui/select";
import { Settings } from "lucide-react";
import { useToast } from "../../contexts/toast";
import { validateModelForPurpose, ModelValidationResult } from "../../utils/modelValidation";


type APIProvider = "openrouter" | "zhipu" | "custom";

type AIModel = {
  id: string;
  name: string;
  description: string;
  supportsMultimodal?: boolean;
};

type ModelCategory = {
  key: 'extractionModel' | 'solutionModel' | 'debuggingModel';
  title: string;
  description: string;
  openrouterModels: AIModel[];
  zhipuModels: AIModel[];
  requiresImages: boolean;
};

// Define available models for each category
const modelCategories: ModelCategory[] = [
  {
    key: 'extractionModel',
    title: '问题提取',
    description: '用于分析截图并提取问题详情的模型',
    requiresImages: true,
    openrouterModels: [
      {
        id: "google/gemma-3-27b-it:free",
        name: "Gemma3-27B",
        description: "Gemma3-27B多模态模型，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "google/gemma-3-12b-it:free",
        name: "Gemma3-12B",
        description: "Gemma3-12B多模态模型，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "mistralai/mistral-small-3.2-24b-instruct:free",
        name: "Mistral-Small-3.2-24B",
        description: "Mistral-Small-3.2-24B多模态模型，与3.1版本相比，3.2版本减少了无限生成，并在工具使用和结构化输出任务中取得了进步。",
        supportsMultimodal: true
      },
      {
        id: "qwen/qwen2.5-vl-32b-instruct:free",
        name: "Qwen2.5-VL-32B",
        description: "Qwen2.5-VL-32B是一个通过强化学习微调的多模态视觉语言模型，用于增强数学推理、结构化输出和视觉问题解决能力。",
        supportsMultimodal: true
      },
      {
        id: "qwen/qwen2.5-vl-72b-instruct:free",
        name: "Qwen2.5-VL-72B",
        description: "Qwen2.5-VL-72B是能够识别常见的物体，如花、鸟、鱼和昆虫。它还非常擅长分析图像中的文本、图表、图标、图形和布局。",
        supportsMultimodal: true
      },
      {
        id: "mistralai/mistral-small-3.1-24b-instruct:free",
        name: "Mistral-Small-3.1-24B",
        description: "Mistral-Small-3.1-24B多模态模型，基于文本的推理和视觉任务中提供最先进的性能，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.0-flash-exp:free",
        name: "Gemini2-Flash-Experimental",
        description: "Gemini2.0闪存实验版多模态模型，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash-lite-preview-06-17",
        name: "Gemini-2.5-Flash-Lite-Preview-06-17(收费/便宜)",
        description: "Gemini-2.5-Flash-Lite是Gemini 2.5家族中的一种轻量级推理模型，针对超低延迟和成本效率进行了优化。（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash-preview-05-20",
        name: "Gemini-2.5-Flash-Preview-05-20(收费/便宜)",
        description: "Gemini-2.5-Flash-05-20是 Google 最先进的工作模型（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.0-flash-001",
        name: "gemini-2.0-flash(收费/很便宜)",
        description: "在多模态理解、编码能力、复杂指令遵循和函数调用方面引入了显著增强。（收费模型/效果可以/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1-mini",
        name: "Gpt-4.1-Mini(收费/稍贵一丢丢)",
        description: "GPT-4.1 Mini 是一个中等规模的模型，其性能与 GPT-4o 相当，但延迟和成本显著降低。（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1-nano",
        name: "Gpt-4.1-Nano(收费/很便宜)",
        description: "对于需要低延迟的任务，GPT-4.1 nano 是 GPT-4.1 系列中最快且最便宜的模型。（收费模型/效果一般/速度巨快/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1",
        name: "GPT-4.1(收费/贵啊)",
        description: "GPT-4.1 是一款旗舰级大型语言模型，专为高级指令遵循、实际软件工程和长上下文推理进行了优化。（收费模型/无需多言，就是贵啊）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4o-mini",
        name: "GPT-4o-mini(收费/便宜)",
        description: "GPT-4o-mini是OpenAI在GPT-4 Omni之后的最新模型，支持文本和图像输入，并以文本形式输出。（收费模型/效果还行/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4o",
        name: "gpt-4o(收费/很贵)",
        description: "GPT-4o是OpenAI的人工智能模型，支持文本和图像输入，并输出文本。（收费模型/无需多言，就是很贵啊）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash",
        name: "Gemini-2.5-Flash(收费/小贵)",
        description: "Gemini-2.5-Flash是Google的顶尖工作模型，专门为高级推理、编码、数学和科学任务设计。它内置了“思考”功能。（收费模型/无需多言，有的小贵）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-pro",
        name: "Gemini-2.5-Pro(收费/贵啊)",
        description: "Gemini 2.5 Pro 是 Google 的最先进的 AI 模型，专为高级推理、编码、数学和科学任务而设计。（收费模型/无需多言，就是贵啊）",
        supportsMultimodal: true
      },
      {
        id: "anthropic/claude-sonnet-4",
        name: "Claude-Sonnet-4(收费/更贵了)",
        description: "Claude Sonnet 4 显著提升了其前身 Sonnet 3.7 的功能，在编码和推理任务中表现出更高的精确度和可控性。（收费模型/无需多言，就是更贵啊）",
        supportsMultimodal: true
      },
      {
        id: "anthropic/claude-opus-4",
        name: "Claude-opus-4(收费/巨贵，我是用不起)",
        description: "Claude Opus 4 被基准测试为世界最佳编码模型，在发布时，为复杂、长时间运行的任务和代理工作流程带来了持续的性能。（收费模型/无需多言，巨贵，我是用不起）",
        supportsMultimodal: true
      }
    ],
    zhipuModels: [
      {
        id: "glm-4v-plus-0111",
        name: "GLM-4V-Plus-0111(收费)",
        description: "智谱AI高级多模态模型，更强的图像理解能力",
        supportsMultimodal: true
      }
    ]
  },
  {
    key: 'solutionModel',
    title: '解决方案生成',
    description: '用于生成代码解决方案的模型',
    requiresImages: false,
    openrouterModels: [
      {
        id: "deepseek/deepseek-r1-0528:free",
        name: "DeepSeek-R1-0528",
        description: "5月28日更新了原始DeepSeek-R1，性能更强（深度思考）",
        supportsMultimodal: false
      },
      {
        id: "tngtech/deepseek-r1t2-chimera:free",
        name: "DeepSeek-R1T2-Chimera",
        description: "DeepSeek-R1T2-Chimera模型，结合R1-0528、R1和V3-0324组装的混合文本模型，在保证强大推理性能，运行速度比原始R1快约20%（深度思考）",
        supportsMultimodal: false
      },
      {
        id: "z-ai/glm-4.5-air:free",
        name: "GLM-4.5-Air",
        description: "GLM-4.5-Air是智谱最新旗舰模型系列的轻量级变体，专为以代理为中心的应用而设计。（深度思考）（好用、挺快）",
        supportsMultimodal: false
      },
      {
        id: "deepseek/deepseek-chat-v3-0324:free",
        name: "DeepSeek-V3-0324",
        description: "DeepSeek-V3-0324模型，DeepSeek 团队旗舰聊天模型系列的最新迭代。",
        supportsMultimodal: false
      },
      {
        id: "tngtech/deepseek-r1t-chimera:free",
        name: "deepseek-r1t-chimera",
        description: "deepseekR1t结合了 R1 的推理能力和 V3 的 token 效率改进",
        supportsMultimodal: false
      },
      {
        id: "deepseek/deepseek-r1:free",
        name: "DeepSeek-R1",
        description: "DeepSeek-R1，性能与OpenAI-o1相当（深度思考）",
        supportsMultimodal: false
      },
      {
        id: "mistralai/devstral-small-2505:free",
        name: "Devstral-Small-2505",
        description: "24B参数的智能体，由Mistral-Small-3.1微调而来，用于高级软件工程任务",
        supportsMultimodal: false
      },
      {
        id: "deepseek/deepseek-chat-v3-0324",
        name: "DeepSeek-V3-0324(收费/挺便宜)",
        description: "DeepSeek团队旗舰聊天模型系列的最新迭代，DeepSeek-V3-0324收费款，就是比免费的更快更稳定（收费模型/效果不错/速度可以/稳定不报错）",
        supportsMultimodal: false
      },
      {
        id: "deepseek/deepseek-r1-0528",
        name: "DeepSeek-R1-0528(收费/挺便宜)",
        description: "5月28日更新了原始DeepSeek-R1，性能更强DeepSeek-R1-0528收费款，就是比免费的更快更稳定（深度思考）（收费模型/效果不错/速度可以/稳定不报错）",
        supportsMultimodal: false
      },
      {
        id: "qwen/qwen3-235b-a22b-07-25",
        name: "Qwen3-235B-A22B-2507(收费/挺便宜)",
        description: "Qwen3-235B-A22B-Instruct-2507是基于Qwen3-235B架构的混合语言模型。（用于复杂推理、数学和代码任务）。（收费模型/效果不错/速度可以/稳定不报错）",
        supportsMultimodal: false
      },
      {
        id: "google/gemini-2.5-flash-lite-preview-06-17",
        name: "Gemini-2.5-Flash-Lite-Preview-06-17(收费/便宜)",
        description: "Gemini-2.5-Flash-Lite是Gemini 2.5家族中的一种轻量级推理模型，针对超低延迟和成本效率进行了优化。（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash-preview-05-20",
        name: "Gemini-2.5-Flash-Preview-05-20(收费/便宜)",
        description: "Gemini-2.5-Flash-05-20是 Google 最先进的工作模型（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.0-flash-001",
        name: "gemini-2.0-flash(收费/很便宜)",
        description: "在多模态理解、编码能力、复杂指令遵循和函数调用方面引入了显著增强。（收费模型/效果可以/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1-mini",
        name: "Gpt-4.1-Mini(收费/稍贵一丢丢)",
        description: "GPT-4.1 Mini 是一个中等规模的模型，其性能与 GPT-4o 相当，但延迟和成本显著降低。（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1-nano",
        name: "Gpt-4.1-Nano(收费/很便宜)",
        description: "对于需要低延迟的任务，GPT-4.1 nano 是 GPT-4.1 系列中最快且最便宜的模型。（收费模型/效果一般/速度巨快/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1",
        name: "GPT-4.1(收费/贵啊)",
        description: "GPT-4.1 是一款旗舰级大型语言模型，专为高级指令遵循、实际软件工程和长上下文推理进行了优化。（收费模型/无需多言，就是贵啊）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4o-mini",
        name: "GPT-4o-mini(收费/便宜)",
        description: "GPT-4o-mini是OpenAI在GPT-4 Omni之后的最新模型，支持文本和图像输入，并以文本形式输出。（收费模型/效果还行/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4o",
        name: "gpt-4o(收费/很贵)",
        description: "GPT-4o是OpenAI的人工智能模型，支持文本和图像输入，并输出文本。（收费模型/无需多言，就是很贵啊）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash",
        name: "Gemini-2.5-Flash(收费/小贵)",
        description: "Gemini-2.5-Flash是Google的顶尖工作模型，专门为高级推理、编码、数学和科学任务设计。它内置了“思考”功能。（收费模型/无需多言，有的小贵）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-pro",
        name: "Gemini-2.5-Pro(收费/贵啊)",
        description: "Gemini 2.5 Pro 是 Google 的最先进的 AI 模型，专为高级推理、编码、数学和科学任务而设计。（收费模型/无需多言，就是贵啊）",
        supportsMultimodal: true
      },
      {
        id: "anthropic/claude-sonnet-4",
        name: "Claude-Sonnet-4(收费/更贵了)",
        description: "Claude Sonnet 4 显著提升了其前身 Sonnet 3.7 的功能，在编码和推理任务中表现出更高的精确度和可控性。（收费模型/无需多言，就是更贵啊）",
        supportsMultimodal: true
      },
      {
        id: "anthropic/claude-opus-4",
        name: "Claude-opus-4(收费/巨贵，我是用不起)",
        description: "Claude Opus 4 被基准测试为世界最佳编码模型，在发布时，为复杂、长时间运行的任务和代理工作流程带来了持续的性能。（收费模型/无需多言，巨贵，我是用不起）",
        supportsMultimodal: true
      }
    ],
    zhipuModels: [
      {
        id: "GLM-4.5-Flash",
        name: "GLM-4.5-Flash（免费无限制）",
        description: "快速文本生成模型，适合代码生成",
        supportsMultimodal: false
      },
      {
        id: "glm-4-flash-250414",
        name: "GLM-4-Flash-250414（免费无限制）",
        description: "快速文本生成模型，免费语言模型 GLM-4-Flash 的增强版本",
        supportsMultimodal: false
      },
      {
        id: "glm-z1-flash",
        name: "GLM-Z1-Flash（深度思考-免费无限制）",
        description: "免费推理：免费的推理API，零成本调用推理大模型，免费无限制",
        supportsMultimodal: false
      },
      {
        id: "GLM-4.5",
        name: "GLM-4.5（收费）",
        description: "高智能旗舰：性能最优，强大的推理能力、代码生成能力",
        supportsMultimodal: false
      },
      {
        id: "GLM-4.5-X",
        name: "GLM-4.5-X（收费）",
        description: "高智能旗舰-极速版：推理速度更快，适用于搜索问答、智能助手、实时翻译等时效性较强场景",
        supportsMultimodal: false
      },
      {
        id: "GLM-4.5-Air",
        name: "GLM-4.5-Air（收费）",
        description: "高性价比：同参数规模性能最佳，在推理、编码和智能体任务上表现强劲",
        supportsMultimodal: false
      },
      {
        id: "glm-z1-flashx",
        name: "GLM-Z1-FlashX（深度思考-收费）",
        description: "高速低价：Flash增强版本，超快推理速度，更快并发保障",
        supportsMultimodal: false
      },
      {
        id: "glm-4-air-250414",
        name: "GLM-4-Air-250414（收费）",
        description: "高性价比：推理能力和价格之间最平衡的模型",
        supportsMultimodal: false
      }
    ]
  },
  {
    key: 'debuggingModel',
    title: '调试',
    description: '用于调试和改进解决方案的模型',
    requiresImages: true,
    openrouterModels: [
      {
        id: "google/gemma-3-27b-it:free",
        name: "Gemma3-27B",
        description: "Gemma3-27B多模态模型，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "google/gemma-3-12b-it:free",
        name: "Gemma3-12B",
        description: "Gemma3-12B多模态模型，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "mistralai/mistral-small-3.2-24b-instruct:free",
        name: "Mistral-Small-3.2-24B",
        description: "Mistral-Small-3.2-24B多模态模型，与3.1版本相比，3.2版本减少了无限生成，并在工具使用和结构化输出任务中取得了进步。",
        supportsMultimodal: true
      },
      {
        id: "qwen/qwen2.5-vl-32b-instruct:free",
        name: "Qwen2.5-VL-32B",
        description: "Qwen2.5-VL-32B是一个通过强化学习微调的多模态视觉语言模型，用于增强数学推理、结构化输出和视觉问题解决能力。",
        supportsMultimodal: true
      },
      {
        id: "qwen/qwen2.5-vl-72b-instruct:free",
        name: "Qwen2.5-VL-72B",
        description: "Qwen2.5-VL-72B是能够识别常见的物体，如花、鸟、鱼和昆虫。它还非常擅长分析图像中的文本、图表、图标、图形和布局。",
        supportsMultimodal: true
      },
      {
        id: "mistralai/mistral-small-3.1-24b-instruct:free",
        name: "Mistral-Small-3.1-24B",
        description: "Mistral-Small-3.1-24B多模态模型，基于文本的推理和视觉任务中提供最先进的性能，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.0-flash-exp:free",
        name: "Gemini2-Flash-Experimental",
        description: "Gemini2.0闪存实验版多模态模型，支持图像理解",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash-lite-preview-06-17",
        name: "Gemini-2.5-Flash-Lite-Preview-06-17(收费/便宜)",
        description: "Gemini-2.5-Flash-Lite是Gemini 2.5家族中的一种轻量级推理模型，针对超低延迟和成本效率进行了优化。（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash-preview-05-20",
        name: "Gemini-2.5-Flash-Preview-05-20(收费/便宜)",
        description: "Gemini-2.5-Flash-05-20是 Google 最先进的工作模型（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.0-flash-001",
        name: "gemini-2.0-flash(收费/很便宜)",
        description: "在多模态理解、编码能力、复杂指令遵循和函数调用方面引入了显著增强。（收费模型/效果可以/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1-mini",
        name: "Gpt-4.1-Mini(收费/稍贵一丢丢)",
        description: "GPT-4.1 Mini 是一个中等规模的模型，其性能与 GPT-4o 相当，但延迟和成本显著降低。（收费模型/效果很好/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1-nano",
        name: "Gpt-4.1-Nano(收费/很便宜)",
        description: "对于需要低延迟的任务，GPT-4.1 nano 是 GPT-4.1 系列中最快且最便宜的模型。（收费模型/效果一般/速度巨快/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4.1",
        name: "GPT-4.1(收费/贵啊)",
        description: "GPT-4.1 是一款旗舰级大型语言模型，专为高级指令遵循、实际软件工程和长上下文推理进行了优化。（收费模型/无需多言，就是贵啊）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4o-mini",
        name: "GPT-4o-mini(收费/便宜)",
        description: "GPT-4o-mini是OpenAI在GPT-4 Omni之后的最新模型，支持文本和图像输入，并以文本形式输出。（收费模型/效果还行/速度可以/稳定不报错）",
        supportsMultimodal: true
      },
      {
        id: "openai/gpt-4o",
        name: "gpt-4o(收费/很贵)",
        description: "GPT-4o是OpenAI的人工智能模型，支持文本和图像输入，并输出文本。（收费模型/无需多言，就是很贵啊）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-flash",
        name: "Gemini-2.5-Flash(收费/小贵)",
        description: "Gemini-2.5-Flash是Google的顶尖工作模型，专门为高级推理、编码、数学和科学任务设计。它内置了“思考”功能。（收费模型/无需多言，有的小贵）",
        supportsMultimodal: true
      },
      {
        id: "google/gemini-2.5-pro",
        name: "Gemini-2.5-Pro(收费/贵啊)",
        description: "Gemini 2.5 Pro 是 Google 的最先进的 AI 模型，专为高级推理、编码、数学和科学任务而设计。（收费模型/无需多言，就是贵啊）",
        supportsMultimodal: true
      },
      {
        id: "anthropic/claude-sonnet-4",
        name: "Claude-Sonnet-4(收费/更贵了)",
        description: "Claude Sonnet 4 显著提升了其前身 Sonnet 3.7 的功能，在编码和推理任务中表现出更高的精确度和可控性。（收费模型/无需多言，就是更贵啊）",
        supportsMultimodal: true
      },
      {
        id: "anthropic/claude-opus-4",
        name: "Claude-opus-4(收费/巨贵，我是用不起)",
        description: "Claude Opus 4 被基准测试为世界最佳编码模型，在发布时，为复杂、长时间运行的任务和代理工作流程带来了持续的性能。（收费模型/无需多言，巨贵，我是用不起）",
        supportsMultimodal: true
      }
    ],
    zhipuModels: [
      {
        id: "glm-4v-plus-0111",
        name: "GLM-4V-Plus-0111(收费)",
        description: "智谱AI高级多模态模型，更强的图像理解能力",
        supportsMultimodal: true
      }
    ]
  }
];

interface SettingsDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function SettingsDialog({ open: externalOpen, onOpenChange }: SettingsDialogProps) {
  const [open, setOpen] = useState(externalOpen || false);
  const [apiKey, setApiKey] = useState("");
  const [apiProvider, setApiProvider] = useState<APIProvider>("openrouter");
  const [baseUrl, setBaseUrl] = useState("");
  const [extractionModel, setExtractionModel] = useState("qwen/qwen2.5-vl-32b-instruct:free");
  const [solutionModel, setSolutionModel] = useState("z-ai/glm-4.5-air:free");
  const [debuggingModel, setDebuggingModel] = useState("mistralai/mistral-small-3.2-24b-instruct:free");
  const [promptLanguage, setPromptLanguage] = useState<"chinese" | "english">("chinese");
  const [openrouterReasoningEnabled, setOpenrouterReasoningEnabled] = useState(false);
  const [zhipuThinkingEnabled, setZhipuThinkingEnabled] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [isUltraVersion, setIsUltraVersion] = useState(false);

  // 模型验证状态
  const [modelValidation, setModelValidation] = useState<{
    [key: string]: {
      isValidating: boolean;
      result?: ModelValidationResult;
    }
  }>({});

  const { showToast } = useToast();
  
  // 防止配置界面抖动
  const [dialogHeight, setDialogHeight] = useState<number | null>(null);
  const dialogRef = useRef<HTMLDivElement>(null);

  // Sync with external open state
  useEffect(() => {
    if (externalOpen !== undefined) {
      setOpen(externalOpen);
    }
  }, [externalOpen]);

  // Handle open state changes
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    // Only call onOpenChange when there's actually a change
    if (onOpenChange && newOpen !== externalOpen) {
      onOpenChange(newOpen);
    }
  };
  
  // 监听对话框打开状态以确保高度稳定
  useEffect(() => {
    if (open && dialogRef.current) {
      // 延迟获取对话框高度，确保内容已完全渲染
      const timer = setTimeout(() => {
        if (dialogRef.current) {
          const height = dialogRef.current.scrollHeight;
          setDialogHeight(height);
        }
      }, 100);
      
      return () => clearTimeout(timer);
    } else if (!open) {
      // 对话框关闭时重置高度
      setDialogHeight(null);
    }
  }, [open, apiProvider]);
  
  // Load current config on dialog open
  useEffect(() => {
    if (open) {
      setIsLoading(true);
      interface Config {
        apiKey?: string;
        apiProvider?: APIProvider;
        baseUrl?: string;
        extractionModel?: string;
        solutionModel?: string;
        debuggingModel?: string;
        promptLanguage?: "chinese" | "english";
        loggingEnabled?: boolean;
        openrouterReasoningEnabled?: boolean;
        zhipuThinkingEnabled?: boolean;
      }

      // 检查Ultra版本
      window.electronAPI.isUltraVersion()
        .then((isUltra: boolean) => {
          setIsUltraVersion(isUltra);
        })
        .catch((error: unknown) => {
          console.error("Failed to check ultra version:", error);
          setIsUltraVersion(false);
        });

      window.electronAPI
        .getConfig()
        .then((config: Config) => {
          setApiKey(config.apiKey || "");
          setApiProvider(config.apiProvider || "openrouter");
          setBaseUrl(config.baseUrl || "");

          // 设置提示词语言
          setPromptLanguage(config.promptLanguage || "chinese");

          // 设置 OpenRouter 推理开关
          setOpenrouterReasoningEnabled(config.openrouterReasoningEnabled || false);

          // 设置智谱AI推理开关
          setZhipuThinkingEnabled(config.zhipuThinkingEnabled || false);

          // 设置默认模型
          setExtractionModel(config.extractionModel || "qwen/qwen2.5-vl-32b-instruct:free");
          setSolutionModel(config.solutionModel || "z-ai/glm-4.5-air:free");
          setDebuggingModel(config.debuggingModel || "mistralai/mistral-small-3.2-24b-instruct:free");
        })
        .catch((error: unknown) => {
          console.error("Failed to load config:", error);
          showToast("错误", "加载设置失败", "error");
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [open, showToast]);

  // Handle API provider change
  const handleProviderChange = (provider: APIProvider) => {
    // 检查自定义API是否需要Ultra版本
    if (provider === "custom" && !isUltraVersion) {
      showToast("功能限制", "自定义API功能需要Ultra版本，请升级后使用", "error");
      return;
    }

    setApiProvider(provider);

    if (provider === "openrouter") {
      // Set OpenRouter default models
      setBaseUrl("");
      setExtractionModel("qwen/qwen2.5-vl-32b-instruct:free");
      setSolutionModel("z-ai/glm-4.5-air:free");
      setDebuggingModel("mistralai/mistral-small-3.2-24b-instruct:free");
    } else if (provider === "zhipu") {
      // Set Zhipu AI defaults
      setBaseUrl(""); // 智谱AI不需要用户填写baseUrl
      setExtractionModel("glm-4v-plus-0111");
      setSolutionModel("GLM-4.5-Flash");
      setDebuggingModel("glm-4v-plus-0111");
    } else if (provider === "custom") {
      // Set custom provider defaults
      setBaseUrl("");
      setExtractionModel("");
      setSolutionModel("");
      setDebuggingModel("");
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // 验证所有模型
      const modelsToValidate = {
        extractionModel: { modelId: extractionModel, requiresImages: true },
        solutionModel: { modelId: solutionModel, requiresImages: false },
        debuggingModel: { modelId: debuggingModel, requiresImages: true }
      };

      // 并行验证所有模型
      const validationPromises = Object.entries(modelsToValidate).map(async ([key, config]) => {
        const result = await validateModelForPurpose(config.modelId, apiKey, config.requiresImages, apiProvider);
        return { key, result };
      });

      const validationResults = await Promise.all(validationPromises);

      // 检查是否有验证失败的模型
      const failedValidations = validationResults.filter(({ result }) => !result.isValid);

      if (failedValidations.length > 0) {
        const errorMessages = failedValidations.map(({ key, result }) => {
          const categoryName = key === 'extractionModel' ? '问题提取' :
                              key === 'solutionModel' ? '解决方案生成' : '调试';
          return `${categoryName}: ${result.error}`;
        });

        showToast("模型验证失败", errorMessages.join('\n'), "error");
        setIsLoading(false);
        return;
      }

      let finalExtractionModel = extractionModel;
      let finalSolutionModel = solutionModel;
      let finalDebuggingModel = debuggingModel;
      
      const result = await window.electronAPI.updateConfig({
        apiKey,
        apiProvider,
        baseUrl: apiProvider === "custom" ? baseUrl : undefined,
        extractionModel: finalExtractionModel,
        solutionModel: finalSolutionModel,
        debuggingModel: finalDebuggingModel,
        promptLanguage,
        openrouterReasoningEnabled,
        zhipuThinkingEnabled,
      });
      
      if (result) {
        showToast("成功", "设置保存成功", "success");
        handleOpenChange(false);
        
        // Force reload the app to apply the API key
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
    } catch (error) {
      console.error("Failed to save settings:", error);
      showToast("错误", "保存设置失败", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // Mask API key for display
  const maskApiKey = (key: string) => {
    if (!key || key.length < 10) return "";
    return `${key.substring(0, 4)}...${key.substring(key.length - 4)}`;
  };

  // Open external link handler
  const openExternalLink = (url: string) => {
    window.electronAPI.openLink(url);
  };

  // 验证单个模型
  const validateModel = async (modelId: string, categoryKey: string, requiresImages: boolean) => {
    if (!apiKey || !modelId) return;

    setModelValidation(prev => ({
      ...prev,
      [categoryKey]: { isValidating: true }
    }));

    try {
      const result = await validateModelForPurpose(modelId, apiKey, requiresImages, apiProvider);

      setModelValidation(prev => ({
        ...prev,
        [categoryKey]: { isValidating: false, result }
      }));

      if (!result.isValid) {
        showToast("模型验证失败", result.error || "模型不可用", "error");
      }
    } catch (error) {
      setModelValidation(prev => ({
        ...prev,
        [categoryKey]: {
          isValidating: false,
          result: {
            isValid: false,
            supportsImages: false,
            error: "验证过程中发生错误"
          }
        }
      }));
    }
  };

  // 处理模型选择变化
  const handleModelChange = (categoryKey: string, modelId: string, requiresImages: boolean) => {
    // 更新模型状态
    switch (categoryKey) {
      case 'extractionModel':
        setExtractionModel(modelId);
        break;
      case 'solutionModel':
        setSolutionModel(modelId);
        break;
      case 'debuggingModel':
        setDebuggingModel(modelId);
        break;
    }

    // 如果有API密钥，立即验证模型
    if (apiKey) {
      validateModel(modelId, categoryKey, requiresImages);
    }
  };

  // 根据API提供商和模型类别过滤模型
  const filterModels = (category: ModelCategory, provider: APIProvider) => {
    let models: AIModel[] = [];

    if (provider === "openrouter") {
      models = category.openrouterModels;
    } else if (provider === "zhipu") {
      models = category.zhipuModels;
    } else {
      // 自定义API不提供预设模型
      models = [];
    }

    // 对于提取问题和调试场景，需要多模态支持
    if (category.key === 'extractionModel' || category.key === 'debuggingModel') {
      return models.filter(model => model.supportsMultimodal);
    }

    // 对于生成方案，所有模型都可以使用
    return models;
  };

  // 将AIModel转换为SelectOption
  const convertToSelectOptions = (models: AIModel[]): SelectOption[] => {
    return models.map(model => ({
      value: model.id,
      label: model.name,
      description: model.description,
      supportsMultimodal: model.supportsMultimodal
    }));
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className="sm:max-w-md settings-dialog"
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'min(450px, 90vw)',
          maxHeight: '90vh',
          overflowY: 'auto',
          zIndex: 9999,
          margin: 0,
          padding: '20px',
          transition: 'all 0.25s ease',
          opacity: 0.98,
          height: dialogHeight ? `${dialogHeight}px` : 'auto',
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-primary)',
          color: 'var(--text-primary)',
        }}
        ref={dialogRef}
      >
        <DialogHeader>
          <DialogTitle style={{ color: 'var(--text-primary)' }}>API 设置</DialogTitle>
          <DialogDescription style={{ color: 'var(--text-tertiary)' }}>
            配置您的 API 密钥和模型偏好。您需要自己的 API 密钥才能使用此应用程序。
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4" style={{ minHeight: '300px' }}>
          {/* API Provider Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>API 提供商</label>
            <div className="grid grid-cols-1 gap-2">
              <div
                className="p-2 rounded-lg cursor-pointer transition-colors"
                style={{
                  backgroundColor: apiProvider === "openrouter" ? 'var(--bg-tertiary)' : 'var(--bg-secondary)',
                  border: `1px solid ${apiProvider === "openrouter" ? 'var(--border-primary)' : 'var(--border-secondary)'}`,
                }}
                onClick={() => handleProviderChange("openrouter")}
                onMouseEnter={(e) => {
                  if (apiProvider !== "openrouter") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (apiProvider !== "openrouter") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: apiProvider === "openrouter" ? 'var(--text-primary)' : 'var(--text-tertiary)',
                    }}
                  />
                  <div className="flex flex-col">
                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>OpenRouter</p>
                    <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>开源免费模型</p>
                  </div>
                </div>
              </div>
              <div
                className="p-2 rounded-lg cursor-pointer transition-colors"
                style={{
                  backgroundColor: apiProvider === "zhipu" ? 'var(--bg-tertiary)' : 'var(--bg-secondary)',
                  border: `1px solid ${apiProvider === "zhipu" ? 'var(--border-primary)' : 'var(--border-secondary)'}`,
                }}
                onClick={() => handleProviderChange("zhipu")}
                onMouseEnter={(e) => {
                  if (apiProvider !== "zhipu") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (apiProvider !== "zhipu") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: apiProvider === "zhipu" ? 'var(--text-primary)' : 'var(--text-tertiary)',
                    }}
                  />
                  <div className="flex flex-col">
                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>智谱AI</p>
                    <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>GLM系列模型（便宜送的多）</p>
                  </div>
                </div>
              </div>
              <div
                className={`p-2 rounded-lg cursor-pointer transition-colors ${!isUltraVersion ? "opacity-50" : ""}`}
                style={{
                  backgroundColor: apiProvider === "custom" ? 'var(--bg-tertiary)' : 'var(--bg-secondary)',
                  border: `1px solid ${apiProvider === "custom" ? 'var(--border-primary)' : 'var(--border-secondary)'}`,
                }}
                onClick={() => handleProviderChange("custom")}
                onMouseEnter={(e) => {
                  if (apiProvider !== "custom" && isUltraVersion) {
                    e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (apiProvider !== "custom" && isUltraVersion) {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: apiProvider === "custom" ? 'var(--text-primary)' : 'var(--text-tertiary)',
                    }}
                  />
                  <div className="flex flex-col">
                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                      自定义API {!isUltraVersion && "(Ultra版本)"}
                    </p>
                    <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>支持OpenAI SDK格式</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* API Key Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }} htmlFor="apiKey">
              {apiProvider === "openrouter" ? "OpenRouter API 密钥" :
               apiProvider === "zhipu" ? "智谱AI API 密钥" : "API 密钥"}
            </label>
            <Input
              id="apiKey"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder={
                apiProvider === "openrouter" ? "sk-or-..." :
                apiProvider === "zhipu" ? "输入智谱AI API密钥..." :
                "输入您的API密钥..."
              }
              style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-primary)',
                color: 'var(--text-primary)',
              }}
            />
            {apiKey && (
              <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                当前: {maskApiKey(apiKey)}
              </p>
            )}
            <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
              您的 API 密钥存储在本地，仅用于调用相应的API服务
            </p>

            {apiProvider === "openrouter" && (
              <div className="mt-2 p-2 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                <p className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>没有 API 密钥？</p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>1. 在 <button
                  onClick={() => openExternalLink('https://openrouter.ai')}
                  className="hover:underline cursor-pointer" style={{ color: 'var(--accent-primary)' }}>OpenRouter</button> 创建账户
                </p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>2. 前往 <button
                  onClick={() => openExternalLink('https://openrouter.ai/keys')}
                  className="hover:underline cursor-pointer" style={{ color: 'var(--accent-primary)' }}>API 密钥</button> 部分
                </p>
                <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>3. 创建新的 API 密钥并粘贴到此处</p>
                <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>4. 您可以免费访问多个开源大语言模型</p>
              </div>
            )}

            {apiProvider === "zhipu" && (
              <div className="mt-2 p-2 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                <p className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>智谱AI API密钥获取：</p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>1. 访问 <button
                  onClick={() => openExternalLink('https://open.bigmodel.cn')}
                  className="hover:underline cursor-pointer" style={{ color: 'var(--accent-primary)' }}>智谱AI开放平台</button>
                </p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>2. 注册并登录账户</p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>3. 在API密钥管理中创建新的密钥</p>
                <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>4. 新用户注册收费token送的多，收费模型也便宜，免费文本模型没啥限制调用次数</p>
              </div>
            )}

            {apiProvider === "custom" && (
              <div className="mt-2 p-2 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                <p className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>自定义API说明：</p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>• 必须支持OpenAI SDK调用格式</p>
                <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>• 需要提供完整的API Base URL</p>
                <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>• 确保API密钥有效且有足够额度</p>
              </div>
            )}
          </div>

          {/* Base URL Input for Custom Provider */}
          {apiProvider === "custom" && (
            <div className="space-y-2">
              <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }} htmlFor="baseUrl">
                API Base URL
              </label>
              <Input
                id="baseUrl"
                type="text"
                value={baseUrl}
                onChange={(e) => setBaseUrl(e.target.value)}
                placeholder="https://api.openai.com/v1"
                style={{
                  backgroundColor: 'var(--bg-secondary)',
                  border: '1px solid var(--border-primary)',
                  color: 'var(--text-primary)',
                }}
              />
              <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                API的基础URL，必须兼容OpenAI SDK格式
              </p>
            </div>
          )}

          {/* Zhipu Thinking Toggle */}
          {apiProvider === "zhipu" && (
            <div className="space-y-2">
              <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                模型推理思考
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="zhipuThinking"
                  checked={zhipuThinkingEnabled}
                  onChange={(e) => setZhipuThinkingEnabled(e.target.checked)}
                  className="w-4 h-4 rounded border-2"
                  style={{
                    backgroundColor: zhipuThinkingEnabled ? 'var(--text-primary)' : 'var(--bg-secondary)',
                    borderColor: 'var(--border-primary)',
                    accentColor: 'var(--text-primary)'
                  }}
                />
                <label htmlFor="zhipuThinking" className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                  开启模型推理思考能力（可能增加响应时间）
                </label>
              </div>
              <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                启用后，支持推理的智谱AI模型将显示详细的思考过程，但会增加响应时间和token消耗
              </p>
            </div>
          )}

          {/* OpenRouter Reasoning Toggle */}
          {apiProvider === "openrouter" && (
            <div className="space-y-2">
              <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                模型推理思考（仅对可控制推理开关模型有效）
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="openrouterReasoning"
                  checked={openrouterReasoningEnabled}
                  onChange={(e) => setOpenrouterReasoningEnabled(e.target.checked)}
                  className="w-4 h-4 rounded border-2"
                  style={{
                    backgroundColor: openrouterReasoningEnabled ? 'var(--text-primary)' : 'var(--bg-secondary)',
                    borderColor: 'var(--border-primary)',
                    accentColor: 'var(--text-primary)'
                  }}
                />
                <label htmlFor="openrouterReasoning" className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                  开启模型推理思考能力（可能增加响应时间）
                </label>
              </div>
              <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                启用后，支持推理的模型将显示详细的思考过程，但会增加响应时间和token消耗
              </p>
            </div>
          )}

          <div className="space-y-2 mt-4">
            <label className="text-sm font-medium mb-2 block" style={{ color: 'var(--text-primary)' }}>键盘快捷键</label>
            <div className="rounded-lg p-3" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
              <div className="grid grid-cols-2 gap-y-2 text-xs">
                <div style={{ color: 'var(--text-tertiary)' }}>切换可见性</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+B / Cmd+B</div>

                <div style={{ color: 'var(--text-tertiary)' }}>切换鼠标穿透</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+N / Cmd+N</div>

                <div style={{ color: 'var(--text-tertiary)' }}>截取屏幕</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+H / Cmd+H</div>

                <div style={{ color: 'var(--text-tertiary)' }}>处理截图</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+Enter / Cmd+Enter</div>

                <div style={{ color: 'var(--text-tertiary)' }}>直接解题</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+Shift+Enter / Cmd+Shift+Enter</div>

                <div style={{ color: 'var(--text-tertiary)' }}>删除最后一张截图</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+L / Cmd+L</div>

                <div style={{ color: 'var(--text-tertiary)' }}>重置视图</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+R / Cmd+R</div>

                <div style={{ color: 'var(--text-tertiary)' }}>退出应用</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+Q / Cmd+Q</div>

                <div style={{ color: 'var(--text-tertiary)' }}>移动窗口</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+方向键</div>

                <div style={{ color: 'var(--text-tertiary)' }}>降低透明度</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+[ / Cmd+[</div>

                <div style={{ color: 'var(--text-tertiary)' }}>提高透明度</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+] / Cmd+]</div>

                <div style={{ color: 'var(--text-tertiary)' }}>缩小</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+- / Cmd+-</div>

                <div style={{ color: 'var(--text-tertiary)' }}>重置缩放</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+0 / Cmd+0</div>

                <div style={{ color: 'var(--text-tertiary)' }}>放大</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+= / Cmd+=</div>

                <div style={{ color: 'var(--text-tertiary)' }}>展开/折叠思考分析</div>
                <div className="font-mono" style={{ color: 'var(--text-secondary)' }}>Ctrl+T / Cmd+T</div>
              </div>
            </div>
          </div>
          
          <div className="space-y-1 mt-4 mb-1 rounded-lg p-3" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
            <h3 className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>模型说明</h3>
            <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>• 问题提取和调试必须使用支持图像分析(多模态)的模型</p>
          </div>

          {/* 提示词语言选择 */}
          <div className="space-y-2 mt-4">
            <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>模型回复语言设置</label>
            <p className="text-xs -mt-1 mb-2" style={{ color: 'var(--text-tertiary)' }}>
              选择AI模型使用的语言，影响AI回复使用的语言
            </p>
            <div className="flex space-x-4">
              <div
                className="flex-1 p-2 rounded-lg cursor-pointer transition-colors"
                style={{
                  backgroundColor: promptLanguage === "chinese" ? 'var(--bg-tertiary)' : 'var(--bg-secondary)',
                  border: `1px solid ${promptLanguage === "chinese" ? 'var(--border-primary)' : 'var(--border-secondary)'}`,
                }}
                onClick={() => setPromptLanguage("chinese")}
                onMouseEnter={(e) => {
                  if (promptLanguage !== "chinese") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (promptLanguage !== "chinese") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: promptLanguage === "chinese" ? 'var(--text-primary)' : 'var(--text-tertiary)',
                    }}
                  />
                  <div className="flex flex-col">
                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>中文</p>
                    <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>AI将以中文回复</p>
                  </div>
                </div>
              </div>
              <div
                className="flex-1 p-2 rounded-lg cursor-pointer transition-colors"
                style={{
                  backgroundColor: promptLanguage === "english" ? 'var(--bg-tertiary)' : 'var(--bg-secondary)',
                  border: `1px solid ${promptLanguage === "english" ? 'var(--border-primary)' : 'var(--border-secondary)'}`,
                }}
                onClick={() => setPromptLanguage("english")}
                onMouseEnter={(e) => {
                  if (promptLanguage !== "english") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (promptLanguage !== "english") {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: promptLanguage === "english" ? 'var(--text-primary)' : 'var(--text-tertiary)',
                    }}
                  />
                  <div className="flex flex-col">
                    <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>英文</p>
                    <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>AI将以英文回复</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4 mt-4">
            <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>AI 模型选择</label>
            <p className="text-xs -mt-3 mb-2" style={{ color: 'var(--text-tertiary)' }}>
              选择用于流程各阶段的模型
            </p>
            
            {modelCategories.map((category) => {
              // Get models based on current API provider and filter them for the current category
              let models = filterModels(category, apiProvider);
              const selectOptions = convertToSelectOptions(models);

              // Determine which state to use based on category key
              const currentValue =
                category.key === 'extractionModel' ? extractionModel :
                category.key === 'solutionModel' ? solutionModel :
                debuggingModel;

              // Get validation state for this category
              const validation = modelValidation[category.key];
              const validationError = validation?.result && !validation.result.isValid
                ? validation.result.error
                : undefined;

              return (
                <div key={category.key} className="mb-4">
                  <label className="text-sm font-medium mb-1 block" style={{ color: 'var(--text-primary)' }}>
                    {category.title}
                  </label>
                  <p className="text-xs mb-2" style={{ color: 'var(--text-tertiary)' }}>{category.description}</p>

                  {apiProvider === "custom" ? (
                    <Input
                      type="text"
                      value={currentValue}
                      onChange={(e) => handleModelChange(category.key, e.target.value, category.requiresImages)}
                      placeholder={
                        category.key === 'extractionModel' ? "gpt-4-vision-preview" :
                        category.key === 'solutionModel' ? "gpt-4-turbo" :
                        "gpt-4-vision-preview"
                      }
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        border: '1px solid var(--border-primary)',
                        color: 'var(--text-primary)',
                      }}
                    />
                  ) : (
                    <Select
                      value={currentValue}
                      onValueChange={(value) => handleModelChange(category.key, value, category.requiresImages)}
                      options={selectOptions}
                      placeholder={`选择${category.title}模型...`}
                      allowCustom={true}
                      customPlaceholder="输入自定义模型ID..."
                      isValidating={validation?.isValidating || false}
                      validationError={validationError}
                      className="w-full"
                    />
                  )}

                  {validation?.result?.isValid && (
                    <div className="mt-2 space-y-1">
                      <div className="text-xs" style={{ color: '#10b981' }}>
                        ✓ 模型验证通过
                        {category.requiresImages && validation.result.supportsImages && " (支持图像分析)"}
                      </div>
                      {validation.result.modelInfo && (
                        <div className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                          {validation.result.modelInfo.name}
                          {validation.result.endpoints && validation.result.endpoints.length > 0 && (
                            <span className="ml-2">• {validation.result.endpoints.length} 个可用端点</span>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            style={{
              border: '1px solid var(--border-primary)',
              backgroundColor: 'transparent',
              color: 'var(--text-primary)',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            取消
          </Button>
          <Button
            className="px-4 py-3 rounded-xl font-medium transition-colors"
            style={{
              backgroundColor: 'var(--text-primary)',
              color: 'var(--bg-primary)',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.9';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1';
            }}
            onClick={handleSave}
            disabled={isLoading || !apiKey}
          >
            {isLoading ? "保存中..." : "保存设置"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
