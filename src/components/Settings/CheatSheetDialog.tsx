import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { useToast } from "../../contexts/toast";
import { useTheme } from "../../contexts/theme";
import { CheatSheetDocument } from "../../types/cheatsheet";

interface CheatSheetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CheatSheetDialog: React.FC<CheatSheetDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { showToast } = useToast();
  const { theme } = useTheme();
  const [documents, setDocuments] = useState<CheatSheetDocument[]>([]);
  const [localDirectories, setLocalDirectories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newMarkdownName, setNewMarkdownName] = useState('');
  const [newMarkdownContent, setNewMarkdownContent] = useState('');
  const [showAddMarkdown, setShowAddMarkdown] = useState(false);

  // 加载数据
  useEffect(() => {
    if (open) {
      loadData();
    }
  }, [open]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [docs, dirs] = await Promise.all([
        window.electronAPI.cheatSheetGetDocuments(),
        window.electronAPI.cheatSheetGetLocalDirectories()
      ]);
      setDocuments(docs || []);
      setLocalDirectories(dirs || []);
    } catch (error) {
      console.error('加载小抄数据失败:', error);
      showToast('错误', '加载小抄数据失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 添加Markdown文档
  const handleAddMarkdown = async () => {
    if (!newMarkdownName.trim() || !newMarkdownContent.trim()) {
      showToast('错误', '请填写文档名称和内容', 'error');
      return;
    }

    try {
      setIsLoading(true);
      await window.electronAPI.cheatSheetCreateMarkdown(newMarkdownName.trim(), newMarkdownContent);
      showToast('成功', 'Markdown文档创建成功', 'success');
      setNewMarkdownName('');
      setNewMarkdownContent('');
      setShowAddMarkdown(false);
      await loadData();
    } catch (error: any) {
      console.error('创建Markdown文档失败:', error);
      showToast('错误', error.message || '创建Markdown文档失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 选择并添加本地文件
  const handleAddLocalFile = async () => {
    try {
      setIsLoading(true);
      const filePath = await window.electronAPI.cheatSheetSelectFile();
      if (filePath) {
        await window.electronAPI.cheatSheetAddLocalFile(filePath);
        showToast('成功', '本地文件添加成功', 'success');
        await loadData();
      }
    } catch (error: any) {
      console.error('添加本地文件失败:', error);
      showToast('错误', error.message || '添加本地文件失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 选择并添加本地目录
  const handleAddLocalDirectory = async () => {
    try {
      setIsLoading(true);
      const directory = await window.electronAPI.cheatSheetSelectDirectory();
      if (directory) {
        // 先扫描目录中的文件
        const scannedDocuments = await window.electronAPI.cheatSheetScanDirectory(directory);
        console.log('扫描到的文件:', scannedDocuments);

        if (scannedDocuments.length === 0) {
          showToast('提示', '该目录中没有找到支持的文件（.md, .pdf, .txt）', 'neutral');
        } else {
          // 将扫描到的文档添加到文档列表
          let addedCount = 0;
          let skippedCount = 0;

          for (const doc of scannedDocuments) {
            try {
              await window.electronAPI.cheatSheetAddLocalFile(doc.filePath!);
              addedCount++;
            } catch (error: any) {
              if (error.message && error.message.includes('文档已存在')) {
                skippedCount++;
                console.log('文档已存在，跳过:', doc.filePath);
              } else {
                console.error('添加文件失败:', doc.filePath, error);
              }
            }
          }

          let message = `扫描完成：添加了 ${addedCount} 个文件`;
          if (skippedCount > 0) {
            message += `，跳过了 ${skippedCount} 个已存在的文件`;
          }
          showToast('成功', message, 'success');
        }

        // 将目录添加到配置中（用于后续扫描）
        await window.electronAPI.cheatSheetAddLocalDirectory(directory);
        await loadData();
      }
    } catch (error: any) {
      console.error('添加本地目录失败:', error);
      showToast('错误', error.message || '添加本地目录失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 删除文档
  const handleDeleteDocument = async (id: string) => {
    try {
      setIsLoading(true);
      const success = await window.electronAPI.cheatSheetDeleteDocument(id);
      if (success) {
        showToast('成功', '文档删除成功', 'success');
        await loadData();
      } else {
        showToast('错误', '文档删除失败', 'error');
      }
    } catch (error) {
      console.error('删除文档失败:', error);
      showToast('错误', '删除文档失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 删除本地目录
  const handleRemoveLocalDirectory = async (directory: string) => {
    try {
      setIsLoading(true);
      await window.electronAPI.cheatSheetRemoveLocalDirectory(directory);
      showToast('成功', '本地目录删除成功', 'success');
      await loadData();
    } catch (error) {
      console.error('删除本地目录失败:', error);
      showToast('错误', '删除本地目录失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 重新扫描本地目录
  const handleRescanDirectory = async (directory: string) => {
    try {
      setIsLoading(true);
      const scannedDocuments = await window.electronAPI.cheatSheetScanDirectory(directory);
      console.log('重新扫描到的文件:', scannedDocuments);

      if (scannedDocuments.length === 0) {
        showToast('提示', '该目录中没有找到支持的文件', 'neutral');
      } else {
        // 将扫描到的文档添加到文档列表
        let addedCount = 0;
        let skippedCount = 0;

        for (const doc of scannedDocuments) {
          try {
            await window.electronAPI.cheatSheetAddLocalFile(doc.filePath!);
            addedCount++;
          } catch (error: any) {
            if (error.message && error.message.includes('文档已存在')) {
              skippedCount++;
              console.log('文档已存在，跳过:', doc.filePath);
            } else {
              console.error('添加文件失败:', doc.filePath, error);
            }
          }
        }

        let message = `重新扫描完成：添加了 ${addedCount} 个新文件`;
        if (skippedCount > 0) {
          message += `，跳过了 ${skippedCount} 个已存在的文件`;
        }
        showToast('成功', message, 'success');
      }

      await loadData();
    } catch (error: any) {
      console.error('重新扫描目录失败:', error);
      showToast('错误', error.message || '重新扫描目录失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={`max-w-4xl max-h-[80vh] overflow-y-auto ${
          theme === 'dark' 
            ? 'theme-bg-primary theme-border-primary' 
            : 'bg-white border-gray-200'
        }`}
      >
        <DialogHeader>
          <DialogTitle className="theme-text-primary">小抄设置</DialogTitle>
          <DialogDescription className="theme-text-secondary">
            管理您的小抄文档和本地目录配置。支持Markdown、PDF和文本文件。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* 添加文档区域 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium theme-text-primary">添加文档</h3>
            
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setShowAddMarkdown(!showAddMarkdown)}
                disabled={isLoading}
                className="px-4 py-2 theme-bg-secondary theme-text-primary theme-border-primary border rounded-lg hover:theme-bg-tertiary transition-colors disabled:opacity-50"
              >
                添加Markdown文档
              </button>
              
              <button
                onClick={handleAddLocalFile}
                disabled={isLoading}
                className="px-4 py-2 theme-bg-secondary theme-text-primary theme-border-primary border rounded-lg hover:theme-bg-tertiary transition-colors disabled:opacity-50"
              >
                添加本地文件
              </button>
              
              <button
                onClick={handleAddLocalDirectory}
                disabled={isLoading}
                className="px-4 py-2 theme-bg-secondary theme-text-primary theme-border-primary border rounded-lg hover:theme-bg-tertiary transition-colors disabled:opacity-50"
              >
                添加本地目录
              </button>
            </div>

            {/* 添加Markdown表单 */}
            {showAddMarkdown && (
              <div className="space-y-3 p-4 theme-bg-secondary rounded-lg theme-border-primary border">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-1">
                    文档名称
                  </label>
                  <input
                    type="text"
                    value={newMarkdownName}
                    onChange={(e) => setNewMarkdownName(e.target.value)}
                    placeholder="输入文档名称"
                    className="w-full px-3 py-2 theme-bg-primary theme-text-primary theme-border-primary border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-1">
                    Markdown内容
                  </label>
                  <textarea
                    value={newMarkdownContent}
                    onChange={(e) => setNewMarkdownContent(e.target.value)}
                    placeholder="输入Markdown内容"
                    rows={8}
                    className="w-full px-3 py-2 theme-bg-primary theme-text-primary theme-border-primary border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
                  />
                </div>
                
                <div className="flex gap-2">
                  <button
                    onClick={handleAddMarkdown}
                    disabled={isLoading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    添加文档
                  </button>
                  
                  <button
                    onClick={() => setShowAddMarkdown(false)}
                    className="px-4 py-2 theme-bg-tertiary theme-text-primary rounded-lg hover:theme-bg-secondary transition-colors"
                  >
                    取消
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 文档列表 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium theme-text-primary">文档列表</h3>
            
            {documents.length === 0 ? (
              <p className="theme-text-secondary text-center py-8">
                暂无文档，请添加一些小抄文档
              </p>
            ) : (
              <div className="space-y-2">
                {documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between p-3 theme-bg-secondary rounded-lg theme-border-primary border"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium theme-text-primary">{doc.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded ${
                          doc.type === 'pdf'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : doc.type === 'text'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        }`}>
                          {doc.type === 'pdf' ? 'PDF' : doc.type === 'text' ? 'Text' : 'Markdown'}
                        </span>
                      </div>
                      <div className="text-sm theme-text-secondary mt-1">
                        大小: {formatFileSize(doc.size)} | 创建时间: {formatDate(doc.createdAt)}
                        {doc.filePath && (
                          <div className="truncate mt-1">路径: {doc.filePath}</div>
                        )}
                      </div>
                    </div>
                    
                    <button
                      onClick={() => handleDeleteDocument(doc.id)}
                      disabled={isLoading}
                      className="px-3 py-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded transition-colors disabled:opacity-50"
                    >
                      删除
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 本地目录列表 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium theme-text-primary">本地目录</h3>
            
            {localDirectories.length === 0 ? (
              <p className="theme-text-secondary text-center py-4">
                暂无配置的本地目录
              </p>
            ) : (
              <div className="space-y-2">
                {localDirectories.map((dir, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 theme-bg-secondary rounded-lg theme-border-primary border"
                  >
                    <div className="flex-1">
                      <div className="theme-text-primary font-mono text-sm truncate">
                        {dir}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <button
                        onClick={() => handleRescanDirectory(dir)}
                        disabled={isLoading}
                        className="px-3 py-1 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900 rounded transition-colors disabled:opacity-50"
                      >
                        重新扫描
                      </button>

                      <button
                        onClick={() => handleRemoveLocalDirectory(dir)}
                        disabled={isLoading}
                        className="px-3 py-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded transition-colors disabled:opacity-50"
                      >
                        删除
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 使用说明 */}
          <div className="space-y-2 p-4 theme-bg-secondary rounded-lg theme-border-primary border">
            <h4 className="font-medium theme-text-primary">使用说明</h4>
            <ul className="text-sm theme-text-secondary space-y-1">
              <li>• 使用 <kbd className={`px-1 py-0.5 rounded text-xs ${
                theme === 'dark'
                  ? 'bg-gray-700 text-gray-200'
                  : 'bg-gray-200 text-gray-800'
              }`}>Ctrl+Alt+C</kbd> 打开小抄模式</li>
              <li>• 使用 <kbd className={`px-1 py-0.5 rounded text-xs ${
                theme === 'dark'
                  ? 'bg-gray-700 text-gray-200'
                  : 'bg-gray-200 text-gray-800'
              }`}>Ctrl+Alt+←</kbd> / <kbd className={`px-1 py-0.5 rounded text-xs ${
                theme === 'dark'
                  ? 'bg-gray-700 text-gray-200'
                  : 'bg-gray-200 text-gray-800'
              }`}>Ctrl+Alt+→</kbd> 翻页</li>
              <li>• 支持Markdown、PDF和文本文件，单个文件最大20MB</li>
              <li>• 小抄模式需要Ultra版本权限</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CheatSheetDialog;
