import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog';
import { useToast } from '../../contexts/toast';

interface CustomPromptsConfig {
  directAnswer: {
    extraction: {
      userPrompt: string;
    };
    debugging: {
      userPrompt: string;
    };
  };
  voiceStreaming: {
    solution: {
      systemPrompt: string;
    };
  };
}

interface CustomPromptsDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function CustomPromptsDialog({ open: externalOpen, onOpenChange }: CustomPromptsDialogProps) {
  const [open, setOpen] = useState(externalOpen || false);
  const [config, setConfig] = useState<CustomPromptsConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'direct' | 'voice'>('direct');
  const { showToast } = useToast();
  const dialogRef = useRef<HTMLDivElement>(null);

  // 同步外部open状态
  useEffect(() => {
    if (externalOpen !== undefined) {
      setOpen(externalOpen);
    }
  }, [externalOpen]);

  // 处理open状态变化
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (onOpenChange && newOpen !== externalOpen) {
      onOpenChange(newOpen);
    }
  };

  // 加载配置
  useEffect(() => {
    if (open) {
      loadConfig();
    }
  }, [open]);

  const loadConfig = async () => {
    setIsLoading(true);
    try {
      const result = await window.electronAPI.getCustomPromptsConfig();
      if (result) {
        setConfig(result);
      }
    } catch (error) {
      console.error("Failed to load custom prompts config:", error);
      showToast("错误", "加载自定义提示词配置失败", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // 保存配置
  const handleSave = async () => {
    if (!config) return;
    
    setIsLoading(true);
    try {
      const result = await window.electronAPI.updateCustomPromptsConfig(config);
      
      if (result) {
        showToast("成功", "自定义提示词配置保存成功", "success");
        handleOpenChange(false);
      }
    } catch (error) {
      console.error("Failed to save custom prompts config:", error);
      showToast("错误", "保存自定义提示词配置失败", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // 重置为中文默认配置
  const handleResetToChinese = async () => {
    try {
      const defaultConfig = await window.electronAPI.getDefaultCustomPromptsConfig('chinese');
      if (defaultConfig) {
        setConfig(defaultConfig);
        showToast("成功", "已重置为中文默认提示词", "success");
      }
    } catch (error) {
      console.error("Failed to reset to Chinese default config:", error);
      showToast("错误", "重置失败", "error");
    }
  };

  // 重置为英文默认配置
  const handleResetToEnglish = async () => {
    try {
      const defaultConfig = await window.electronAPI.getDefaultCustomPromptsConfig('english');
      if (defaultConfig) {
        setConfig(defaultConfig);
        showToast("成功", "已重置为英文默认提示词", "success");
      }
    } catch (error) {
      console.error("Failed to reset to English default config:", error);
      showToast("错误", "重置失败", "error");
    }
  };

  // 更新配置
  const updateConfig = (path: string[], value: string) => {
    if (!config) return;
    
    const newConfig = JSON.parse(JSON.stringify(config));
    let current = newConfig;
    
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }
    current[path[path.length - 1]] = value;
    
    setConfig(newConfig);
  };

  if (!config) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className="sm:max-w-6xl"
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'min(1200px, 95vw)',
          maxHeight: '90vh',
          overflowY: 'auto',
          zIndex: 9999,
          margin: 0,
          padding: '20px',
          transition: 'all 0.25s ease',
          opacity: 0.98,
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-primary)',
          color: 'var(--text-primary)',
        }}
        ref={dialogRef}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2" style={{ color: 'var(--text-primary)' }}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
              <polyline points="14,2 14,8 20,8" />
              <line x1="16" y1="13" x2="8" y2="13" />
              <line x1="16" y1="17" x2="8" y2="17" />
              <polyline points="10,9 9,9 8,9" />
            </svg>
            自定义提示词设置
          </DialogTitle>
          <DialogDescription style={{ color: 'var(--text-tertiary)' }}>
            配置您的自定义提示词。您可以使用系统变量如 ${'{language}'}。
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* 标签页导航 */}
          <div className="flex space-x-1 p-1 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
            <button
              className="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors"
              style={{
                backgroundColor: activeTab === 'direct' ? 'var(--bg-tertiary)' : 'transparent',
                color: activeTab === 'direct' ? 'var(--text-primary)' : 'var(--text-tertiary)',
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'direct') {
                  e.currentTarget.style.color = 'var(--text-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'direct') {
                  e.currentTarget.style.color = 'var(--text-tertiary)';
                }
              }}
              onClick={() => setActiveTab('direct')}
            >
              直接解答
            </button>
            <button
              className="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors"
              style={{
                backgroundColor: activeTab === 'voice' ? 'var(--bg-tertiary)' : 'transparent',
                color: activeTab === 'voice' ? 'var(--text-primary)' : 'var(--text-tertiary)',
              }}
              onMouseEnter={(e) => {
                if (activeTab !== 'voice') {
                  e.currentTarget.style.color = 'var(--text-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== 'voice') {
                  e.currentTarget.style.color = 'var(--text-tertiary)';
                }
              }}
              onClick={() => setActiveTab('voice')}
            >
              语音模式流式解答
            </button>
          </div>



          {/* 直接解答配置 */}
          {activeTab === 'direct' && (
            <div className="space-y-6">
              {/* 提取模型 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>提取模型</h3>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>
                    User Prompt
                  </label>
                  <textarea
                    value={config.directAnswer.extraction.userPrompt}
                    onChange={(e) => updateConfig(['directAnswer', 'extraction', 'userPrompt'], e.target.value)}
                    className="w-full h-64 px-3 py-2 rounded-md resize-y focus:outline-none focus:ring-2 theme-textarea"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    } as React.CSSProperties}
                    placeholder="输入用户提示词..."
                  />
                </div>
              </div>

              {/* 调试模型 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>调试模型</h3>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>
                    User Prompt
                  </label>
                  <textarea
                    value={config.directAnswer.debugging.userPrompt}
                    onChange={(e) => updateConfig(['directAnswer', 'debugging', 'userPrompt'], e.target.value)}
                    className="w-full h-64 px-3 py-2 rounded-md resize-y focus:outline-none focus:ring-2 theme-textarea"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    } as React.CSSProperties}
                    placeholder="输入用户提示词..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* 语音模式流式解答配置 */}
          {activeTab === 'voice' && (
            <div className="space-y-6">
              {/* 解决方案模型 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>解决方案模型</h3>
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>
                    System Prompt
                  </label>
                  <textarea
                    value={config.voiceStreaming.solution.systemPrompt}
                    onChange={(e) => updateConfig(['voiceStreaming', 'solution', 'systemPrompt'], e.target.value)}
                    className="w-full h-56 px-3 py-2 rounded-md resize-y focus:outline-none focus:ring-2 theme-textarea"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    } as React.CSSProperties}
                    placeholder="输入系统提示词..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* 变量说明 */}
          <div className="mt-6 p-4 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
            <h4 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>可用的系统变量：</h4>
            <div className="text-xs space-y-1" style={{ color: 'var(--text-tertiary)' }}>
              <p><code className="px-1 rounded" style={{ backgroundColor: 'var(--bg-tertiary)' }}>${'{language}'}</code> - 当前选择的编程语言</p>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-between pt-4" style={{ borderTop: '1px solid var(--border-primary)' }}>
          <div className="flex gap-2">
            <button
              onClick={handleResetToChinese}
              disabled={isLoading}
              className="px-3 py-2 text-sm rounded-md transition-colors disabled:opacity-50"
              style={{
                backgroundColor: 'var(--bg-tertiary)',
                color: 'var(--text-primary)',
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                }
              }}
            >
              重置为中文模板
            </button>
            <button
              onClick={handleResetToEnglish}
              disabled={isLoading}
              className="px-3 py-2 text-sm rounded-md transition-colors disabled:opacity-50"
              style={{
                backgroundColor: 'var(--bg-tertiary)',
                color: 'var(--text-primary)',
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                }
              }}
            >
              重置为英文模板
            </button>
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => handleOpenChange(false)}
              disabled={isLoading}
              className="px-4 py-2 text-sm rounded-md transition-colors disabled:opacity-50"
              style={{
                backgroundColor: 'var(--bg-tertiary)',
                color: 'var(--text-primary)',
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                }
              }}
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-4 py-2 text-sm rounded-md transition-colors disabled:opacity-50"
              style={{
                backgroundColor: 'var(--text-primary)',
                color: 'var(--bg-primary)',
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.opacity = '0.9';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.opacity = '1';
                }
              }}
            >
              {isLoading ? "保存中..." : "保存"}
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
