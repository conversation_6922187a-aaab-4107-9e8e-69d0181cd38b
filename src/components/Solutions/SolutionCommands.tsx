import React, { useState, useEffect, useRef } from "react"
import { createPortal } from "react-dom"
import { useToast } from "../../contexts/toast"
import { Screenshot } from "../../types/screenshots"
import { supabase } from "../../lib/supabase"
import { LanguageSelector } from "../shared/LanguageSelector"
import { VoiceSettingsDialog } from "../Settings/VoiceSettingsDialog"
import { CustomPromptsDialog } from "../Settings/CustomPromptsDialog"
import ActivationStatus from "../ui/ActivationStatus"
import { ThemeSwitch } from "../ui/ThemeSwitch"
import { COMMAND_KEY } from "../../utils/platform"

export interface SolutionCommandsProps {
  onTooltipVisibilityChange: (visible: boolean, height: number) => void
  isProcessing: boolean
  screenshots?: Screenshot[]
  extraScreenshots?: Screenshot[]
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const handleSignOut = async () => {
  try {
    // Clear any local storage or electron-specific data first
    localStorage.clear()
    sessionStorage.clear()

    // Then sign out from Supabase
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  } catch (err) {
    console.error("Error signing out:", err)
  }
}

const SolutionCommands: React.FC<SolutionCommandsProps> = ({
  onTooltipVisibilityChange,
  isProcessing,
  extraScreenshots = [],
  credits,
  currentLanguage,
  setLanguage
}) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })
  const [voiceSettingsOpen, setVoiceSettingsOpen] = useState(false)
  const [customPromptsOpen, setCustomPromptsOpen] = useState(false)
  const [isUltraVersion, setIsUltraVersion] = useState(false)

  const tooltipRef = useRef<HTMLDivElement>(null)
  const gearIconRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  useEffect(() => {
    if (onTooltipVisibilityChange) {
      let tooltipHeight = 0
      if (tooltipRef.current && isTooltipVisible) {
        tooltipHeight = tooltipRef.current.offsetHeight + 10 // Adjust if necessary
      }
      onTooltipVisibilityChange(isTooltipVisible, tooltipHeight)
    }
  }, [isTooltipVisible, onTooltipVisibilityChange])

  // 检查Ultra版本权限
  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const ultraResult = await window.electronAPI.isUltraVersion();
        setIsUltraVersion(ultraResult);
      } catch (error) {
        console.error('检查权限失败:', error);
      }
    };

    checkPermissions();
  }, [])

  const handleMouseEnter = () => {
    if (gearIconRef.current) {
      const rect = gearIconRef.current.getBoundingClientRect()
      setTooltipPosition({
        top: rect.bottom + 8,
        left: rect.right - 320 // 320px is the tooltip width (w-80)
      })
    }
    setIsTooltipVisible(true)
  }

  const handleMouseLeave = () => {
    setIsTooltipVisible(false)
  }

  return (
    <div>
      <div className="pt-2 w-fit">
        <div className="text-xs theme-text-primary backdrop-blur-md theme-bg-secondary rounded-lg py-2 px-4 flex items-center justify-center gap-4">
          {/* Show/Hide - Always visible */}
          <div
            className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:theme-bg-tertiary transition-colors"
            onClick={async () => {
              try {
                const result = await window.electronAPI.toggleMainWindow()
                if (!result.success) {
                  console.error("Failed to toggle window:", result.error)
                  showToast("错误", "切换窗口失败", "error")
                }
              } catch (error) {
                console.error("Error toggling window:", error)
                showToast("错误", "切换窗口失败", "error")
              }
            }}
          >
            <span className="text-[11px] leading-none">显示/隐藏</span>
            <div className="flex gap-1">
              <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                {COMMAND_KEY}
              </button>
              <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                B
              </button>
            </div>
          </div>

          {/* Screenshot and Debug commands - Only show if not processing */}
          {!isProcessing && (
            <>
              <div
                className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                onClick={async () => {
                  try {
                    const result = await window.electronAPI.triggerScreenshot()
                    if (!result.success) {
                      console.error("Failed to take screenshot:", result.error)
                      showToast("错误", "截图失败", "error")
                    }
                  } catch (error) {
                    console.error("Error taking screenshot:", error)
                    showToast("错误", "截图失败", "error")
                  }
                }}
              >
                <span className="text-[11px] leading-none truncate">
                  {extraScreenshots.length === 0
                    ? "截图你的代码"
                    : "截图"}
                </span>
                <div className="flex gap-1">
                  <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                    {COMMAND_KEY}
                  </button>
                  <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                    H
                  </button>
                </div>
              </div>

              {extraScreenshots.length > 0 && (
                <>
                  <div
                    className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:theme-bg-tertiary transition-colors"
                    onClick={async () => {
                      try {
                        const result =
                          await window.electronAPI.triggerProcessScreenshots()
                        if (!result.success) {
                          console.error(
                            "Failed to process screenshots:",
                            result.error
                          )
                          showToast(
                            "错误",
                            "处理截图失败",
                            "error"
                          )
                        }
                      } catch (error) {
                        console.error("Error processing screenshots:", error)
                        showToast(
                          "错误",
                          "处理截图失败",
                          "error"
                        )
                      }
                    }}
                  >
                    <span className="text-[11px] leading-none">调试</span>
                    <div className="flex gap-1">
                      <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                        {COMMAND_KEY}
                      </button>
                      <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                        ↵
                      </button>
                    </div>
                  </div>

                  <div
                    className="flex flex-col cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                    onClick={async () => {
                      // 检查Ultra权限
                      try {
                        const isUltra = await window.electronAPI.isUltraVersion()
                        if (!isUltra) {
                          showToast(
                            "权限不足",
                            "直接调试功能需要Ultra版本权限，请升级到Ultra版本",
                            "error"
                          )
                          return
                        }
                      } catch (error) {
                        console.error("Error checking Ultra permission:", error)
                        showToast("错误", "权限检查失败", "error")
                        return
                      }

                      try {
                        const result =
                          await window.electronAPI.triggerDirectAnswer()
                        if (!result.success) {
                          console.error(
                            "Failed to process direct debug:",
                            result.error
                          )
                          showToast(
                            "错误",
                            "直接调试失败",
                            "error"
                          )
                        }
                      } catch (error) {
                        console.error("Error processing direct debug:", error)
                        showToast(
                          "错误",
                          "直接调试失败",
                          "error"
                        )
                      }
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-[11px] leading-none">直接调试</span>
                      <div className="flex gap-1 ml-2">
                        <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                          {COMMAND_KEY}
                        </button>
                        <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                          Shift
                        </button>
                        <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                          ↵
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </>
          )}

          {/* Start Over - Always visible */}
          <div
            className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:theme-bg-tertiary transition-colors"
            onClick={async () => {
              try {
                const result = await window.electronAPI.triggerReset()
                if (!result.success) {
                  console.error("Failed to reset:", result.error)
                  showToast("错误", "重置失败", "error")
                }
              } catch (error) {
                console.error("Error resetting:", error)
                showToast("错误", "重置失败", "error")
              }
            }}
          >
            <span className="text-[11px] leading-none">重新开始</span>
            <div className="flex gap-1">
              <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                {COMMAND_KEY}
              </button>
              <button className="theme-bg-tertiary rounded-md px-1.5 py-1 text-[11px] leading-none theme-text-secondary">
                R
              </button>
            </div>
          </div>

          {/* Activation Status */}
          <ActivationStatus />

          {/* Separator */}
          <div className="mx-2 h-4 w-px theme-border-primary" />

          {/* Theme Switch */}
          <ThemeSwitch className="scale-75" />

          {/* Separator */}
          <div className="mx-2 h-4 w-px theme-border-primary" />

          {/* Settings with Tooltip */}
          <div
            className="relative inline-block"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {/* Gear icon */}
            <div
              ref={gearIconRef}
              className="w-4 h-4 flex items-center justify-center cursor-pointer theme-text-secondary hover:theme-text-primary transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-3.5 h-3.5"
              >
                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </div>

            {/* Tooltip Content - Rendered via Portal */}
            {isTooltipVisible && createPortal(
              <div
                ref={tooltipRef}
                className="fixed w-80"
                style={{
                  top: tooltipPosition.top,
                  left: tooltipPosition.left,
                  zIndex: 1000
                }}
              >
                {/* Add transparent bridge */}
                <div className="absolute -top-2 right-0 w-full h-2" />
                <div className="p-3 text-xs theme-bg-secondary backdrop-blur-md rounded-lg border theme-border-primary theme-text-primary shadow-lg overflow-visible">
                  <div className="space-y-4">
                    <h3 className="font-medium whitespace-nowrap">
                      键盘快捷键
                    </h3>
                    <div className="space-y-3">
                      {/* Show/Hide - Always visible */}
                      <div
                        className="cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                        onClick={async () => {
                          try {
                            const result =
                              await window.electronAPI.toggleMainWindow()
                            if (!result.success) {
                              console.error(
                                "Failed to toggle window:",
                                result.error
                              )
                              showToast(
                                "错误",
                                "切换窗口失败",
                                "error"
                              )
                            }
                          } catch (error) {
                            console.error("Error toggling window:", error)
                            showToast(
                              "错误",
                              "切换窗口失败",
                              "error"
                            )
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <span className="truncate">显示/隐藏窗口</span>
                          <div className="flex gap-1 flex-shrink-0">
                            <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                              {COMMAND_KEY}
                            </span>
                            <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                              B
                            </span>
                          </div>
                        </div>
                        <p className="text-[10px] leading-relaxed theme-text-tertiary truncate mt-1">
                          显示或隐藏此窗口。
                        </p>
                      </div>

                      {/* Screenshot and Debug commands - Only show if not processing */}
                      {!isProcessing && (
                        <>
                          <div
                            className="cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                            onClick={async () => {
                              try {
                                const result =
                                  await window.electronAPI.triggerScreenshot()
                                if (!result.success) {
                                  console.error(
                                    "Failed to take screenshot:",
                                    result.error
                                  )
                                  showToast(
                                    "错误",
                                    "截图失败",
                                    "error"
                                  )
                                }
                              } catch (error) {
                                console.error("Error taking screenshot:", error)
                                showToast(
                                  "错误",
                                  "截图失败",
                                  "error"
                                )
                              }
                            }}
                          >
                            <div className="flex items-center justify-between">
                              <span className="truncate">截图</span>
                              <div className="flex gap-1 flex-shrink-0">
                                <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                  {COMMAND_KEY}
                                </span>
                                <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                  H
                                </span>
                              </div>
                            </div>
                            <p className="text-[10px] leading-relaxed theme-text-tertiary truncate mt-1">
                              拍摄问题或代码的额外部分以获取调试帮助。
                            </p>
                          </div>

                          {extraScreenshots.length > 0 && (
                            <>
                              <div
                                className="cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                                onClick={async () => {
                                  try {
                                    const result =
                                      await window.electronAPI.triggerProcessScreenshots()
                                    if (!result.success) {
                                      console.error(
                                        "Failed to process screenshots:",
                                        result.error
                                      )
                                      showToast(
                                        "错误",
                                        "处理截图失败",
                                        "error"
                                      )
                                    }
                                  } catch (error) {
                                    console.error(
                                      "Error processing screenshots:",
                                      error
                                    )
                                    showToast(
                                      "错误",
                                      "处理截图失败",
                                      "error"
                                    )
                                  }
                                }}
                              >
                                <div className="flex items-center justify-between">
                                  <span className="truncate">调试</span>
                                  <div className="flex gap-1 flex-shrink-0">
                                    <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                      {COMMAND_KEY}
                                    </span>
                                    <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                      ↵
                                    </span>
                                  </div>
                                </div>
                                <p className="text-[10px] leading-relaxed theme-text-tertiary truncate mt-1">
                                  根据所有先前和新添加的截图生成新的解决方案。
                                </p>
                              </div>

                              <div
                                className="cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                                onClick={async () => {
                                  // 检查Ultra权限
                                  try {
                                    const isUltra = await window.electronAPI.isUltraVersion()
                                    if (!isUltra) {
                                      showToast(
                                        "权限不足",
                                        "直接调试功能需要Ultra版本权限，请升级到Ultra版本",
                                        "error"
                                      )
                                      return
                                    }
                                  } catch (error) {
                                    console.error("Error checking Ultra permission:", error)
                                    showToast("错误", "权限检查失败", "error")
                                    return
                                  }

                                  try {
                                    const result =
                                      await window.electronAPI.triggerDirectAnswer()
                                    if (!result.success) {
                                      console.error(
                                        "Failed to process direct debug:",
                                        result.error
                                      )
                                      showToast(
                                        "错误",
                                        "直接调试失败",
                                        "error"
                                      )
                                    }
                                  } catch (error) {
                                    console.error(
                                      "Error processing direct debug:",
                                      error
                                    )
                                    showToast(
                                      "错误",
                                      "直接调试失败",
                                      "error"
                                    )
                                  }
                                }}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex flex-col">
                                    <span className="truncate">直接调试</span>
                                  </div>
                                  <div className="flex gap-1 flex-shrink-0">
                                    <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                      {COMMAND_KEY}
                                    </span>
                                    <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                      Shift
                                    </span>
                                    <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                                      ↵
                                    </span>
                                  </div>
                                </div>
                                <p className="text-[10px] leading-relaxed theme-text-tertiary truncate mt-1">
                                  基于所有截图直接进行调试分析，无需问题提取。
                                </p>
                              </div>
                            </>
                          )}
                        </>
                      )}

                      {/* Start Over - Always visible */}
                      <div
                        className="cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                        onClick={async () => {
                          try {
                            const result =
                              await window.electronAPI.triggerReset()
                            if (!result.success) {
                              console.error("Failed to reset:", result.error)
                              showToast("错误", "重置失败", "error")
                            }
                          } catch (error) {
                            console.error("Error resetting:", error)
                            showToast("错误", "重置失败", "error")
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <span className="truncate">重新开始</span>
                          <div className="flex gap-1 flex-shrink-0">
                            <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                              {COMMAND_KEY}
                            </span>
                            <span className="theme-bg-tertiary px-1.5 py-0.5 rounded text-[10px] leading-none theme-text-secondary">
                              R
                            </span>
                          </div>
                        </div>
                        <p className="text-[10px] leading-relaxed theme-text-tertiary truncate mt-1">
                          开始新的问题。
                        </p>
                      </div>
                    </div>

                    {/* Separator and Log Out */}
                    <div className="pt-3 mt-3 border-t border-white/10">
                      <LanguageSelector
                        currentLanguage={currentLanguage}
                        setLanguage={setLanguage}
                        onLanguageSelected={() => setIsTooltipVisible(false)}
                      />

                      {/* API Settings */}
                      <div className="mb-3 px-2 space-y-2">
                        <div className="flex items-center justify-between text-[13px] font-medium theme-text-primary">
                          <span>大模型 API 设置</span>
                          <button
                            className="theme-bg-tertiary hover:theme-bg-secondary px-2 py-1 rounded text-[11px] theme-text-primary"
                            onClick={() => window.electronAPI.openSettingsPortal()}
                          >
                            设置
                          </button>
                        </div>

                        <div className="flex items-center justify-between text-[13px] font-medium theme-text-primary">
                          <span>语音识别 API 设置</span>
                          <button
                            className="theme-bg-tertiary hover:theme-bg-secondary px-2 py-1 rounded text-[11px] theme-text-primary"
                            onClick={() => setVoiceSettingsOpen(true)}
                          >
                            设置
                          </button>
                        </div>

                        {/* 自定义提示词设置 - 仅Ultra版用户可见 */}
                        {isUltraVersion && (
                          <div className="flex items-center justify-between text-[13px] font-medium theme-text-primary">
                            <span>自定义提示词设置</span>
                            <button
                              className="theme-bg-tertiary hover:theme-bg-secondary px-2 py-1 rounded text-[11px] theme-text-primary"
                              onClick={() => setCustomPromptsOpen(true)}
                            >
                              设置
                            </button>
                          </div>
                        )}

                        {/* 小抄设置 - 仅Ultra版用户可见 */}
                        {isUltraVersion && (
                          <div className="flex items-center justify-between text-[13px] font-medium theme-text-primary">
                            <span>小抄设置</span>
                            <button
                              className="theme-bg-tertiary hover:theme-bg-secondary px-2 py-1 rounded text-[11px] theme-text-primary"
                              onClick={() => {
                                setIsTooltipVisible(false);
                                window.dispatchEvent(new CustomEvent('open-cheatsheet-settings'));
                              }}
                            >
                              设置
                            </button>
                          </div>
                        )}
                      </div>

                      <button
                        onClick={handleSignOut}
                        className="flex items-center gap-2 text-[11px] text-red-400 hover:text-red-300 transition-colors w-full"
                      >
                        <div className="w-4 h-4 flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="w-3 h-3"
                          >
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                            <polyline points="16 17 21 12 16 7" />
                            <line x1="21" y1="12" x2="9" y2="12" />
                          </svg>
                        </div>
                        退出登录
                      </button>
                    </div>
                  </div>
                </div>
              </div>,
              document.body
            )}
          </div>
        </div>
      </div>

      {/* Voice Settings Dialog */}
      <VoiceSettingsDialog
        open={voiceSettingsOpen}
        onOpenChange={setVoiceSettingsOpen}
      />

      {/* Custom Prompts Dialog */}
      <CustomPromptsDialog
        open={customPromptsOpen}
        onOpenChange={setCustomPromptsOpen}
      />
    </div>
  )
}

export default SolutionCommands
