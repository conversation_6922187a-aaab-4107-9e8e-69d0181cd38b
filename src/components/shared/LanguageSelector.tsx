import React, { useState, useRef, useEffect } from "react"

interface LanguageSelectorProps {
  currentLanguage: string
  setLanguage: (language: string) => void
  onLanguageSelected?: () => void
}

const languages = [
  { value: "python", label: "Python" },
  { value: "javascript", label: "JavaScript" },
  { value: "java", label: "Java" },
  { value: "golang", label: "Go" },
  { value: "cpp", label: "C++" },
  { value: "swift", label: "Swift" },
  { value: "kotlin", label: "Kotl<PERSON>" },
  { value: "ruby", label: "Ruby" },
  { value: "shell", label: "Shell" },
  { value: "sql", label: "SQL" },
  { value: "r", label: "R" }
]

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  currentLanguage,
  setLanguage,
  onLanguageSelected
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleLanguageChange = async (newLanguage: string) => {
    try {
      // Save language preference to electron store
      await window.electronAPI.updateConfig({ language: newLanguage })

      // Update global language variable
      window.__LANGUAGE__ = newLanguage

      // Update state in React
      setLanguage(newLanguage)

      // Close dropdown
      setIsOpen(false)

      // Call the callback to hide tooltip if provided
      if (onLanguageSelected) {
        onLanguageSelected()
      }

      console.log(`Language changed to ${newLanguage}`);
    } catch (error) {
      console.error("Error updating language:", error)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const currentLanguageLabel = languages.find(lang => lang.value === currentLanguage)?.label || currentLanguage

  return (
    <div className="mb-3 px-2 space-y-1 relative" ref={dropdownRef}>
      <div className="flex items-center justify-between text-[13px] font-medium theme-text-primary">
        <span>编程语言</span>
        <div className="relative">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="bg-black/80 text-white/90 rounded px-2 py-1 text-sm outline-none border border-white/10 focus:border-white/20 hover:border-white/20 transition-colors flex items-center gap-2 min-w-[80px] justify-between"
          >
            <span>{currentLanguageLabel}</span>
            <svg
              className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isOpen && (
            <div className="absolute top-full left-0 mt-1 w-full bg-black/90 border border-white/20 rounded shadow-lg z-[1001] max-h-48 overflow-y-auto">
              {languages.map((language) => (
                <button
                  key={language.value}
                  onClick={() => handleLanguageChange(language.value)}
                  className={`w-full text-left px-3 py-2 text-sm hover:bg-white/10 transition-colors ${
                    currentLanguage === language.value ? 'bg-white/20 text-white' : 'text-white/90'
                  }`}
                >
                  {language.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
