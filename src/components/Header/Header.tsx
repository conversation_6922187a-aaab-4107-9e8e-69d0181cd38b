import React, { useState } from 'react';
import { Settings, LogOut, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '../ui/button';
import { useToast } from '../../contexts/toast';
import { ThemeSwitch } from '../ui/ThemeSwitch';

interface HeaderProps {
  currentLanguage: string;
  setLanguage: (language: string) => void;
  onOpenSettings: () => void;
}

// Available programming languages
const LANGUAGES = [
  { value: 'python', label: 'Python' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'csharp', label: 'C#' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'typescript', label: 'TypeScript' },
];

export function Header({ currentLanguage, setLanguage, onOpenSettings }: HeaderProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const { showToast } = useToast();

  // Handle logout - clear API key and reload app
  const handleLogout = async () => {
    try {
      // Update config with empty API key
      await window.electronAPI.updateConfig({
        apiKey: '',
      });
      
      showToast('成功', '退出登录成功', 'success');
      
      // Reload the app after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      console.error('Error logging out:', error);
      showToast('错误', '退出登录失败', 'error');
    }
  };

  // Handle language selection
  const handleLanguageSelect = (lang: string) => {
    setLanguage(lang);
    setDropdownOpen(false);
    
    // Also save the language preference to config
    window.electronAPI.updateConfig({
      language: lang
    }).catch((error: unknown) => {
      console.error('Failed to save language preference:', error);
    });
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };

  // Find the current language object
  const currentLangObj = LANGUAGES.find(lang => lang.value === currentLanguage) || LANGUAGES[0];

  return (
    <div className="theme-bg-primary p-2 border-b theme-border-primary flex items-center justify-between">
      <div className="flex items-center space-x-1">
        <span className="theme-text-primary text-sm mr-2">编程语言:</span>
        <div className="relative">
          <button
            onClick={toggleDropdown}
            className="flex items-center gap-1 rounded-md theme-bg-secondary px-3 py-1.5 text-sm theme-text-primary hover:theme-bg-tertiary transition-colors"
          >
            {currentLangObj.label}
            {dropdownOpen ? (
              <ChevronUp className="h-4 w-4 theme-text-secondary" />
            ) : (
              <ChevronDown className="h-4 w-4 theme-text-secondary" />
            )}
          </button>
          
          {dropdownOpen && (
            <div className="absolute z-10 mt-1 w-full rounded-md theme-bg-primary border theme-border-primary shadow-lg">
              <div className="py-1">
                {LANGUAGES.map((lang) => (
                  <button
                    key={lang.value}
                    onClick={() => handleLanguageSelect(lang.value)}
                    className={`block w-full text-left px-4 py-2 text-sm ${
                      currentLanguage === lang.value
                        ? 'theme-bg-secondary theme-text-primary'
                        : 'theme-text-secondary hover:theme-bg-secondary'
                    }`}
                  >
                    {lang.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* 主题切换滑块 */}
        <ThemeSwitch />

        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 theme-text-secondary hover:theme-text-primary hover:theme-bg-secondary"
            onClick={onOpenSettings}
            title="Settings"
          >
          <Settings className="h-4 w-4 mr-1" />
          <span className="text-xs">设置</span>
        </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-red-400/80 hover:text-red-400 hover:theme-bg-secondary"
            onClick={handleLogout}
            title="Log Out"
          >
            <LogOut className="h-4 w-4 mr-1" />
            <span className="text-xs">退出登录</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
