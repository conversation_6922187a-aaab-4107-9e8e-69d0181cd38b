import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useGummySpeechRecognition, AudioListeningMode, RecognitionResult } from '../../hooks/useGummySpeechRecognition';
import AudioWaveform from './AudioWaveform';
import { useTheme } from '../../contexts/theme';
import { audioStartupOptimizer } from '../../utils/AudioStartupOptimizer';

export type VoiceProvider = 'tongyi-gummy';

interface VoiceInputProps {
  onSendMessage: (message: string) => void;
  isStreaming: boolean;
  enableDualAudio?: boolean; // 是否启用双音频源模式（保留兼容性）
  listeningMode?: AudioListeningMode; // 音频监听模式
  onConnectionStatusChange?: (isConnected: boolean) => void; // 连接状态变化回调
  layoutMode?: 'bottom' | 'left'; // 布局模式
  autoSendConfig?: {
    enabled: boolean;
    roundsToSend: number;
    recognitionsPerRound: number;
  };
}

const VoiceInput: React.FC<VoiceInputProps> = ({
  onSendMessage,
  isStreaming,
  enableDualAudio = false,
  listeningMode = 'microphone-only',
  onConnectionStatusChange,
  layoutMode = 'bottom',
  autoSendConfig
}) => {
  const { theme } = useTheme();
  const [inputText, setInputText] = useState<string>('');
  const [voiceProvider, setVoiceProvider] = useState<VoiceProvider>('tongyi-gummy'); // 默认使用通义Gummy语音识别
  const [language, setLanguage] = useState<string>('zh'); // 默认中文
  const [currentListeningMode, setCurrentListeningMode] = useState<AudioListeningMode>(listeningMode); // 当前监听模式
  const [isConnected, setIsConnected] = useState<boolean>(false); // 连接状态
  const connectionEstablishedRef = useRef<boolean>(false); // 防止重复连接

  // 优化回调函数，避免重新创建
  const handleResult = useCallback((text: string) => {
    // 仅麦克风模式的结果回调
    setInputText(text);
  }, []);

  const handleDualResult = useCallback((result: RecognitionResult) => {
    // 双音频模式的结果回调
    // console.log(`语音识别结果 [${result.source}]:`, result.text);
  }, []);

  // 自动发送回调
  const handleAutoSend = useCallback((text: string) => {
    console.log('自动发送消息:', text);
    onSendMessage(text);
    setInputText('');
    speechRecognition.resetText();
  }, [onSendMessage]);

  // 统一的语音识别hook，支持所有音频监听模式
  const speechRecognition = useGummySpeechRecognition({
    onResult: handleResult,
    onDualResult: handleDualResult,
    language: language,
    listeningMode: currentListeningMode,
    autoSendConfig: autoSendConfig,
    onAutoSend: handleAutoSend
  });

  // 从配置中加载选择的语音识别提供商和语言设置（仅在组件挂载时加载一次）
  useEffect(() => {
    const loadVoiceConfig = async () => {
      try {
        if (window.electronAPI && window.electronAPI.getVoiceConfig) {
          const config = await window.electronAPI.getVoiceConfig();
          if (config) {
            if (config.selectedProvider) {
              setVoiceProvider(config.selectedProvider);
            }
            if (config.language) {
              setLanguage(config.language);
            }
          }
        }
      } catch (error) {
        console.warn('无法加载语音配置:', error);
      }
    };

    loadVoiceConfig();
  }, []); // 确保只在组件挂载时执行一次

  // 保存语音提供商选择
  const handleVoiceProviderChange = useCallback(async (provider: VoiceProvider) => {
    setVoiceProvider(provider);

    try {
      if (window.electronAPI && window.electronAPI.updateVoiceConfig) {
        await window.electronAPI.updateVoiceConfig({ selectedProvider: provider });
      }
    } catch (error) {
      console.warn('无法保存语音提供商选择:', error);
    }
  }, []);

  // 统一的接口，现在所有模式都使用同一个hook
  const isListening = currentListeningMode === 'microphone-only'
    ? speechRecognition.isListening
    : (speechRecognition.isMicrophoneListening || speechRecognition.isSystemAudioListening);

  const recognizedText = currentListeningMode === 'microphone-only'
    ? speechRecognition.text
    : speechRecognition.combinedText;

  const startListening = speechRecognition.startListening;
  const stopListening = speechRecognition.stopListening;
  const resetText = speechRecognition.resetText;
  const clearAccumulatedText = speechRecognition.clearAccumulatedText;
  const syncExternalText = speechRecognition.syncExternalText;
  const error = speechRecognition.error;
  const provider = speechRecognition.provider;

  // 同步语音识别的文本到输入框
  useEffect(() => {
    if (currentListeningMode !== 'microphone-only' && speechRecognition.combinedText !== inputText) {
      setInputText(speechRecognition.combinedText);
    }
  }, [currentListeningMode, speechRecognition.combinedText, inputText]);

  // 监听外部传入的listeningMode变化
  useEffect(() => {
    setCurrentListeningMode(listeningMode);
    speechRecognition.setListeningMode(listeningMode);
  }, [listeningMode]); // 移除 speechRecognition 依赖

  // 连接管理 - 优化连接建立时机
  useEffect(() => {
    let isComponentMounted = true;
    let connectionTimeout: NodeJS.Timeout;

    const establishConnection = async () => {
      // 防止重复连接
      if (connectionEstablishedRef.current) {
        console.log('VoiceInput: 连接已建立，跳过重复连接');
        return;
      }

      if (voiceProvider === 'tongyi-gummy' && speechRecognition.connectToService) {
        console.log('VoiceInput: 准备建立Gummy连接...');
        connectionEstablishedRef.current = true;
        try {
          const success = await speechRecognition.connectToService();
          if (isComponentMounted) {
            setIsConnected(success);
            if (onConnectionStatusChange) {
              onConnectionStatusChange(success);
            }
            if (success) {
              console.log('VoiceInput: Gummy连接建立成功');
              // 减少等待时间，因为预初始化已经完成
              if (currentListeningMode === 'system-only' || currentListeningMode === 'dual-audio') {
                console.log('VoiceInput: 等待语音识别服务完全就绪...');
                await new Promise(resolve => setTimeout(resolve, 500)); // 从2000ms减少到500ms
              }
            } else {
              console.error('VoiceInput: Gummy连接建立失败');
              connectionEstablishedRef.current = false; // 连接失败时重置标志
            }
          }
        } catch (error) {
          console.error('VoiceInput: Gummy连接建立异常:', error);
          connectionEstablishedRef.current = false; // 连接异常时重置标志
          if (isComponentMounted) {
            setIsConnected(false);
            if (onConnectionStatusChange) {
              onConnectionStatusChange(false);
            }
          }
        }
      }
    };

    // 使用优化器的配置来设置延迟时间
    const optimizedConfig = audioStartupOptimizer.getOptimizedConfig();
    connectionTimeout = setTimeout(() => {
      if (isComponentMounted) {
        establishConnection();
      }
    }, optimizedConfig.connectionTimeout); // 使用优化的连接超时时间

    // 组件卸载时断开连接
    return () => {
      isComponentMounted = false;
      connectionEstablishedRef.current = false; // 重置连接标志
      if (connectionTimeout) {
        clearTimeout(connectionTimeout);
      }
      if (speechRecognition.disconnectFromService) {
        speechRecognition.disconnectFromService();
        setIsConnected(false);
        if (onConnectionStatusChange) {
          onConnectionStatusChange(false);
        }
      }
    };
  }, []); // 空依赖数组，只在组件挂载/卸载时执行

  // 处理文本输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInputText(newValue);

    // 同步手动编辑的文本到语音识别的状态
    // 对于所有语音识别提供商都进行同步，确保状态一致性
    if (syncExternalText) {
      syncExternalText(newValue);
    }
  };
  
  // 处理录音按钮点击
  const handleRecordClick = useCallback(() => {
    if (isListening) {
      stopListening();
    } else {
      // 开始录音时不清空输入框，保留之前的内容
      // 只有在用户明确发送消息或手动清空时才清空
      startListening();
    }
  }, [isListening, stopListening, startListening]);

  // 独立控制麦克风
  const handleMicrophoneToggle = useCallback(() => {
    if (speechRecognition.isMicrophoneListening) {
      speechRecognition.stopMicrophoneOnly();
    } else {
      speechRecognition.startMicrophoneOnly();
    }
  }, [speechRecognition]);

  // 独立控制系统音频
  const handleSystemAudioToggle = useCallback(() => {
    if (speechRecognition.isSystemAudioListening) {
      speechRecognition.stopSystemAudioOnly();
    } else {
      speechRecognition.startSystemAudioOnly();
    }
  }, [speechRecognition]);
  
  // 处理发送消息
  const handleSendMessage = useCallback(() => {
    if (inputText.trim() && !isStreaming) {
      onSendMessage(inputText.trim());
      setInputText('');
      resetText();
    }
  }, [inputText, isStreaming, onSendMessage, resetText]);

  // 处理清空文本
  const handleClearText = useCallback(() => {
    setInputText('');
    if (clearAccumulatedText) {
      clearAccumulatedText();
    } else {
      resetText();
    }
  }, [clearAccumulatedText, resetText]);
  
  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+Enter发送消息
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 监听全局Ctrl+Enter事件
  useEffect(() => {
    const handleCtrlEnter = () => {
      handleSendMessage();
    };
    
    // 订阅全局Ctrl+Enter事件
    const unsubscribeHandleCtrlEnter = window.electronAPI.onHandleCtrlEnter(handleCtrlEnter);
    
    return () => {
      unsubscribeHandleCtrlEnter();
    };
  }, [handleSendMessage]);

  // 监听全局语音录制切换事件
  useEffect(() => {
    const handleToggleVoiceRecording = () => {
      handleRecordClick();
    };
    
    // 订阅全局语音录制切换事件
    const unsubscribeToggleVoiceRecording = window.electronAPI.onToggleVoiceRecording(handleToggleVoiceRecording);
    
    return () => {
      unsubscribeToggleVoiceRecording();
    };
  }, [handleRecordClick]);

  // 获取模式显示信息
  const getModeInfo = useCallback((mode: AudioListeningMode) => {
    switch (mode) {
      case 'microphone-only':
        return { icon: '🎤', label: '仅麦克风', description: '只监听麦克风音频' };
      case 'system-only':
        return { icon: '🔊', label: '仅系统音频', description: '只监听系统输出音频' };
      case 'dual-audio':
        return { icon: '🎧', label: '双音频源', description: '同时监听麦克风和系统音频' };
    }
  }, []);

  return (
    <div
      className={`p-4 ${
        layoutMode === 'left'
          ? 'min-w-[400px] max-w-[450px] w-[40%] flex flex-col flex-shrink-0 h-full'
          : 'flex-shrink-0 min-h-[200px]'
      }`}
      style={{
        ...(layoutMode === 'left'
          ? {
              borderRight: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
              backgroundColor: theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(8px)'
            }
          : {
              borderTop: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
              backgroundColor: theme === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(8px)'
            }
        )
      }}
    >
      {/* 系统音频权限提示 */}
      {(currentListeningMode === 'system-only' || currentListeningMode === 'dual-audio') &&
       !speechRecognition.systemAudioPermissionGranted && (
        <div className="mb-3 p-2 rounded text-sm" style={{
          backgroundColor: theme === 'dark' ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.1)',
          color: theme === 'dark' ? '#fbbf24' : '#d97706',
          border: `1px solid ${theme === 'dark' ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`
        }}>
          系统音频权限未授予，请在系统设置中允许应用访问系统音频
        </div>
      )}

      {error && (
        <div className="text-sm mb-2" style={{ color: theme === 'dark' ? '#fca5a5' : '#dc2626' }}>
          <div className="font-semibold">语音识别错误：</div>
          <div>{error}</div>
          {error.includes('API密钥未配置') && (
            <div className="mt-1" style={{ color: theme === 'dark' ? '#fbbf24' : '#d97706' }}>
              💡 提示：请在设置中配置通义Gummy API密钥。
            </div>
          )}
          {error.includes('配置不完整') && (
            <div className="mt-1" style={{ color: theme === 'dark' ? '#fbbf24' : '#d97706' }}>
              💡 提示：需要在阿里云百炼平台申请通义Gummy服务并配置API密钥。
            </div>
          )}
        </div>
      )}
      {provider && provider !== 'none' && (
        <div className="text-xs mb-2" style={{ color: theme === 'dark' ? '#4ade80' : '#16a34a' }}>
          当前使用：{provider}
        </div>
      )}
      {/* 状态指示器 */}
      {isListening && (
        <div className="flex items-center gap-4 mb-3 text-sm">
          {currentListeningMode === 'dual-audio' ? (
            <>
              {speechRecognition.isMicrophoneListening && (
                <div className="flex items-center gap-1" style={{ color: '#10b981' }}>
                  <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                  麦克风监听中
                </div>
              )}
              {speechRecognition.isSystemAudioListening && (
                <div className="flex items-center gap-1" style={{ color: '#3b82f6' }}>
                  <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                  系统音频监听中
                </div>
              )}
            </>
          ) : (
            <div className="flex items-center gap-1" style={{ color: '#10b981' }}>
              <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
              {getModeInfo(currentListeningMode).label}监听中
            </div>
          )}

          {/* 自动发送状态指示 */}
          {autoSendConfig && speechRecognition.autoSendEnabled && (
            <div className="flex items-center gap-1" style={{ color: '#f59e0b' }}>
              <span className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></span>
              自动发送已开启 ({speechRecognition.recognitionCount}/{autoSendConfig.roundsToSend * autoSendConfig.recognitionsPerRound})
            </div>
          )}
        </div>
      )}

      {/* 双音频模式说明 */}
      {currentListeningMode === 'dual-audio' && (
        <div className="mb-3 p-2 rounded text-xs" style={{
          backgroundColor: theme === 'dark' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
          color: theme === 'dark' ? '#93c5fd' : '#2563eb',
          border: `1px solid ${theme === 'dark' ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`
        }}>
          💡 双音频模式：可独立控制 🎤麦克风（绿色）和 🔊系统音频（蓝色），或使用 🎧全部控制按钮同时操作
        </div>
      )}

      <div className="flex items-center gap-2 mb-2">
        {/* 双音频模式显示独立控制按钮 */}
        {currentListeningMode === 'dual-audio' ? (
          <>
            {/* 麦克风控制按钮 */}
            <button
              onClick={handleMicrophoneToggle}
              className="p-2 rounded-full flex-shrink-0 text-white transition-colors"
              style={{
                backgroundColor: speechRecognition.isMicrophoneListening
                  ? '#10b981'
                  : (theme === 'dark' ? '#4b5563' : '#6b7280')
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = speechRecognition.isMicrophoneListening
                  ? '#059669'
                  : (theme === 'dark' ? '#374151' : '#4b5563');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = speechRecognition.isMicrophoneListening
                  ? '#10b981'
                  : (theme === 'dark' ? '#4b5563' : '#6b7280');
              }}
              title={speechRecognition.isMicrophoneListening ? '停止麦克风监听' : '开始麦克风监听'}
            >
              <span style={{ fontSize: '15px' }}>🎤</span>
            </button>

            {/* 系统音频控制按钮 */}
            <button
              onClick={handleSystemAudioToggle}
              className="p-2 rounded-full flex-shrink-0 text-white transition-colors"
              style={{
                backgroundColor: speechRecognition.isSystemAudioListening
                  ? '#3b82f6'
                  : (theme === 'dark' ? '#4b5563' : '#6b7280')
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = speechRecognition.isSystemAudioListening
                  ? '#2563eb'
                  : (theme === 'dark' ? '#374151' : '#4b5563');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = speechRecognition.isSystemAudioListening
                  ? '#3b82f6'
                  : (theme === 'dark' ? '#4b5563' : '#6b7280');
              }}
              title={speechRecognition.isSystemAudioListening ? '停止系统音频监听' : '开始系统音频监听'}
            >
              <span style={{ fontSize: '15px' }}>🔊</span>
            </button>

            {/* 全部控制按钮 */}
            <button
              onClick={handleRecordClick}
              className="p-2 rounded-full flex-shrink-0 text-white transition-colors"
              style={{
                backgroundColor: isListening
                  ? '#dc2626'
                  : (theme === 'dark' ? '#2563eb' : '#1d4ed8')
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = isListening
                  ? '#b91c1c'
                  : (theme === 'dark' ? '#1d4ed8' : '#1e40af');
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = isListening
                  ? '#dc2626'
                  : (theme === 'dark' ? '#2563eb' : '#1d4ed8');
              }}
              title={isListening ? '停止全部监听' : '开始全部监听'}
            >
              <span style={{ fontSize: '15px' }}>🎧</span>
            </button>
          </>
        ) : (
          /* 单音频模式显示原有的单一控制按钮 */
          <button
            onClick={handleRecordClick}
            className="p-2 rounded-full flex-shrink-0 text-white transition-colors"
            style={{
              backgroundColor: isListening
                ? '#dc2626'
                : (theme === 'dark' ? '#2563eb' : '#1d4ed8')
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = isListening
                ? '#b91c1c'
                : (theme === 'dark' ? '#1d4ed8' : '#1e40af');
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = isListening
                ? '#dc2626'
                : (theme === 'dark' ? '#2563eb' : '#1d4ed8');
            }}
            title={isListening ? `停止${getModeInfo(currentListeningMode).label}录音` : `开始${getModeInfo(currentListeningMode).label}录音`}
          >
            <span style={{ fontSize: '15px' }}>
              {currentListeningMode === 'microphone-only' ? '🎤' : '🔊'}
            </span>
          </button>
        )}
        <button
          onClick={handleClearText}
          className="p-2 rounded-full flex-shrink-0 text-white transition-colors"
          style={{
            backgroundColor: theme === 'dark' ? '#4b5563' : '#6b7280',
            opacity: !inputText.trim() ? 0.5 : 1
          }}
          onMouseEnter={(e) => {
            if (inputText.trim()) {
              e.currentTarget.style.backgroundColor = theme === 'dark' ? '#374151' : '#4b5563';
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = theme === 'dark' ? '#4b5563' : '#6b7280';
          }}
          title="清空文本"
          disabled={!inputText.trim()}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>

        {/* 自动发送控制按钮 */}
        {autoSendConfig && (
          <button
            onClick={() => speechRecognition.setAutoSendEnabled(!speechRecognition.autoSendEnabled)}
            className="p-2 rounded-full flex-shrink-0 text-white transition-colors"
            style={{
              backgroundColor: speechRecognition.autoSendEnabled
                ? '#10b981'
                : (theme === 'dark' ? '#4b5563' : '#6b7280')
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = speechRecognition.autoSendEnabled
                ? '#059669'
                : (theme === 'dark' ? '#374151' : '#4b5563');
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = speechRecognition.autoSendEnabled
                ? '#10b981'
                : (theme === 'dark' ? '#4b5563' : '#6b7280');
            }}
            title={speechRecognition.autoSendEnabled ? '关闭自动发送' : '开启自动发送'}
          >
            <span style={{ fontSize: '15px' }}>
              {speechRecognition.autoSendEnabled ? '🚀' : '⏸️'}
            </span>
          </button>
        )}
        <div className="flex-1">
          <AudioWaveform isRecording={isListening} className="w-full h-10" theme={theme} />
        </div>
      </div>

      <div className={`flex gap-2 ${layoutMode === 'left' ? 'flex-col flex-1' : 'items-start'}`}>
        <textarea
          value={inputText}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={
            currentListeningMode === 'dual-audio'
              ? "语音识别结果将显示在这里，区分用户和面试官..."
              : `${getModeInfo(currentListeningMode).label}识别结果将显示在这里...`
          }
          className={`flex-1 p-3 rounded-md resize-none transition-colors ${
            layoutMode === 'left' ? 'flex-1 min-h-[200px]' : 'h-24'
          }`}
          style={{
            border: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'}`,
            backgroundColor: theme === 'dark' ? 'rgba(26, 26, 26, 0.7)' : 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(6px)',
            color: theme === 'dark' ? '#ffffff' : '#000000'
          }}
          onFocus={(e) => {
            e.target.style.borderColor = theme === 'dark' ? '#3b82f6' : '#2563eb';
            e.target.style.boxShadow = `0 0 0 3px ${theme === 'dark' ? 'rgba(59, 130, 246, 0.3)' : 'rgba(37, 99, 235, 0.3)'}`;
          }}
          onBlur={(e) => {
            e.target.style.borderColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)';
            e.target.style.boxShadow = 'none';
          }}
        />
        <button
          onClick={handleSendMessage}
          disabled={!inputText.trim() || isStreaming}
          className={`p-3 rounded-md flex-shrink-0 transition-colors ${
            layoutMode === 'left' ? 'w-full h-12' : 'h-auto self-stretch'
          }`}
          style={{
            backgroundColor: (!inputText.trim() || isStreaming)
              ? (theme === 'dark' ? '#374151' : '#9ca3af')
              : (theme === 'dark' ? '#2563eb' : '#1d4ed8'),
            color: (!inputText.trim() || isStreaming)
              ? (theme === 'dark' ? '#9ca3af' : '#6b7280')
              : '#ffffff',
            cursor: (!inputText.trim() || isStreaming) ? 'not-allowed' : 'pointer'
          }}
          onMouseEnter={(e) => {
            if (inputText.trim() && !isStreaming) {
              e.currentTarget.style.backgroundColor = theme === 'dark' ? '#1d4ed8' : '#1e40af';
            }
          }}
          onMouseLeave={(e) => {
            if (inputText.trim() && !isStreaming) {
              e.currentTarget.style.backgroundColor = theme === 'dark' ? '#2563eb' : '#1d4ed8';
            }
          }}
        >
          <span className="flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
            发送
          </span>
        </button>
      </div>

      <div
        className="flex justify-between items-center mt-2 text-xs"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)' }}
      >
        <div>
          {isListening
            ? `正在${getModeInfo(currentListeningMode).label}录音...`
            : `按 Cmd/Ctrl+M 开始/停止${getModeInfo(currentListeningMode).label}录音`
          }
        </div>
        <div>按 Cmd/Ctrl+Enter 发送</div>
      </div>
    </div>
  );
};

export default VoiceInput; 