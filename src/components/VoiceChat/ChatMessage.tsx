import React, { useEffect, useState, useRef, memo } from 'react';
import MarkdownRenderer from '../ui/MarkdownRenderer';
import { useTheme } from '../../contexts/theme';

interface ChatMessageProps {
  role: 'user' | 'assistant';
  content: string;
  reasoning?: string; // 深度思考模型的推理内容
}

const ChatMessage: React.FC<ChatMessageProps> = ({ role, content, reasoning }) => {
  const { theme } = useTheme();
  const isUser = role === 'user';
  // 使用本地状态来跟踪内容，确保组件重新渲染
  const [displayContent, setDisplayContent] = useState(content);
  const [displayReasoning, setDisplayReasoning] = useState(reasoning || '');
  const [showReasoning, setShowReasoning] = useState(false);
  // 添加content的引用，用于调试
  const contentRef = useRef<string>(content);
  const reasoningRef = useRef<string>(reasoning || '');
  // 添加计数器来跟踪更新次数
  const updateCountRef = useRef<number>(0);

  // 当content prop变化时更新本地状态
  useEffect(() => {
    if (content !== contentRef.current) {
      updateCountRef.current++;
      contentRef.current = content;

      // 确保在下一个渲染周期设置内容，避免状态批处理导致的问题
      setTimeout(() => {
        setDisplayContent(content);
      }, 0);
    }
  }, [content, role]);

  // 当reasoning prop变化时更新本地状态
  useEffect(() => {
    if (reasoning !== reasoningRef.current) {
      reasoningRef.current = reasoning || '';

      setTimeout(() => {
        setDisplayReasoning(reasoning || '');
      }, 0);
    }
  }, [reasoning]);

  // 计算消息气泡的样式
  const containerClasses = isUser
      ? 'flex justify-end mb-4'
      : 'flex justify-start mb-4';

  const bubbleStyle = isUser
      ? {
          backgroundColor: theme === 'dark' ? '#2563eb' : '#1d4ed8',
          color: '#ffffff'
        }
      : {
          backgroundColor: theme === 'dark' ? 'rgba(26, 26, 26, 0.7)' : 'rgba(245, 245, 245, 0.7)',
          backdropFilter: 'blur(6px)',
          color: theme === 'dark' ? '#ffffff' : '#000000',
          border: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`
        };

  // // 添加调试信息
  // console.log(`ChatMessage [${role}] 渲染, 显示内容长度: ${displayContent?.length || 0}`);

  return (
      <div className={containerClasses}>
        <div
          className="rounded-lg py-2 px-4 break-words max-w-[90%] sm:max-w-[85%] md:max-w-[80%]"
          style={bubbleStyle}
        >
          {isUser ? (
              // 用户消息简单显示文本
              <div className="whitespace-pre-wrap">{displayContent || '(空消息)'}</div>
          ) : (
              // AI助手消息支持Markdown和代码高亮
              <div className="w-full">
                {/* 推理内容展示区域 */}
                {displayReasoning && (
                    <div className="mb-3">
                      <button
                          onClick={() => setShowReasoning(!showReasoning)}
                          className="flex items-center gap-2 text-sm transition-colors mb-2"
                          style={{
                            color: theme === 'dark' ? '#60a5fa' : '#2563eb'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.color = theme === 'dark' ? '#93c5fd' : '#1d4ed8';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.color = theme === 'dark' ? '#60a5fa' : '#2563eb';
                          }}
                      >
                        <svg
                            className={`w-4 h-4 transition-transform ${showReasoning ? 'rotate-90' : ''}`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                        <span>思考过程</span>
                        <span
                          className="text-xs"
                          style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)' }}
                        >
                          ({displayReasoning.length} 字符)
                        </span>
                      </button>
                      {showReasoning && (
                          <div
                            className="rounded-lg p-3"
                            style={{
                              backgroundColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
                              backdropFilter: 'blur(4px)',
                              borderLeft: `4px solid ${theme === 'dark' ? '#3b82f6' : '#2563eb'}`
                            }}
                          >
                            <div
                              className="text-sm whitespace-pre-wrap"
                              style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.8)' }}
                            >
                              {displayReasoning}
                            </div>
                          </div>
                      )}
                    </div>
                )}

                {/* 回答内容 */}
                {displayContent ? (
                    <MarkdownRenderer content={displayContent} />
                ) : (
                    <span style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)' }}>
                      正在思考...
                    </span>
                )}
              </div>
          )}
        </div>
      </div>
  );
};

// 使用memo优化性能，避免不必要的重新渲染
export default memo(ChatMessage, (prevProps, nextProps) => {
  // 只有当content或reasoning发生变化时才重新渲染
  return prevProps.content === nextProps.content &&
         prevProps.reasoning === nextProps.reasoning &&
         prevProps.role === nextProps.role;
});