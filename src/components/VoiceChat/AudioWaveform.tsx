import React, { useEffect, useRef } from 'react';

interface AudioWaveformProps {
  isRecording: boolean;
  className?: string;
  theme?: 'dark' | 'light';
}

const AudioWaveform: React.FC<AudioWaveformProps> = ({ isRecording, className = '', theme = 'dark' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  
  // 绘制音频波形
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const width = canvas.width;
    const height = canvas.height;
    const centerY = height / 2;
    const barWidth = 2;
    const barGap = 2;
    const barCount = Math.floor(width / (barWidth + barGap));
    const barHeights = Array(barCount).fill(0);
    
    // 重置画布
    const reset = () => {
      ctx.clearRect(0, 0, width, height);
      
      if (!isRecording) {
        // 非录音状态，绘制静态波形
        const staticColor = theme === 'dark' ? 'rgba(59, 130, 246, 0.5)' : 'rgba(37, 99, 235, 0.5)';
        ctx.fillStyle = staticColor;
        for (let i = 0; i < barCount; i++) {
          const x = i * (barWidth + barGap);
          const barHeight = Math.min(10, height / 7);
          const y = centerY - barHeight / 2;
          ctx.fillRect(x, y, barWidth, barHeight);
        }
        return;
      }
    };
    
    // 绘制动态波形
    const drawWaveform = () => {
      ctx.clearRect(0, 0, width, height);
      
      // 更新每个柱状条的高度
      for (let i = 0; i < barCount; i++) {
        if (isRecording) {
          // 录音时，创建随机高度的波形，模拟音频输入
          const targetHeight = Math.random() * height * 0.5;
          // 平滑过渡
          barHeights[i] = barHeights[i] + (targetHeight - barHeights[i]) * 0.15;
        } else {
          // 非录音时，波形高度降低到最小值
          barHeights[i] = Math.max(0, barHeights[i] * 0.9);
        }
        
        // 绘制柱状条
        const x = i * (barWidth + barGap);
        const barHeight = Math.max(2, barHeights[i]);
        const y = centerY - barHeight / 2;
        
        // 设置渐变色
        const gradient = ctx.createLinearGradient(x, y, x, y + barHeight);
        const topColor = theme === 'dark' ? 'rgba(59, 130, 246, 0.8)' : 'rgba(37, 99, 235, 0.8)';
        const bottomColor = theme === 'dark' ? 'rgba(59, 130, 246, 0.4)' : 'rgba(37, 99, 235, 0.4)';
        gradient.addColorStop(0, topColor); // 顶部
        gradient.addColorStop(1, bottomColor); // 底部，半透明
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, barWidth, barHeight);
      }
      
      // 继续动画循环
      animationRef.current = requestAnimationFrame(drawWaveform);
    };
    
    if (isRecording) {
      // 开始录音动画
      drawWaveform();
    } else {
      // 停止录音，重置画布
      reset();
      cancelAnimationFrame(animationRef.current!);
    }
    
    // 清理函数
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRecording]);
  
  return (
    <div className={`relative ${className}`}>
      <canvas 
        ref={canvasRef}
        width={200}
        height={40}
        className="w-full h-full"
      />
    </div>
  );
};

export default AudioWaveform; 