import React, { useEffect, useRef, useState, useCallback } from 'react';
import ChatMessage from './ChatMessage';
import { useTheme } from '../../contexts/theme';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  reasoning?: string; // 深度思考模型的推理内容
}

interface ChatHistoryProps {
  messages: ChatMessage[];
  className?: string;
  shouldScrollToBottom?: boolean; // 新增：是否应该滚动到底部的外部控制
  onScrollToBottomChange?: (isAtBottom: boolean) => void; // 新增：滚动位置变化回调
  isStreaming?: boolean; // 新增：是否正在流式输出
  layoutMode?: 'bottom' | 'left'; // 新增：布局模式
}

const ChatHistory: React.FC<ChatHistoryProps> = ({
  messages,
  className = '',
  shouldScrollToBottom,
  onScrollToBottomChange,
  isStreaming = false,
  layoutMode = 'bottom'
}) => {
  const { theme } = useTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isAtBottom, setIsAtBottom] = useState(true); // 跟踪是否在底部
  const [userScrolled, setUserScrolled] = useState(false); // 跟踪用户是否主动滚动
  const [isInitialized, setIsInitialized] = useState(false); // 跟踪是否已初始化
  const lastMessageCountRef = useRef(messages.length);

  // 检查是否在底部的阈值（像素）
  const BOTTOM_THRESHOLD = 50;

  // 检查是否在底部
  const checkIfAtBottom = useCallback(() => {
    if (!chatContainerRef.current) return false;

    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - BOTTOM_THRESHOLD;

    return isNearBottom;
  }, []);

  // 滚动到底部
  const scrollToBottom = useCallback((smooth: boolean = true) => {
    if (messagesEndRef.current) {
      // 在流式输出期间使用更直接的滚动方式，避免动画造成的性能问题
      if (isStreaming) {
        // 直接滚动到底部，不使用动画
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
      } else {
        // 非流式输出时使用平滑滚动
        requestAnimationFrame(() => {
          messagesEndRef.current?.scrollIntoView({
            behavior: smooth ? 'smooth' : 'auto'
          });
        });
      }
    }
  }, [isStreaming]);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    const newIsAtBottom = checkIfAtBottom();

    if (newIsAtBottom !== isAtBottom) {
      setIsAtBottom(newIsAtBottom);
      onScrollToBottomChange?.(newIsAtBottom);
    }

    // 如果用户向上滚动了，标记为用户主动滚动
    if (!newIsAtBottom && !userScrolled) {
      setUserScrolled(true);
    }
  }, [isAtBottom, userScrolled, checkIfAtBottom, onScrollToBottomChange]);

  // 智能滚动逻辑
  useEffect(() => {
    const currentMessageCount = messages.length;
    const previousMessageCount = lastMessageCountRef.current;

    // 检查是否有新消息
    const hasNewMessage = currentMessageCount > previousMessageCount;

    if (hasNewMessage) {
      const lastMessage = messages[messages.length - 1];
      const isNewUserMessage = lastMessage?.role === 'user';

      // 规则1: 用户发送新消息时，强制滚动到底部
      if (isNewUserMessage) {
        setUserScrolled(false); // 重置用户滚动状态
        setIsAtBottom(true);
        scrollToBottom(true);
      }
      // 规则2: AI回复时的处理
      else if (isAtBottom && !userScrolled) {
        if (isStreaming) {
          // 流式输出期间，立即滚动，不使用延迟
          scrollToBottom(false);
        } else {
          // 非流式输出时，使用延迟确保DOM更新完成
          const scrollTimeout = setTimeout(() => {
            scrollToBottom(true);
          }, 50);
          return () => clearTimeout(scrollTimeout);
        }
      }
      // 规则3: 如果用户不在底部，不自动滚动，让内容在下方加载
    }

    lastMessageCountRef.current = currentMessageCount;
  }, [messages, isAtBottom, userScrolled, scrollToBottom, isStreaming]);

  // 外部控制滚动到底部
  useEffect(() => {
    if (shouldScrollToBottom) {
      setUserScrolled(false);
      setIsAtBottom(true);
      scrollToBottom(true);
    }
  }, [shouldScrollToBottom, scrollToBottom]);

  // 组件初始化时滚动到底部（如果有消息的话）
  useEffect(() => {
    // 当组件首次挂载且有消息时，确保滚动到底部
    if (messages.length > 0 && !isInitialized) {
      setIsInitialized(true);

      // 使用多重延迟确保DOM完全渲染
      const initScrollTimeout1 = setTimeout(() => {
        setUserScrolled(false);
        setIsAtBottom(true);
        scrollToBottom(false); // 第一次立即滚动
      }, 50);

      // 再次确保滚动到底部
      const initScrollTimeout2 = setTimeout(() => {
        scrollToBottom(false);
      }, 200);

      // 最后一次确保
      const initScrollTimeout3 = setTimeout(() => {
        scrollToBottom(false);
      }, 400);

      return () => {
        clearTimeout(initScrollTimeout1);
        clearTimeout(initScrollTimeout2);
        clearTimeout(initScrollTimeout3);
      };
    }
  }, [messages.length, isInitialized, scrollToBottom]);

  // 添加滚动事件监听
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container) return;

    // 添加滚动监听
    container.addEventListener('scroll', handleScroll, { passive: true });

    // 初始检查是否在底部
    const initialIsAtBottom = checkIfAtBottom();
    setIsAtBottom(initialIsAtBottom);
    onScrollToBottomChange?.(initialIsAtBottom);

    // 只有在未初始化且有消息时才滚动到底部
    if (messages.length > 0 && !initialIsAtBottom && !isInitialized) {
      setTimeout(() => {
        setUserScrolled(false);
        setIsAtBottom(true);
        scrollToBottom(false);
      }, 50);
    }

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, checkIfAtBottom, onScrollToBottomChange, messages.length, scrollToBottom, isInitialized]);

  // 使用MutationObserver监听DOM变化，确保内容渲染完成后滚动
  // 在流式输出期间禁用以提升性能
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container || messages.length === 0 || isStreaming) return;

    const observer = new MutationObserver(() => {
      // DOM发生变化时，只有在未初始化时才自动滚动到底部
      // 避免在用户手动滚动时强制滚动
      if (!isInitialized && !userScrolled) {
        setTimeout(() => {
          scrollToBottom(false);
        }, 10);
      }
    });

    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: false
    });

    return () => {
      observer.disconnect();
    };
  }, [messages.length, isInitialized, userScrolled, scrollToBottom, isStreaming]);

  // 添加自定义滚动条样式
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* 自定义滚动条样式 */
      .voice-chat-container::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      .voice-chat-container::-webkit-scrollbar-track {
        background: ${theme === 'dark' ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.1)'};
        border-radius: 4px;
      }
      .voice-chat-container::-webkit-scrollbar-thumb {
        background: ${theme === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'};
        border-radius: 4px;
      }
      .voice-chat-container::-webkit-scrollbar-thumb:hover {
        background: ${theme === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)'};
      }
    `;
    document.head.appendChild(styleEl);

    return () => {
      document.head.removeChild(styleEl);
    };
  }, [theme]);

  // 移除自定义滚轮事件处理器，使用浏览器原生滚动
  // 这样可以避免滚动冲突问题
  
  return (
    <div
      ref={chatContainerRef}
      className={`flex-1 overflow-y-auto overflow-x-hidden p-4 voice-chat-container min-h-0 ${className}`}
      style={{
        ...(layoutMode === 'left'
          ? {
              // 左侧布局时，确保与输入框高度一致
              minHeight: '0',
              maxHeight: 'none',
              height: '100%'
            }
          : {
              // 底部布局时，保持原有样式
              minHeight: '200px',
              maxHeight: 'calc(100vh - 300px)'
            }
        ),
        // 在流式输出期间优化滚动性能
        scrollBehavior: isStreaming ? 'auto' : 'smooth',
        // 启用硬件加速
        transform: 'translateZ(0)',
        willChange: isStreaming ? 'scroll-position' : 'auto'
      }}
    >
      <div className="w-full max-w-full">
        {messages.map((message, index) => (
          <ChatMessage
            key={index}
            role={message.role}
            content={message.content}
            reasoning={message.reasoning}
          />
        ))}
        {/* 用于自动滚动的空div */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ChatHistory; 