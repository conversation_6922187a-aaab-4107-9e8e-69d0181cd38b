import React, { useEffect, useState } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
// 修复组件类型导入
import type { Components } from 'react-markdown/lib/ast-to-react';
import CopyButton from './CopyButton';
import { useTheme } from '../../contexts/theme';

interface MarkdownRendererProps {
  /** Markdown 内容 */
  content: string;
  /** 自定义样式类名 */
  className?: string;
  /** 默认编程语言，当代码块没有指定语言时使用 */
  defaultLanguage?: string;
  /** 搜索词，用于高亮显示 */
  searchTerm?: string;
}

/**
 * 本地图片渲染器组件
 */
const LocalImageRenderer: React.FC<{
  src: string;
  alt?: string;
  title?: string;
  theme: string;
}> = ({ src, alt, title, theme }) => {
  const [imageDataUrl, setImageDataUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadImage = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 从新格式中提取文件路径
        if (!src.startsWith('data:image/local;path,')) {
          setError('无效的本地图片格式');
          return;
        }

        // 提取Base64编码的路径
        const encodedPath = src.replace('data:image/local;path,', '');

        // 解码路径
        let imagePath;
        try {
          imagePath = atob(encodedPath);
        } catch (decodeError) {
          console.error('路径解码失败:', decodeError);
          setError('路径解码失败');
          return;
        }

        // console.log('加载本地图片:', imagePath);

        // 通过API获取图片数据
        const result = await window.electronAPI.getImageDataUrl(imagePath);

        if (result.success && result.dataUrl) {
          setImageDataUrl(result.dataUrl);
        } else {
          setError(result.error || '图片加载失败');
        }
      } catch (err: any) {
        console.error('加载本地图片失败:', err);
        setError(err.message || '图片加载失败');
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [src]);

  if (isLoading) {
    return (
      <div className="inline-flex items-center justify-center p-4 border-2 border-dashed rounded-lg my-2">
        <div className="text-center">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <div className="text-sm" style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)' }}>
            加载图片中...
          </div>
        </div>
      </div>
    );
  }

  if (error || !imageDataUrl) {
    // 解码路径用于显示
    let displayPath = src;
    if (src.startsWith('data:image/local;path,')) {
      try {
        const encodedPath = src.replace('data:image/local;path,', '');
        displayPath = atob(encodedPath);
      } catch (e) {
        displayPath = '路径解析失败';
      }
    }

    return (
      <div
        className="inline-flex items-center justify-center p-4 border-2 border-dashed rounded-lg my-2"
        style={{
          borderColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
          color: theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'
        }}
      >
        <div className="text-center">
          <div className="text-2xl mb-2">🖼️</div>
          <div className="text-sm">图片加载失败</div>
          <div className="text-xs mt-1">{alt || '未知图片'}</div>
          <div className="text-xs mt-1 font-mono break-all">{displayPath}</div>
          {error && <div className="text-xs mt-1 text-red-500">{error}</div>}
        </div>
      </div>
    );
  }

  return (
    <img
      src={imageDataUrl}
      alt={alt || ''}
      title={title}
      className="max-w-full h-auto rounded-lg shadow-sm my-2"
      style={{
        border: theme === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)'
      }}
      // onLoad={() => console.log('本地图片加载成功:', src)}
    />
  );
};

/**
 * 高亮搜索词的函数 - 简化版本，只做高亮显示
 */
const highlightSearchTerm = (
  text: string,
  searchTerm: string
): React.ReactNode => {
  if (!searchTerm) return text;

  // 创建正则表达式进行搜索词高亮
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part, index) => {
    // 检查是否是匹配的搜索词
    const testRegex = new RegExp(`^${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i');
    if (testRegex.test(part)) {
      return (
        <span
          key={index}
          className="bg-yellow-200 text-black px-1 rounded"
        >
          {part}
        </span>
      );
    }
    return part;
  });
};

/**
 * Markdown 渲染器组件
 * 支持代码块语法高亮和复制功能
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = '',
  defaultLanguage = 'shell',
  searchTerm = ''
}) => {
  const { theme } = useTheme();

  // 根据主题选择代码高亮样式
  const codeStyle = theme === 'dark' ? tomorrow : oneLight;

  // 通用的文本处理函数
  const processTextContent = React.useCallback((children: any): any => {
    if (!searchTerm) return children;

    if (typeof children === 'string') {
      return highlightSearchTerm(children, searchTerm);
    }

    if (Array.isArray(children)) {
      return children.map((child, index) => {
        if (typeof child === 'string') {
          return highlightSearchTerm(child, searchTerm);
        }
        // 对于React元素，递归处理其children
        if (React.isValidElement(child) && child.props && typeof child.props === 'object' && 'children' in child.props) {
          return React.cloneElement(child, {
            ...(child.props as any),
            children: processTextContent((child.props as any).children)
          });
        }
        return child;
      });
    }

    return children;
  }, [searchTerm]);

  // 自定义渲染器处理代码块
  const components: Components = {
    code({node, inline, className, children, ...props}: any) {
      try {
        const match = /language-(\w+)/.exec(className || '');
        const childrenContent = Array.isArray(children)
            ? children.join('')
            : String(children);
        // 如果 inline 是 undefined，我们可以通过其他方式判断是否为内联代码
        const isInline = inline !== undefined ? inline : (
            // 通过 node 类型判断
            node?.position?.start.line === node?.position?.end.line
        );

        // 处理代码块（三个反引号）
        // 如果有语言标识，使用语法高亮
        if (!isInline) {
          const codeString = childrenContent.replace(/\n$/, '');
          
          return (
            <div className="relative group">
              <SyntaxHighlighter
                style={codeStyle}
                language={match ? match[1] : defaultLanguage}
                PreTag="div"
                customStyle={{
                  borderRadius: '6px',
                  padding: '12px',
                  maxWidth: '100%',
                  overflowX: 'auto',
                  marginTop: '8px',
                  marginBottom: '8px',
                  border: theme === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)'
                }}
                {...props}
              >
                {codeString}
              </SyntaxHighlighter>
              <CopyButton code={codeString} />
            </div>
          );
        } else {
          return (
              <code
                className={`${className || ''} px-1 py-0.5 rounded`}
                style={{
                  backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0, 0, 0, 0.15)',
                  color: theme === 'dark' ? '#ffffff' : '#000000'
                }}
                {...props}
              >
                {children}
              </code>
          );
        }
      } catch (error) {
        // 如果代码块渲染失败，降级到简单的 pre 标签
        console.warn('代码块渲染失败，使用降级方案:', error);
        return (
          <pre
            className="p-3 rounded overflow-x-auto"
            style={{
              backgroundColor: theme === 'dark' ? 'rgba(26, 26, 26, 0.9)' : 'rgba(245, 245, 245, 0.9)',
              color: theme === 'dark' ? '#ffffff' : '#000000',
              border: theme === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)'
            }}
          >
            <code>{children}</code>
          </pre>
        );
      }
    },
    p: ({ children }: any) => (
      <p
        className="mb-3 break-words leading-relaxed"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)' }}
      >
        {processTextContent(children)}
      </p>
    ),
    pre: ({ children }: any) => (
      <pre className="w-full max-w-full overflow-x-auto">{children}</pre>
    ),
    ul: ({ children }: any) => (
      <ul
        className="list-disc ml-5 mb-4 space-y-1"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)' }}
      >
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol
        className="list-decimal ml-5 mb-4 space-y-1"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)' }}
      >
        {children}
      </ol>
    ),
    li: ({ children }: any) => (
      <li
        className="mb-1"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)' }}
      >
        {processTextContent(children)}
      </li>
    ),
    a: ({ href, children }: any) => (
      <a
        href={href}
        className="hover:underline"
        style={{ color: theme === 'dark' ? '#60a5fa' : '#2563eb' }}
        target="_blank"
        rel="noopener noreferrer"
      >
        {processTextContent(children)}
      </a>
    ),
    blockquote: ({ children }: any) => (
      <blockquote
        className="pl-4 italic my-4"
        style={{
          borderLeft: `4px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'}`,
          color: theme === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'
        }}
      >
        {processTextContent(children)}
      </blockquote>
    ),
    table: ({ children }: any) => (
      <div className="overflow-x-auto my-4">
        <table
          className="min-w-full"
          style={{
            borderTop: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'}`,
            borderBottom: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'}`
          }}
        >
          {children}
        </table>
      </div>
    ),
    thead: ({ children }: any) => (
      <thead
        style={{
          backgroundColor: theme === 'dark' ? 'rgba(26, 26, 26, 0.8)' : 'rgba(245, 245, 245, 0.8)'
        }}
      >
        {children}
      </thead>
    ),
    tbody: ({ children }: any) => (
      <tbody
        style={{
          borderTop: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`
        }}
      >
        {children}
      </tbody>
    ),
    tr: ({ children }: any) => (
      <tr
        style={{
          borderBottom: `1px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`
        }}
      >
        {children}
      </tr>
    ),
    th: ({ children }: any) => (
      <th
        className="px-4 py-2 text-left text-sm font-medium uppercase tracking-wider"
        style={{
          color: theme === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
        }}
      >
        {processTextContent(children)}
      </th>
    ),
    td: ({ children }: any) => (
      <td
        className="px-4 py-2 text-sm"
        style={{
          color: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
        }}
      >
        {processTextContent(children)}
      </td>
    ),
    // 添加标题元素的支持
    h1: ({ children }: any) => (
      <h1
        className="text-3xl font-bold mb-4 mt-6"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.9)' }}
      >
        {processTextContent(children)}
      </h1>
    ),
    h2: ({ children }: any) => (
      <h2
        className="text-2xl font-bold mb-3 mt-5"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.9)' }}
      >
        {processTextContent(children)}
      </h2>
    ),
    h3: ({ children }: any) => (
      <h3
        className="text-xl font-bold mb-2 mt-4"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.9)' }}
      >
        {processTextContent(children)}
      </h3>
    ),
    h4: ({ children }: any) => (
      <h4
        className="text-lg font-bold mb-2 mt-3"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.9)' }}
      >
        {processTextContent(children)}
      </h4>
    ),
    h5: ({ children }: any) => (
      <h5
        className="text-base font-bold mb-1 mt-2"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.9)' }}
      >
        {processTextContent(children)}
      </h5>
    ),
    h6: ({ children }: any) => (
      <h6
        className="text-sm font-bold mb-1 mt-2"
        style={{ color: theme === 'dark' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(0, 0, 0, 0.9)' }}
      >
        {processTextContent(children)}
      </h6>
    ),
    img: ({ src, alt, title }: any) => {
      // 处理本地图片路径
      if (src && src.startsWith('data:image/local;path,')) {
        return <LocalImageRenderer src={src} alt={alt} title={title} theme={theme} />;
      }

      // 处理普通图片
      const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
        const img = e.target as HTMLImageElement;
        console.warn('图片加载失败:', src);

        // 显示错误占位符
        img.style.display = 'none';
        const errorDiv = document.createElement('div');
        errorDiv.className = 'inline-flex items-center justify-center p-4 border-2 border-dashed rounded-lg';
        errorDiv.style.borderColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)';
        errorDiv.style.color = theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)';
        errorDiv.innerHTML = `
          <div class="text-center">
            <div class="text-2xl mb-2">🖼️</div>
            <div class="text-sm">图片加载失败</div>
            <div class="text-xs mt-1">${alt || '未知图片'}</div>
            <div class="text-xs mt-1 font-mono">${src}</div>
          </div>
        `;
        img.parentNode?.insertBefore(errorDiv, img);
      };

      const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
        // console.log('图片加载成功:', src);
      };

      return (
        <img
          src={src}
          alt={alt || ''}
          title={title}
          className="max-w-full h-auto rounded-lg shadow-sm my-2"
          style={{
            border: theme === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)'
          }}
          onError={handleImageError}
          onLoad={handleImageLoad}
          loading="lazy"
        />
      );
    }
  };

  // 渲染内容
  const renderContent = () => {
    // 渲染为Markdown
    try {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={components}
          skipHtml={false}
        >
          {content}
        </ReactMarkdown>
      );
    } catch (error) {
      console.warn('Markdown 渲染失败:', error);
      // 降级为纯文本
      return (
        <div
          className="whitespace-pre-wrap"
          style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
        >
          {content}
        </div>
      );
    }
  };

  return (
    <div className={`${className || 'markdown-body w-full max-w-full overflow-hidden'}`}>
      {renderContent()}
    </div>
  );
};

export default MarkdownRenderer;
