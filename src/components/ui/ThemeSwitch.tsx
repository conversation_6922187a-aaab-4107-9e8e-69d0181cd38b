import React from 'react';
import { useTheme } from '../../contexts/theme';
import { Sun, Moon } from 'lucide-react';

interface ThemeSwitchProps {
  className?: string;
}

export const ThemeSwitch: React.FC<ThemeSwitchProps> = ({ className = '' }) => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className={`flex items-center ${className}`}>
      <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          checked={theme === 'light'}
          onChange={toggleTheme}
          className="sr-only"
        />
        <div className="relative w-11 h-6 rounded-full transition-colors duration-200 ease-in-out focus:outline-none theme-switch-container"
          style={{
            backgroundColor: 'var(--bg-tertiary)',
          } as React.CSSProperties}
        >
          {/* 滑块背景 */}
          <div
            className="absolute inset-0 rounded-full transition-colors duration-200"
            style={{
              backgroundColor: theme === 'light' ? 'var(--accent-primary)' : 'var(--bg-tertiary)',
            }}
          />

          {/* 滑块按钮 */}
          <div
            className={`absolute top-0.5 left-0.5 w-5 h-5 rounded-full shadow-md transform transition-transform duration-200 ease-in-out flex items-center justify-center ${
              theme === 'light' ? 'translate-x-5' : 'translate-x-0'
            }`}
            style={{
              backgroundColor: 'var(--text-primary)',
            }}
          >
            {theme === 'light' ? (
              <Sun className="w-3 h-3" style={{ color: '#eab308' }} />
            ) : (
              <Moon className="w-3 h-3" style={{ color: 'var(--bg-primary)' }} />
            )}
          </div>
        </div>

        {/* 标签 */}
        <span className="ml-2 text-xs theme-text-secondary">
          {theme === 'light' ? '浅色' : '深色'}
        </span>
      </label>
    </div>
  );
};
