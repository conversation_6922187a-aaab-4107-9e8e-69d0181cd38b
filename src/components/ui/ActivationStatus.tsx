import React, { useState, useEffect, useCallback, useMemo } from "react"
import { useTheme } from "../../contexts/theme"

interface ActivationInfo {
  remainingCalls: number
  isPermanent: boolean
  planType?: string
}

interface ActivationStatusProps {
  className?: string
}

const ActivationStatus: React.FC<ActivationStatusProps> = ({ className = "" }) => {
  const { theme } = useTheme()
  const [activationInfo, setActivationInfo] = useState<ActivationInfo | null>(null)

  // 使用useCallback优化更新函数
  const updateActivationInfo = useCallback((data: { remainingCalls: number }) => {
    setActivationInfo(prev => {
      if (!prev) return null
      // 只有当剩余次数真正改变时才更新
      if (prev.remainingCalls === data.remainingCalls) return prev
      return {
        ...prev,
        remainingCalls: data.remainingCalls
      }
    })
  }, [])

  // 获取激活信息
  useEffect(() => {
    const fetchActivationInfo = async () => {
      try {
        const result = await window.electronAPI.getRemainingCalls()
        if (result.success) {
          setActivationInfo({
            remainingCalls: result.remainingCalls || 0,
            isPermanent: result.isPermanent || false,
            planType: result.planType || 'standard'
          })
        }
      } catch (error) {
        console.error('获取激活信息失败:', error)
      }
    }

    fetchActivationInfo()

    // 监听剩余调用次数更新事件
    const unsubscribe = window.electronAPI.onRemainingCallsUpdated?.(updateActivationInfo)

    return () => {
      unsubscribe?.()
    }
  }, [updateActivationInfo])

  // 使用useMemo优化显示文本计算
  const displayText = useMemo(() => {
    if (!activationInfo) return null
    const planName = activationInfo.planType === 'ultra' ? 'Ultra版' : '标准版'
    return activationInfo.isPermanent
      ? `${planName}-永久`
      : `${planName}-${activationInfo.remainingCalls}次`
  }, [activationInfo])

  // 如果没有激活信息，不显示组件
  if (!activationInfo || !displayText) {
    return null
  }

  return (
    <div
      className={`flex items-center gap-2 px-2 py-1.5 text-[11px] min-w-[85px] leading-none ${className}`}
      style={{
        backgroundColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        color: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)',
        backdropFilter: 'blur(4px)',
        borderRadius: '6px'
      }}
    >
      <span>{displayText}</span>
    </div>
  )
}

export default ActivationStatus
