import React from 'react';
import { useTheme } from '../../contexts/theme';
import { Sun, Moon } from 'lucide-react';

interface ThemeToggleProps {
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`
        flex items-center justify-center w-10 h-10 rounded-lg
        bg-[var(--bg-secondary)] border border-[var(--border-primary)]
        text-[var(--text-primary)] hover:bg-[var(--bg-tertiary)]
        transition-all duration-200 ease-in-out
        ${className}
      `}
      title={theme === 'dark' ? '切换到浅色主题' : '切换到深色主题'}
    >
      {theme === 'dark' ? (
        <Sun className="w-5 h-5" />
      ) : (
        <Moon className="w-5 h-5" />
      )}
    </button>
  );
};

interface ThemeSelectProps {
  value: 'dark' | 'light';
  onChange: (theme: 'dark' | 'light') => void;
  className?: string;
}

export const ThemeSelect: React.FC<ThemeSelectProps> = ({ 
  value, 
  onChange, 
  className = '' 
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className="text-sm font-medium text-[var(--text-primary)]">
        主题外观
      </label>
      <div className="grid grid-cols-2 gap-2">
        <button
          type="button"
          onClick={() => onChange('dark')}
          className={`
            p-3 rounded-lg border transition-all duration-200
            ${value === 'dark' 
              ? 'border-[var(--accent-primary)] bg-[var(--accent-primary)]/10' 
              : 'border-[var(--border-primary)] bg-[var(--bg-secondary)]'
            }
          `}
        >
          <div className="flex items-center gap-2">
            <Moon className="w-4 h-4 text-[var(--text-primary)]" />
            <span className="text-sm text-[var(--text-primary)]">深色</span>
          </div>
          <div className="mt-2 h-8 rounded bg-gray-900 border border-gray-700"></div>
        </button>
        
        <button
          type="button"
          onClick={() => onChange('light')}
          className={`
            p-3 rounded-lg border transition-all duration-200
            ${value === 'light' 
              ? 'border-[var(--accent-primary)] bg-[var(--accent-primary)]/10' 
              : 'border-[var(--border-primary)] bg-[var(--bg-secondary)]'
            }
          `}
        >
          <div className="flex items-center gap-2">
            <Sun className="w-4 h-4 text-[var(--text-primary)]" />
            <span className="text-sm text-[var(--text-primary)]">浅色</span>
          </div>
          <div className="mt-2 h-8 rounded bg-white border border-gray-200"></div>
        </button>
      </div>
    </div>
  );
};
