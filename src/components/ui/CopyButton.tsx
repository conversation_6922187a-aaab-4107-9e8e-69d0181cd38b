import React, { useState } from 'react';

interface CopyButtonProps {
  /** 要复制的代码内容 */
  code: string;
  /** 自定义样式类名 */
  className?: string;
  /** 复制成功后的提示文本 */
  copiedText?: string;
  /** 默认提示文本 */
  defaultText?: string;
}

/**
 * 复制按钮组件
 * 用于复制代码块内容到剪贴板
 */
const CopyButton: React.FC<CopyButtonProps> = ({ 
  code, 
  className = '',
  copiedText = '已复制!',
  defaultText = '复制代码'
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const baseClasses = "absolute top-2 right-2 p-1.5 rounded bg-gray-700 hover:bg-gray-600 transition-colors duration-200 text-gray-300 hover:text-white";
  const finalClasses = className ? `${baseClasses} ${className}` : baseClasses;

  return (
    <button
      onClick={handleCopy}
      className={finalClasses}
      title={copied ? copiedText : defaultText}
    >
      {copied ? (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path 
            fillRule="evenodd" 
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
            clipRule="evenodd" 
          />
        </svg>
      ) : (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
          <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
        </svg>
      )}
    </button>
  );
};

export default CopyButton;
