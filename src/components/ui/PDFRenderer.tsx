import React, { useState, useCallback, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { useTheme } from '../../contexts/theme';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

// 直接使用 CDN 作为 worker 源，避免本地路径问题
pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

console.log('PDF.js worker 路径设置为:', pdfjs.GlobalWorkerOptions.workerSrc);

interface PDFRendererProps {
  /** PDF文件路径 */
  filePath: string;
  /** 自定义样式类名 */
  className?: string;
  /** 搜索词，用于高亮显示 */
  searchTerm?: string;
  /** 显示模式：'single' 单页模式，'continuous' 连续滚动模式 */
  displayMode?: 'single' | 'continuous';
}

/**
 * PDF渲染器组件
 * 基于react-pdf实现PDF文件的显示和基本操作
 */
const PDFRenderer: React.FC<PDFRendererProps> = ({
  filePath,
  className = '',
  searchTerm = '',
  displayMode = 'single'
}) => {
  const { theme } = useTheme();
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfData, setPdfData] = useState<ArrayBuffer | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);

  // 显示模式状态（允许用户切换）
  const [currentDisplayMode, setCurrentDisplayMode] = useState<'single' | 'continuous'>(displayMode);

  // 懒加载相关状态
  const [visiblePages, setVisiblePages] = useState<Set<number>>(new Set([1, 2, 3]));

  // 加载PDF文件数据
  useEffect(() => {
    const loadPDFData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 通过Electron API读取PDF文件
        const result = await window.electronAPI.readPDFFile(filePath);
        if (result.success && result.data) {
          setPdfData(result.data);
        } else {
          setError(result.error || 'PDF文件读取失败');
        }
      } catch (err: any) {
        console.error('加载PDF文件失败:', err);
        setError(err.message || 'PDF文件加载失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (filePath) {
      loadPDFData();
    }
  }, [filePath]);

  // PDF文档加载成功回调
  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPageNumber(1);
    setIsLoading(false);
  }, []);

  // PDF文档加载失败回调
  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('PDF文档加载失败:', error);

    // 如果是 worker 相关错误，尝试重新配置
    if (error.message.includes('worker') || error.message.includes('Worker') || error.message.includes('URL.parse')) {
      console.log('检测到 worker 错误，尝试禁用 worker...');
      pdfjs.GlobalWorkerOptions.workerSrc = '';
      setError('PDF worker 出现问题，已切换到兼容模式。请刷新页面重试。');
    } else {
      setError(error.message || 'PDF文档加载失败');
    }

    setIsLoading(false);
  }, []);

  // 页面导航
  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(1, prev - 1));
  }, []);

  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(numPages, prev + 1));
  }, [numPages]);

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
    }
  }, [numPages]);

  // 缩放控制
  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(3.0, prev + 0.2));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(0.5, prev - 0.2));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(1.0);
  }, []);

  // 切换显示模式
  const toggleDisplayMode = useCallback(() => {
    setCurrentDisplayMode(prev => prev === 'single' ? 'continuous' : 'single');
  }, []);

  // 懒加载：根据滚动位置更新可见页面
  const updateVisiblePages = useCallback((scrollTop: number, containerHeight: number) => {
    if (currentDisplayMode !== 'continuous' || numPages === 0) return;

    // 估算每页高度（这里使用一个大概值，实际应该根据页面渲染后的高度计算）
    const estimatedPageHeight = 800 * scale + 16; // 页面高度 + 间距
    const currentPage = Math.floor(scrollTop / estimatedPageHeight) + 1;

    // 显示当前页及前后各1-2页
    const newVisiblePages = new Set<number>();
    for (let i = Math.max(1, currentPage - 1); i <= Math.min(numPages, currentPage + 2); i++) {
      newVisiblePages.add(i);
    }

    setVisiblePages(newVisiblePages);
  }, [currentDisplayMode, numPages, scale]);

  // 外部打开PDF
  const handleOpenExternal = useCallback(() => {
    window.electronAPI?.openExternal?.(filePath);
  }, [filePath]);

  // 重试加载PDF
  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    setError(null);
    setIsLoading(true);

    // 重新配置 worker
    if (retryCount === 0) {
      // 第一次重试：禁用 worker
      pdfjs.GlobalWorkerOptions.workerSrc = '';
      console.log('重试 1: 禁用 worker');
    } else if (retryCount === 1) {
      // 第二次重试：使用更旧的稳定版本
      pdfjs.GlobalWorkerOptions.workerSrc = 'https://unpkg.com/pdfjs-dist@2.16.105/build/pdf.worker.min.js';
      console.log('重试 2: 使用旧版本 worker');
    }
  }, [retryCount]);

  // 键盘快捷键处理（移除缩放快捷键，避免与全局快捷键冲突）
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target && (e.target as HTMLElement).tagName === 'INPUT') {
        return; // 如果焦点在输入框中，不处理快捷键
      }

      switch (e.key) {
        case 'ArrowLeft':
          if (currentDisplayMode === 'single') {
            e.preventDefault();
            goToPrevPage();
          }
          break;
        case 'ArrowRight':
          if (currentDisplayMode === 'single') {
            e.preventDefault();
            goToNextPage();
          }
          break;
        // 移除缩放相关快捷键，避免与全局快捷键冲突
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [goToPrevPage, goToNextPage, currentDisplayMode]);

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center space-y-4">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="theme-text-secondary">加载PDF中...</p>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error || !pdfData) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center space-y-4">
          <div className="text-6xl">❌</div>
          <h3 className="text-xl font-medium theme-text-primary">PDF加载失败</h3>
          <p className="theme-text-secondary">{error || '无法读取PDF文件'}</p>
          <div className="flex gap-3 justify-center">
            {retryCount < 2 && (
              <button
                onClick={handleRetry}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                重试 ({retryCount + 1}/3)
              </button>
            )}
            <button
              onClick={handleOpenExternal}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              使用系统程序打开
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* PDF工具栏 */}
      <div className={`flex items-center justify-between p-3 border-b ${
        theme === 'dark' 
          ? 'theme-bg-secondary theme-border-primary' 
          : 'bg-gray-50 border-gray-200'
      }`}>
        <div className="flex items-center gap-3">
          {/* 显示模式切换 */}
          <div className="flex items-center gap-1">
            <button
              onClick={toggleDisplayMode}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                currentDisplayMode === 'single'
                  ? 'bg-blue-600 text-white'
                  : 'theme-bg-tertiary theme-text-primary hover:theme-bg-secondary'
              }`}
            >
              单页
            </button>
            <button
              onClick={toggleDisplayMode}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                currentDisplayMode === 'continuous'
                  ? 'bg-blue-600 text-white'
                  : 'theme-bg-tertiary theme-text-primary hover:theme-bg-secondary'
              }`}
            >
              连续
            </button>
          </div>

          {/* 页面导航 - 仅在单页模式下显示 */}
          {currentDisplayMode === 'single' && (
            <div className="flex items-center gap-1">
              <button
                onClick={goToPrevPage}
                disabled={pageNumber <= 1}
                className="px-2 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors disabled:opacity-50"
              >
                ←
              </button>
              <div className="flex items-center gap-1">
                <input
                  type="number"
                  value={pageNumber}
                  onChange={(e) => goToPage(parseInt(e.target.value) || 1)}
                  min={1}
                  max={numPages}
                  className="w-16 px-2 py-1 text-sm text-center theme-bg-primary theme-text-primary theme-border-primary border rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <span className="text-sm theme-text-secondary">/ {numPages}</span>
              </div>
              <button
                onClick={goToNextPage}
                disabled={pageNumber >= numPages}
                className="px-2 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors disabled:opacity-50"
              >
                →
              </button>
            </div>
          )}

          {/* 连续模式下显示总页数 */}
          {currentDisplayMode === 'continuous' && (
            <div className="flex items-center gap-1">
              <span className="text-sm theme-text-secondary">总共 {numPages} 页</span>
            </div>
          )}

          {/* 缩放控制 */}
          <div className="flex items-center gap-1">
            <button
              onClick={zoomOut}
              className="px-2 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors"
            >
              -
            </button>
            <span className="text-sm theme-text-secondary min-w-[3rem] text-center">
              {Math.round(scale * 100)}%
            </span>
            <button
              onClick={zoomIn}
              className="px-2 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors"
            >
              +
            </button>
            <button
              onClick={resetZoom}
              className="px-2 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors"
            >
              重置
            </button>
          </div>
        </div>

        <button
          onClick={handleOpenExternal}
          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          外部打开
        </button>
      </div>

      {/* PDF内容区域 */}
      <div
        className="flex-1 overflow-auto p-4 flex justify-center"
        onScroll={(e) => {
          if (currentDisplayMode === 'continuous') {
            const target = e.target as HTMLDivElement;
            updateVisiblePages(target.scrollTop, target.clientHeight);
          }
        }}
      >
        <div className="pdf-container">
          <Document
            file={pdfData}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div className="flex items-center justify-center p-8">
                <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              </div>
            }
            error={
              <div className="text-center p-8">
                <p className="theme-text-secondary">PDF文档加载失败</p>
              </div>
            }
          >
            {currentDisplayMode === 'continuous' ? (
              // 连续滚动模式：懒加载显示页面
              <div className="space-y-4">
                {Array.from(new Array(numPages), (el, index) => {
                  const pageNum = index + 1;
                  const isVisible = visiblePages.has(pageNum);

                  return (
                    <div key={`page_${pageNum}`} className="flex justify-center">
                      {isVisible ? (
                        <Page
                          pageNumber={pageNum}
                          scale={scale}
                          loading={
                            <div className="flex items-center justify-center p-8">
                              <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                            </div>
                          }
                          error={
                            <div className="text-center p-8">
                              <p className="theme-text-secondary">页面 {pageNum} 加载失败</p>
                            </div>
                          }
                          className={`pdf-page ${theme === 'dark' ? 'pdf-page-dark' : 'pdf-page-light'}`}
                        />
                      ) : (
                        // 占位符，保持布局
                        <div
                          className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600"
                          style={{ width: '595px', height: `${800 * scale}px` }}
                        >
                          <p className="theme-text-secondary">页面 {pageNum}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              // 单页模式：只显示当前页面
              <Page
                pageNumber={pageNumber}
                scale={scale}
                loading={
                  <div className="flex items-center justify-center p-8">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                }
                error={
                  <div className="text-center p-8">
                    <p className="theme-text-secondary">页面加载失败</p>
                  </div>
                }
                className={`pdf-page ${theme === 'dark' ? 'pdf-page-dark' : 'pdf-page-light'}`}
              />
            )}
          </Document>
        </div>
      </div>

      {/* 底部提示信息 */}
      <div className={`px-4 py-2 border-t text-xs theme-text-secondary ${
        theme === 'dark'
          ? 'theme-bg-secondary theme-border-primary'
          : 'bg-gray-50 border-gray-200'
      }`}>
        {currentDisplayMode === 'single' && (
          <>
            快捷键:
            <kbd className={`mx-1 px-1 py-0.5 rounded ${
              theme === 'dark'
                ? 'bg-gray-700 text-gray-200'
                : 'bg-gray-200 text-gray-800'
            }`}>←/→</kbd> 翻页
          </>
        )}
        {currentDisplayMode === 'continuous' && (
          <>
            提示: 使用滚轮或滚动条浏览多页内容，支持懒加载优化
          </>
        )}
      </div>
    </div>
  );
};

export default PDFRenderer;
