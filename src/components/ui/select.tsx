import * as React from "react"
import { cn } from "../../lib/utils"
import { ChevronDown, Check } from "lucide-react"

// Tooltip component for long descriptions
const Tooltip: React.FC<{ content: string; children: React.ReactNode }> = ({ content, children }) => {
  const [isVisible, setIsVisible] = React.useState(false)
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const tooltipRef = React.useRef<HTMLDivElement>(null)
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  const handleMouseEnter = (e: React.MouseEvent) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    const rect = e.currentTarget.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // Calculate optimal position
    let x = rect.left + rect.width / 2
    let y = rect.top - 10

    // Adjust if tooltip would go off screen
    if (x + 200 > viewportWidth) {
      x = viewportWidth - 220
    }
    if (x - 200 < 0) {
      x = 220
    }
    if (y - 100 < 0) {
      y = rect.bottom + 10
    }

    setPosition({ x, y })

    // Delay showing tooltip slightly to avoid flicker
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true)
    }, 300)
  }

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsVisible(false)
  }

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return (
    <div
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className="fixed z-[1002] px-4 py-3 text-sm rounded-lg shadow-xl max-w-md backdrop-blur-sm"
          style={{
            left: position.x,
            top: position.y,
            transform: position.y > window.innerHeight / 2 ? 'translate(-50%, -100%)' : 'translate(-50%, 10px)',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            lineHeight: '1.5',
            color: 'var(--text-primary)',
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-primary)',
          }}
        >
          {content}
        </div>
      )}
    </div>
  )
}

export interface SelectOption {
  value: string
  label: string
  description?: string
  supportsMultimodal?: boolean
}

export interface SelectProps {
  value: string
  onValueChange: (value: string) => void
  options: SelectOption[]
  placeholder?: string
  allowCustom?: boolean
  customPlaceholder?: string
  className?: string
  disabled?: boolean
  isValidating?: boolean
  validationError?: string
}

const Select = React.forwardRef<HTMLDivElement, SelectProps>(
  ({ 
    value, 
    onValueChange, 
    options, 
    placeholder = "选择选项...", 
    allowCustom = false,
    customPlaceholder = "输入自定义模型ID...",
    className,
    disabled = false,
    isValidating = false,
    validationError,
    ...props 
  }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false)
    const [isCustomMode, setIsCustomMode] = React.useState(false)
    const [customValue, setCustomValue] = React.useState("")
    const dropdownRef = React.useRef<HTMLDivElement>(null)

    // Check if current value is a custom value (not in predefined options)
    const isCurrentValueCustom = !options.some(option => option.value === value)
    
    // Initialize custom mode and value based on current value
    React.useEffect(() => {
      if (isCurrentValueCustom && value) {
        setIsCustomMode(true)
        setCustomValue(value)
      }
    }, [value, isCurrentValueCustom])

    // Close dropdown when clicking outside
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false)
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [])

    const handleOptionSelect = (optionValue: string) => {
      if (optionValue === "custom") {
        setIsCustomMode(true)
        setCustomValue("")
        return
      }
      
      setIsCustomMode(false)
      setCustomValue("")
      onValueChange(optionValue)
      setIsOpen(false)
    }

    const handleCustomSubmit = () => {
      if (customValue.trim()) {
        onValueChange(customValue.trim())
        setIsOpen(false)
      }
    }

    const handleCustomKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleCustomSubmit()
      } else if (e.key === 'Escape') {
        setIsCustomMode(false)
        setCustomValue("")
        setIsOpen(false)
      }
    }

    const getDisplayValue = () => {
      if (isCurrentValueCustom && value) {
        return value
      }
      
      const selectedOption = options.find(option => option.value === value)
      return selectedOption ? selectedOption.label : placeholder
    }

    const getDisplayDescription = () => {
      if (isCurrentValueCustom) {
        return "自定义模型"
      }
      
      const selectedOption = options.find(option => option.value === value)
      return selectedOption?.description
    }

    return (
      <div className={cn("relative", className)} ref={dropdownRef} {...props}>
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={cn(
            "flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm transition-colors",
            "focus:outline-none",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          style={{
            backgroundColor: 'var(--bg-secondary)',
            border: `1px solid ${validationError ? '#ef4444' : 'var(--border-primary)'}`,
            color: 'var(--text-primary)',
          }}
          onMouseEnter={(e) => {
            if (!disabled) {
              e.currentTarget.style.borderColor = validationError ? '#ef4444' : 'var(--border-primary)';
            }
          }}
          onFocus={(e) => {
            if (!disabled) {
              e.currentTarget.style.borderColor = validationError ? '#ef4444' : 'var(--border-primary)';
            }
          }}
        >
          <div className="flex flex-col items-start flex-1 min-w-0">
            <span className={cn(
              "truncate",
              !value && "opacity-50"
            )} style={{ color: 'var(--text-primary)' }}>
              {getDisplayValue()}
            </span>
            {getDisplayDescription() && (
              <Tooltip content={getDisplayDescription()!}>
                <span className="text-xs line-clamp-2 leading-relaxed mt-0.5" style={{ color: 'var(--text-tertiary)' }}>
                  {getDisplayDescription()}
                </span>
              </Tooltip>
            )}
          </div>
          <div className="flex items-center gap-2 ml-2">
            {isValidating && (
              <div className="w-4 h-4 border-2 rounded-full animate-spin" style={{
                borderColor: 'var(--text-tertiary)',
                borderTopColor: 'var(--text-primary)'
              }} />
            )}
            <ChevronDown className={cn(
              "h-4 w-4 transition-transform",
              isOpen && "rotate-180"
            )} style={{ color: 'var(--text-tertiary)' }} />
          </div>
        </button>

        {validationError && (
          <p className="mt-1 text-xs" style={{ color: '#ef4444' }}>{validationError}</p>
        )}

        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg z-[1001] max-h-80 overflow-y-auto" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-primary)',
          }}>
            <div className="py-1">
              {options.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleOptionSelect(option.value)}
                  className="w-full text-left px-3 py-3 text-sm transition-colors"
                  style={{
                    backgroundColor: value === option.value ? 'var(--bg-tertiary)' : 'transparent',
                    color: value === option.value ? 'var(--text-primary)' : 'var(--text-secondary)',
                  }}
                  onMouseEnter={(e) => {
                    if (value !== option.value) {
                      e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (value !== option.value) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex flex-col flex-1 min-w-0 pr-2">
                      <span className="font-medium truncate">{option.label}</span>
                      {option.description && (
                        <Tooltip content={option.description}>
                          <div className="text-xs mt-1.5 leading-relaxed line-clamp-3 max-w-full" style={{ color: 'var(--text-tertiary)' }}>
                            {option.description}
                          </div>
                        </Tooltip>
                      )}
                      {option.supportsMultimodal !== undefined && (
                        <span className="text-xs italic mt-1.5" style={{ color: 'var(--text-tertiary)' }}>
                          {option.supportsMultimodal ? "支持图像分析" : "仅支持文本"}
                        </span>
                      )}
                    </div>
                    {value === option.value && (
                      <Check className="h-4 w-4 flex-shrink-0 mt-0.5" style={{ color: 'var(--text-primary)' }} />
                    )}
                  </div>
                </button>
              ))}
              
              {allowCustom && (
                <>
                  <div className="my-1" style={{ borderTop: '1px solid var(--border-primary)' }} />
                  {!isCustomMode ? (
                    <button
                      onClick={() => handleOptionSelect("custom")}
                      className="w-full text-left px-3 py-2 text-sm transition-colors"
                      style={{ color: 'var(--text-secondary)' }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <span className="font-medium">自定义模型</span>
                        <span className="text-xs" style={{ color: 'var(--text-tertiary)' }}>输入自定义模型ID</span>
                      </div>
                    </button>
                  ) : (
                    <div className="px-3 py-2">
                      <input
                        type="text"
                        value={customValue}
                        onChange={(e) => setCustomValue(e.target.value)}
                        onKeyDown={handleCustomKeyDown}
                        placeholder={customPlaceholder}
                        className="w-full px-2 py-1 text-sm rounded focus:outline-none"
                        style={{
                          backgroundColor: 'var(--bg-secondary)',
                          border: '1px solid var(--border-primary)',
                          color: 'var(--text-primary)',
                        }}
                        onFocus={(e) => {
                          e.currentTarget.style.borderColor = 'var(--border-primary)';
                        }}
                        autoFocus
                      />
                      <div className="flex gap-2 mt-2">
                        <button
                          onClick={handleCustomSubmit}
                          disabled={!customValue.trim()}
                          className="px-2 py-1 text-xs rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          style={{
                            backgroundColor: 'var(--bg-tertiary)',
                            color: 'var(--text-primary)',
                          }}
                          onMouseEnter={(e) => {
                            if (customValue.trim()) {
                              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (customValue.trim()) {
                              e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                            }
                          }}
                        >
                          确认
                        </button>
                        <button
                          onClick={() => {
                            setIsCustomMode(false)
                            setCustomValue("")
                          }}
                          className="px-2 py-1 text-xs rounded transition-colors"
                          style={{
                            backgroundColor: 'var(--bg-secondary)',
                            color: 'var(--text-tertiary)',
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                          }}
                        >
                          取消
                        </button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }
)

Select.displayName = "Select"

export { Select }
