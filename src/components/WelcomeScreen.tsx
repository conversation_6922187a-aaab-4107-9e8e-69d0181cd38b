import React from 'react';
import { Button } from './ui/button';
import { ThemeToggle } from './ui/ThemeToggle';

interface WelcomeScreenProps {
  onOpenSettings: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onOpenSettings }) => {
  return (
    <div className="theme-bg-primary min-h-screen flex flex-col items-center justify-center p-8">
      <div className="max-w-md w-full theme-bg-primary border theme-border-primary rounded-xl p-6 shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold theme-text-primary flex items-center gap-2">
            <span>Interview Artifact</span>
            <span className="text-sm font-normal px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded-md">Unlocked Edition</span>
          </h1>
          <ThemeToggle />
        </div>

        <div className="mb-8">
          <h2 className="text-lg font-medium theme-text-primary mb-3">欢迎使用面试编程助手</h2>
          <p className="theme-text-secondary text-sm mb-4">
            这个应用程序通过提供AI驱动的编程问题解决方案，
            帮助您在技术面试中取得成功。
          </p>
          <div className="theme-bg-secondary theme-border-primary border rounded-lg p-4 mb-4">
            <h3 className="theme-text-primary font-medium mb-2">全局快捷键</h3>
            <ul className="space-y-2">
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">切换显示/隐藏</span>
                <span className="theme-text-secondary">Ctrl+B / Cmd+B</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">鼠标穿透/不穿透</span>
                <span className="theme-text-secondary">Ctrl+N / Cmd+N</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">移动窗口</span>
                <span className="theme-text-secondary">Ctrl+方向键 / Cmd+方向键</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">截图</span>
                <span className="theme-text-secondary">Ctrl+H / Cmd+H</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">降低透明度</span>
                <span className="theme-text-secondary">Ctrl+[ / Cmd+[</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">提高透明度</span>
                <span className="theme-text-secondary">Ctrl+] / Cmd+]</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">删除最后一张截图</span>
                <span className="theme-text-secondary">Ctrl+L / Cmd+L</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">处理截图</span>
                <span className="theme-text-secondary">Ctrl+Enter / Cmd+Enter</span>
              </li>
              <li className="flex justify-between text-sm">
                  <span className="theme-text-tertiary">直接解题</span>
                  <span className="theme-text-secondary">Ctrl+Shift+Enter / Cmd+Shift+Enter</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">重置视图</span>
                <span className="theme-text-secondary">Ctrl+R / Cmd+R</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">退出应用</span>
                <span className="theme-text-secondary">Ctrl+Q / Cmd+Q</span>
              </li>
              <li className="flex justify-between text-sm">
                <span className="theme-text-tertiary">进入语音解答模式</span>
                <span className="theme-text-secondary">Ctrl+Shift+M / Cmd+Shift+M</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="theme-bg-secondary theme-border-primary border rounded-lg p-4 mb-6">
          <h3 className="theme-text-primary font-medium mb-2">开始使用</h3>
          <p className="theme-text-tertiary text-sm mb-3">
            在使用应用程序之前，您需要配置您的 大模型 API 密钥。
          </p>
          <Button
            className="w-full px-4 py-3 bg-white text-black rounded-xl font-medium hover:bg-white/90 transition-colors flex items-center justify-center gap-2"
            onClick={onOpenSettings}
          >
            打开设置
          </Button>
        </div>

        <div className="theme-text-tertiary text-xs text-center">
          首先对您的编程问题进行截图 (Ctrl+H / Cmd+H)
        </div>
      </div>
    </div>
  );
};