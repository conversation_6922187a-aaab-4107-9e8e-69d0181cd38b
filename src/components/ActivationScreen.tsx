import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { useToast } from '../contexts/toast';

interface ActivationScreenProps {
  onActivate: () => void;
}

const ActivationScreen: React.FC<ActivationScreenProps> = ({ onActivate }) => {
  const [activationCode, setActivationCode] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [remainingCalls, setRemainingCalls] = useState<number | null>(null);
  const [isPermanent, setIsPermanent] = useState<boolean>(false);
  const { showToast } = useToast();

  // 检查是否已激活
  useEffect(() => {
    const checkActivation = async () => {
      try {
        console.log("正在检查激活状态...");
        const result = await window.electronAPI.checkActivation();
        console.log("激活状态检查结果:", result);
        if (result.activated) {
          // 获取剩余调用次数
          try {
            const callsInfo = await window.electronAPI.getRemainingCalls();
            if (callsInfo.success) {
              setRemainingCalls(callsInfo.remainingCalls || 0);
              setIsPermanent(callsInfo.isPermanent || false);
            }
          } catch (error) {
            console.error('获取剩余调用次数失败', error);
          }
          
          onActivate();
        } else {
          console.log("应用未激活，请输入激活码");
        }
      } catch (error) {
        console.error('检查激活状态失败', error);
      }
    };

    checkActivation();
    
    // 监听剩余调用次数更新
    const handleRemainingCallsUpdated = (data: { remainingCalls: number }) => {
      console.log("剩余调用次数更新:", data);
      setRemainingCalls(data.remainingCalls);
    };
    
    // 监听激活必要消息
    const handleActivationRequired = () => {
      console.log("收到激活必要消息");
      // 不需要调用 onActivate，此处让用户进行激活
      // 清除任何可能存在的错误信息
      setError(null);
    };
    
    // 监听激活成功消息
    const handleActivationSuccess = () => {
      console.log("收到激活成功消息");
      showToast("激活成功", "应用已成功激活", "success");
      onActivate();
    };
    
    // 监听激活错误消息
    const handleActivationError = (message: string) => {
      console.log("收到激活错误消息:", message);
      setError(message);
      showToast("激活失败", message, "error");
    };
    
    // 添加事件监听器
    window.electronAPI.on('remaining-calls-updated', handleRemainingCallsUpdated);
    window.electronAPI.onActivationRequired(handleActivationRequired);
    window.electronAPI.onActivationSuccess(handleActivationSuccess);
    window.electronAPI.onActivationError(handleActivationError);
    
    return () => {
      // 移除事件监听器
      window.electronAPI.removeListener('remaining-calls-updated', handleRemainingCallsUpdated);
      // 理想情况下应该移除其他监听器，但目前 API 可能没有提供相应方法
    };
  }, [onActivate, showToast]);

  // 处理激活提交
  const handleActivate = async () => {
    if (!activationCode.trim()) {
      setError('请输入激活码');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await window.electronAPI.activateApp(activationCode.trim());
      
      if (result.success) {
        showToast(
          '激活成功',
          '应用已成功激活',
          'success'
        );
        
        // 获取剩余调用次数
        try {
          const callsInfo = await window.electronAPI.getRemainingCalls();
          if (callsInfo.success) {
            setRemainingCalls(callsInfo.remainingCalls || 0);
            setIsPermanent(callsInfo.isPermanent || false);
            
            if (callsInfo.isPermanent) {
              showToast(
                '永久激活',
                '您的激活码为永久授权，可无限使用',
                'success'
              );
            } else {
              showToast(
                '授权有效',
                `您的激活码有 ${callsInfo.remainingCalls} 次使用机会`,
                'neutral'
              );
            }
          }
        } catch (error) {
          console.error('获取剩余调用次数失败', error);
        }
        
        onActivate();
      } else {
        setError(result.message || '激活失败，请检查激活码是否正确');
      }
    } catch (error) {
      console.error('激活失败', error);
      setError('激活过程中发生错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black bg-opacity-70 p-6">
      <div className="w-full max-w-md p-8 space-y-6 bg-zinc-800 rounded-lg shadow-xl border border-zinc-600">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold text-white">激活您的应用</h1>
          <p className="text-zinc-300">
            请输入授权激活码以继续使用完整功能
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <div className="text-sm font-medium text-zinc-200">激活码</div>
            <Input
              id="activation-code"
              placeholder="请输入您的激活码"
              value={activationCode}
              onChange={(e) => setActivationCode(e.target.value)}
              disabled={isLoading}
              className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
            />
            {error && <p className="text-red-400 text-sm">{error}</p>}
          </div>

          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            onClick={handleActivate}
            disabled={isLoading || !activationCode.trim()}
          >
            {isLoading ? '正在激活...' : '激活'}
          </Button>
        </div>

        <div className="pt-4 text-center text-sm text-zinc-400">
          <p>
            如需获取激活码，请联系软件提供商
          </p>
        </div>
      </div>
    </div>
  );
};

export default ActivationScreen; 