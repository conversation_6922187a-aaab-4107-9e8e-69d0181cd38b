import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from '../../contexts/theme';
import { useToast } from '../../contexts/toast';
import MarkdownRenderer from '../ui/MarkdownRenderer';
import PDFViewer from './PDFViewer';
import { CheatSheetDocument, CheatSheetSearchResult } from '../../types/cheatsheet';

interface CheatSheetViewerProps {
  document: CheatSheetDocument;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
  documentIndex: number;
  totalDocuments: number;
}

const CheatSheetViewer: React.FC<CheatSheetViewerProps> = ({
  document,
  onClose,
  onNext,
  onPrevious,
  documentIndex,
  totalDocuments,
}) => {
  const { theme } = useTheme();
  const { showToast } = useToast();
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<CheatSheetSearchResult | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 加载文档内容
  useEffect(() => {
    loadDocumentContent();
  }, [document]);

  // 移除自动跳转功能，只保留搜索高亮

  // 监听快捷键事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.altKey) {
        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          onPrevious();
        } else if (e.key === 'ArrowRight') {
          e.preventDefault();
          onNext();
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      } else if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onNext, onPrevious, onClose]);

  const loadDocumentContent = async () => {
    try {
      setIsLoading(true);

      // 如果是PDF文件，不需要加载内容
      if (document.type === 'pdf') {
        setContent('');
        setIsLoading(false);
        return;
      }

      const docContent = await window.electronAPI.cheatSheetGetDocumentContent(document);

      // 检查是否是PDF文件标识
      if (docContent.startsWith('PDF_FILE:')) {
        setContent('');
      } else {
        setContent(docContent);
      }
    } catch (error: any) {
      console.error('加载文档内容失败:', error);
      showToast('错误', error.message || '加载文档内容失败', 'error');
      setContent('加载文档内容失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 搜索功能
  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults(null);
      return;
    }

    try {
      const results = await window.electronAPI.cheatSheetSearch(document, searchTerm.trim());
      setSearchResults(results);

      if (results.totalMatches === 0) {
        showToast('搜索结果', '未找到匹配的内容', 'neutral');
      } else {
        showToast('搜索结果', `找到 ${results.totalMatches} 个匹配项`, 'success');
      }
    } catch (error: any) {
      console.error('搜索失败:', error);
      showToast('错误', error.message || '搜索失败', 'error');
    }
  };

  // 移除跳转相关的函数，简化搜索功能

  // 渲染内容
  const renderContent = () => {
    if (document.type === 'pdf') {
      return <PDFViewer filePath={document.filePath} className="h-full" />;
    } else {
      return (
        <div
          ref={contentRef}
          className="h-full overflow-y-auto p-6 theme-bg-primary"
        >
          <MarkdownRenderer
            content={content}
            className="max-w-none markdown-content theme-text-primary theme-bg-secondary"
            searchTerm={searchTerm}
          />
        </div>
      );
    }
  };

  return (
    <div className={`fixed inset-0 z-50 ${
      theme === 'dark' 
        ? 'bg-black/90 backdrop-blur-sm' 
        : 'bg-white/90 backdrop-blur-sm'
    }`}>
      <div className="h-full flex flex-col">
        {/* 头部工具栏 */}
        <div className={`flex items-center justify-between p-4 border-b ${
          theme === 'dark' 
            ? 'theme-bg-secondary theme-border-primary' 
            : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold theme-text-primary">{document.name}</h1>
            <span className="text-sm theme-text-secondary">
              {documentIndex + 1} / {totalDocuments}
            </span>
            <span className={`px-2 py-1 text-xs rounded ${
              document.type === 'pdf'
                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                : document.type === 'text'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            }`}>
              {document.type === 'pdf' ? 'PDF' : document.type === 'text' ? 'Text' : 'Markdown'}
            </span>
          </div>

          {/* 搜索栏 */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
                placeholder="搜索内容 (Ctrl+F)"
                className="px-3 py-1 text-sm theme-bg-primary theme-text-primary theme-border-primary border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleSearch}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                搜索
              </button>
            </div>

            {/* 搜索结果统计 */}
            {searchResults && searchResults.totalMatches > 0 && (
              <div className="flex items-center gap-1">
                <span className="text-sm theme-text-secondary">
                  找到 {searchResults.totalMatches} 个匹配项
                </span>
              </div>
            )}

            {/* 导航按钮 */}
            <div className="flex items-center gap-1">
              <button
                onClick={onPrevious}
                disabled={totalDocuments <= 1}
                className="px-3 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors disabled:opacity-50"
              >
                ← 上一个
              </button>
              <button
                onClick={onNext}
                disabled={totalDocuments <= 1}
                className="px-3 py-1 text-sm theme-bg-tertiary theme-text-primary rounded hover:theme-bg-secondary transition-colors disabled:opacity-50"
              >
                下一个 →
              </button>
            </div>

            <button
              onClick={onClose}
              className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              关闭 (ESC)
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center gap-3">
                <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <p className="theme-text-secondary">加载中...</p>
              </div>
            </div>
          ) : (
            renderContent()
          )}
        </div>

        {/* 底部快捷键提示 */}
        <div className={`px-4 py-2 border-t text-xs theme-text-secondary ${
          theme === 'dark' 
            ? 'theme-bg-secondary theme-border-primary' 
            : 'bg-gray-50 border-gray-200'
        }`}>
          快捷键:
          <kbd className={`mx-1 px-1 py-0.5 rounded ${
            theme === 'dark'
              ? 'bg-gray-700 text-gray-200'
              : 'bg-gray-200 text-gray-800'
          }`}>Ctrl+Alt+←/→</kbd> 翻页 |
          <kbd className={`mx-1 px-1 py-0.5 rounded ${
            theme === 'dark'
              ? 'bg-gray-700 text-gray-200'
              : 'bg-gray-200 text-gray-800'
          }`}>Ctrl+F</kbd> 搜索 |
          <kbd className={`mx-1 px-1 py-0.5 rounded ${
            theme === 'dark'
              ? 'bg-gray-700 text-gray-200'
              : 'bg-gray-200 text-gray-800'
          }`}>ESC</kbd> 关闭
        </div>
      </div>
    </div>
  );
};

export default CheatSheetViewer;
