// CheatSheet types
export interface CheatSheetDocument {
  id: string;
  name: string;
  type: 'markdown' | 'pdf' | 'text';
  filePath: string; // 文件路径（必需）
  size: number; // File size in bytes
  createdAt: number;
  updatedAt: number;
}

export interface CheatSheetConfig {
  documents: CheatSheetDocument[];
  localDirectories: string[]; // 配置的本地文件目录
  maxFileSize: number; // 最大文件大小限制（字节）
  currentDocumentIndex: number; // 当前查看的文档索引
}

export interface CheatSheetSearchResult {
  matches: Array<{ line: number; content: string; index: number }>;
  totalMatches: number;
}
