export interface ElectronAPI {
  // Original methods
  openSubscriptionPortal: (authData: {
    id: string
    email: string
  }) => Promise<{ success: boolean; error?: string }>
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  clearStore: () => Promise<{ success: boolean; error?: string }>
  getScreenshots: () => Promise<{
    success: boolean
    previews?: Array<{ path: string; preview: string }> | null
    error?: string
  }>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => () => void
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void
  onUnauthorized: (callback: () => void) => () => void
  onDebugError: (callback: (error: string) => void) => () => void
  openExternalUrl: (url: string) => void
  toggleMainWindow: () => Promise<{ success: boolean; error?: string }>
  triggerScreenshot: () => Promise<{ success: boolean; error?: string }>
  triggerProcessScreenshots: () => Promise<{ success: boolean; error?: string }>
  triggerDirectAnswer: () => Promise<{ success: boolean; error?: string }>
  triggerDirectDebug: () => Promise<{ success: boolean; error?: string }>
  triggerReset: () => Promise<{ success: boolean; error?: string }>
  triggerMoveLeft: () => Promise<{ success: boolean; error?: string }>
  triggerMoveRight: () => Promise<{ success: boolean; error?: string }>
  triggerMoveUp: () => Promise<{ success: boolean; error?: string }>
  triggerMoveDown: () => Promise<{ success: boolean; error?: string }>
  onSubscriptionUpdated: (callback: () => void) => () => void
  onSubscriptionPortalClosed: (callback: () => void) => () => void
  startUpdate: () => Promise<{ success: boolean; error?: string }>
  installUpdate: () => void
  onUpdateAvailable: (callback: (info: any) => void) => () => void
  onUpdateDownloaded: (callback: (info: any) => void) => () => void

  decrementCredits: () => Promise<void>
  setInitialCredits: (credits: number) => Promise<void>
  onCreditsUpdated: (callback: (credits: number) => void) => () => void
  onOutOfCredits: (callback: () => void) => () => void
  openSettingsPortal: () => Promise<void>
  getPlatform: () => string
  
  // New methods for OpenRouter integration
  getConfig: () => Promise<{ apiKey: string; model: string }>
  updateConfig: (config: { apiKey?: string; model?: string }) => Promise<boolean>
  checkApiKey: () => Promise<boolean>
  validateApiKey: (apiKey: string) => Promise<{ valid: boolean; error?: string }>
  validateModel: (modelId: string, apiKey: string, requiresImages?: boolean) => Promise<{
    isValid: boolean;
    supportsImages: boolean;
    error?: string;
    endpoints?: any[];
    modelInfo?: {
      id: string;
      name: string;
      description: string;
      architecture: {
        tokenizer: string;
        instruct_type: string;
        modality: string;
        input_modalities: string[];
        output_modalities: string[];
      };
    };
  }>
  openLink: (url: string) => void
  onApiKeyInvalid: (callback: () => void) => () => void
  
  // 激活相关 API
  checkActivation: () => Promise<{ activated: boolean }>
  activateApp: (activationCode: string) => Promise<{ success: boolean; message: string }>
  getRemainingCalls: () => Promise<{
    success: boolean;
    remainingCalls?: number;
    isPermanent?: boolean;
    planType?: string;
    message: string
  }>
  consumeCall: () => Promise<{ 
    success: boolean; 
    remainingCalls?: number; 
    message: string 
  }>
  on: (channel: string, callback: (...args: any[]) => void) => void
  onActivationRequired: (callback: () => void) => () => void
  onActivationSuccess: (callback: () => void) => () => void
  onActivationError: (callback: (error: string) => void) => () => void
  onRemainingCallsUpdated: (callback: (data: { remainingCalls: number }) => void) => () => void
  onVoicePermissionRequired: (callback: () => void) => () => void
  onVoiceRecordingPermissionRequired: (callback: () => void) => () => void
  onDirectAnswerPermissionRequired: (callback: () => void) => () => void
  removeListener: (channel: string, callback: (...args: any[]) => void) => void

  // 语音识别相关 API
  getVoiceConfig: () => Promise<{
    selectedProvider: 'xunfei' | 'xunfei-spark' | 'tongyi-gummy';
    language: string;
    audioListeningMode: 'microphone-only' | 'system-only' | 'dual-audio';
    inputLayoutMode: 'bottom' | 'left';
    xunfei: { appId: string; apiKey: string; apiSecret: string };
    xunfeiSpark: { appId: string; apiKey: string; apiSecret: string };
    tongyiGummy: { apiKey: string };
    vocabularyTables: any[];
    activeVocabularyId?: string;
  }>
  updateVoiceConfig: (config: {
    selectedProvider?: 'xunfei' | 'xunfei-spark' | 'tongyi-gummy';
    language?: string;
    audioListeningMode?: 'microphone-only' | 'system-only' | 'dual-audio';
    inputLayoutMode?: 'bottom' | 'left';
    xunfei?: { appId: string; apiKey: string; apiSecret: string };
    xunfeiSpark?: { appId: string; apiKey: string; apiSecret: string };
    tongyiGummy?: { apiKey: string };
    vocabularyTables?: any[];
    activeVocabularyId?: string;
  }) => Promise<boolean>
  testVoiceCredentials: (provider: 'xunfei' | 'xunfei-spark' | 'tongyi-gummy', credentials: {
    appId?: string; apiKey: string; apiSecret?: string;
  }) => Promise<{ valid: boolean; error?: string }>

  // 语音聊天模式相关 API
  toggleVoiceChatMode: () => Promise<boolean>
  onVoiceChatModeChanged: (callback: (isVoiceChatMode: boolean) => void) => () => void
  onHandleCtrlEnter: (callback: () => void) => () => void
  onHandleCtrlShiftEnter: (callback: () => void) => () => void
  onToggleVoiceRecording: (callback: () => void) => () => void

  // 流式聊天 API
  streamChat: (messages: any[]) => Promise<{
    onMessage: (callback: (data: { type: 'content' | 'reasoning', token: string }) => void) => void
    onDone: (callback: () => void) => void
    onError: (callback: (error: string) => void) => void
    abort: () => void
  }>

  // 通义Gummy WebSocket APIs
  gummyConnect: (config: any, language?: string) => Promise<{ success: boolean; error?: string }>
  getOrCreateGummyHandler: (sourceIdentifier: string, config: any, language?: string) => Promise<{ success: boolean; error?: string }>
  gummyCheckAndReconnect: (sourceIdentifier?: string) => Promise<{ success: boolean; connected: boolean; error?: string }>
  gummyStartTask: (sourceIdentifier?: string) => Promise<{ success: boolean; error?: string }>
  gummyFinishTask: (sourceIdentifier?: string) => Promise<{ success: boolean; error?: string }>
  gummyDisconnect: () => Promise<{ success: boolean; error?: string }>
  gummySendAudio: (audioData: ArrayBuffer, sourceIdentifier?: string) => Promise<{ success: boolean; error?: string }>

  // 兼容旧的API
  gummyStartRecognition: (config: any, language?: string) => Promise<{ success: boolean; error?: string }>
  gummyStopRecognition: () => Promise<{ success: boolean; error?: string }>

  onGummyResult: (callback: (transcription: any) => void) => () => void
  onGummyTaskStarted: (callback: (data?: any) => void) => () => void
  onGummyTaskFinished: (callback: (data?: any) => void) => () => void
  onGummyError: (callback: (errorData: any) => void) => () => void
  onGummyEnd: (callback: (endData?: any) => void) => () => void

  // 讯飞WebSocket APIs (仅保留Spark版本)
  xunfeiStartRecognition: (config: any, language?: string, type?: string) => Promise<{ success: boolean; error?: string }>
  xunfeiSendAudio: (audioData: ArrayBuffer) => Promise<{ success: boolean; error?: string }>
  xunfeiStopRecognition: () => Promise<{ success: boolean; error?: string }>
  onXunfeiResult: (callback: (text: string) => void) => () => void
  onXunfeiError: (callback: (error: string) => void) => () => void
  onXunfeiEnd: (callback: () => void) => () => void

  // 语音提供商配置相关
  getVoiceProviderConfig: (provider: string) => Promise<any>
  isVoiceProviderConfigured: (provider: string) => Promise<boolean>

  // 语音配置事件监听
  onVoiceConfigUpdated: (callback: (config: any) => void) => () => void

  // 自定义提示词配置相关
  getCustomPromptsConfig: () => Promise<any>
  updateCustomPromptsConfig: (config: any) => Promise<boolean>
  getDefaultCustomPromptsConfig: (language?: 'chinese' | 'english') => Promise<any>

  // 小抄相关API
  cheatSheetGetDocuments: () => Promise<any[]>
  cheatSheetGetCurrentDocument: () => Promise<any | null>
  cheatSheetCreateMarkdown: (name: string, content: string) => Promise<any>
  cheatSheetSelectFile: () => Promise<string | null>
  cheatSheetAddLocalFile: (filePath: string) => Promise<any>
  cheatSheetGetDocumentContent: (document: any) => Promise<string>
  cheatSheetSearch: (document: any, searchTerm: string) => Promise<{
    matches: Array<{ line: number; content: string; index: number }>;
    totalMatches: number;
  }>
  cheatSheetNextDocument: () => Promise<any | null>
  cheatSheetPreviousDocument: () => Promise<any | null>
  cheatSheetSetCurrentDocument: (index: number) => Promise<{ success: boolean; error?: string }>
  cheatSheetDeleteDocument: (id: string) => Promise<boolean>
  cheatSheetUpdateDocument: (id: string, updates: any) => Promise<boolean>
  cheatSheetSelectDirectory: () => Promise<string | null>
  cheatSheetScanDirectory: (directoryPath: string) => Promise<any[]>
  cheatSheetAddLocalDirectory: (directory: string) => Promise<{ success: boolean; error?: string }>
  cheatSheetRemoveLocalDirectory: (directory: string) => Promise<{ success: boolean; error?: string }>
  cheatSheetGetLocalDirectories: () => Promise<string[]>

  // 系统操作
  openExternal: (path: string) => Promise<{ success: boolean; error?: string }>
  getImageDataUrl: (imagePath: string) => Promise<{ success: boolean; dataUrl?: string; error?: string }>
  readPDFFile: (pdfPath: string) => Promise<{ success: boolean; data?: ArrayBuffer; error?: string }>

  // 系统音频捕获相关API
  checkSystemAudioPermission: () => Promise<boolean>
  getSystemAudioSources: () => Promise<Array<{ id: string; name: string; type: 'screen' | 'window' }>>
  preInitializeSystemAudio: (options?: any) => Promise<{ success: boolean; error?: string }>
  startSystemAudioCapture: (options?: any) => Promise<{ success: boolean; error?: string }>
  stopSystemAudioCapture: () => Promise<{ success: boolean; error?: string }>
  clearSystemAudioCache: () => Promise<{ success: boolean; error?: string }>
  isSystemAudioCapturing: () => Promise<boolean>
  getSystemAudioConfig: () => Promise<any>
  handleSystemAudioData: (audioData: ArrayBuffer) => Promise<{ success: boolean; error?: string }>

  // 系统音频事件监听
  onSystemAudioData: (callback: (audioData: ArrayBuffer) => void) => () => void
  onSystemAudioError: (callback: (error: string) => void) => () => void
  onSystemAudioStarted: (callback: () => void) => () => void
  onSystemAudioStopped: (callback: () => void) => () => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    electron: {
      ipcRenderer: {
        on: (channel: string, func: (...args: any[]) => void) => void
        removeListener: (
          channel: string,
          func: (...args: any[]) => void
        ) => void
      }
    }
    __CREDITS__: number
    __LANGUAGE__: string
    __IS_INITIALIZED__: boolean
    __AUTH_TOKEN__?: string | null
  }
}
