import SubscribedApp from "./_pages/SubscribedApp"
import { UpdateNotification } from "./components/UpdateNotification"
import ActivationScreen from "./components/ActivationScreen"
import {
  QueryClient,
  QueryClientProvider
} from "@tanstack/react-query"
import { useEffect, useState, useCallback } from "react"
import {
  Toast,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport
} from "./components/ui/toast"
import { ToastContext } from "./contexts/toast"
import { WelcomeScreen } from "./components/WelcomeScreen"
import { SettingsDialog } from "./components/Settings/SettingsDialog"
import CheatSheetDialog from "./components/Settings/CheatSheetDialog"
import VoiceChatPanel from "./components/VoiceChat/VoiceChatPanel"
import { ThemeProvider } from "./contexts/theme"

// Create a React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0,
      gcTime: Infinity,
      retry: 1,
      refetchOnWindowFocus: false
    },
    mutations: {
      retry: 1
    }
  }
})

// Root component that provides the QueryClient
function App() {
  const [toastState, setToastState] = useState({
    open: false,
    title: "",
    description: "",
    variant: "neutral" as "neutral" | "success" | "error"
  })
  const [credits, setCredits] = useState<number>(999) // Unlimited credits
  const [currentLanguage, setCurrentLanguage] = useState<string>("python")
  const [isInitialized, setIsInitialized] = useState(false)
  const [hasApiKey, setHasApiKey] = useState(false)
  const [apiKeyDialogOpen, setApiKeyDialogOpen] = useState(false)
  // 添加激活状态
  const [isActivated, setIsActivated] = useState(false)
  const [isActivationChecked, setIsActivationChecked] = useState(false)
  // 添加语音聊天模式状态
  const [isVoiceChatMode, setIsVoiceChatMode] = useState(false)
  // 添加鼠标穿透状态
  const [isMouseClickThrough, setIsMouseClickThrough] = useState(false)
  // Note: Model selection is now handled via separate extraction/solution/debugging model settings

  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isCheatSheetSettingsOpen, setIsCheatSheetSettingsOpen] = useState(false)

  // 处理激活成功
  const handleActivationSuccess = useCallback(() => {
    setIsActivated(true)
  }, [])

  // 处理语音模式切换
  const handleVoiceChatModeToggle = useCallback(() => {
    console.log("App: 主动切换语音聊天模式");
    // 调用主进程切换语音模式
    window.electronAPI.toggleVoiceChatMode()
      .then((enabled: boolean) => {
        console.log("语音聊天模式切换结果:", enabled);
        setIsVoiceChatMode(enabled);
      })
      .catch((err: Error) => console.error("切换语音模式失败:", err));
  }, []);

  // 关闭语音聊天模式
  const handleCloseVoiceChat = useCallback(() => {
    console.log("App: 关闭语音聊天模式");
    // 直接调用切换函数，它会将模式从开启变为关闭
    window.electronAPI.toggleVoiceChatMode()
      .catch((err: Error) => console.error("关闭语音模式失败:", err));
  }, []);

  // Set unlimited credits
  const updateCredits = useCallback(() => {
    setCredits(999) // No credit limit in this version
    window.__CREDITS__ = 999
  }, [])

  // Helper function to safely update language
  const updateLanguage = useCallback((newLanguage: string) => {
    setCurrentLanguage(newLanguage)
    window.__LANGUAGE__ = newLanguage
  }, [])

  // Helper function to mark initialization complete
  const markInitialized = useCallback(() => {
    setIsInitialized(true)
    window.__IS_INITIALIZED__ = true
  }, [])

  // Show toast method
  const showToast = useCallback(
    (
      title: string,
      description: string,
      variant: "neutral" | "success" | "error"
    ) => {
      setToastState({
        open: true,
        title,
        description,
        variant
      })
    },
    []
  )

  // 监听语音聊天模式变化
  useEffect(() => {
    const unsubscribeVoiceChatMode = window.electronAPI.onVoiceChatModeChanged((isEnabled: boolean) => {
      console.log("App收到语音聊天模式状态变更:", isEnabled);
      setIsVoiceChatMode(isEnabled);
    });

    return () => {
      unsubscribeVoiceChatMode();
    };
  }, []);

  // 监听鼠标穿透状态变化
  useEffect(() => {
    const unsubscribeMouseClickThrough = window.electronAPI.onMouseClickThroughChanged((isClickThrough: boolean) => {
      console.log("App收到鼠标穿透状态变更:", isClickThrough);
      setIsMouseClickThrough(isClickThrough);
    });

    return () => {
      unsubscribeMouseClickThrough();
    };
  }, []);

  // 处理Ctrl+Enter快捷键
  useEffect(() => {
    const handleCtrlEnter = () => {
      console.log("处理Ctrl+Enter快捷键，当前语音模式:", isVoiceChatMode);
      if (isVoiceChatMode) {
        // 在语音模式下，模拟发送按钮点击
        // 这部分交由VoiceChatPanel内部处理
      } else {
        // 在普通模式下，触发处理截图
        window.electronAPI.triggerProcessScreenshots();
      }
    };

    const unsubscribeHandleCtrlEnter = window.electronAPI.onHandleCtrlEnter(handleCtrlEnter);

    return () => {
      unsubscribeHandleCtrlEnter();
    };
  }, [isVoiceChatMode]);

  // 处理Ctrl+Shift+Enter快捷键（直接解答模式）
  useEffect(() => {
    const handleCtrlShiftEnter = async () => {
      console.log("处理Ctrl+Shift+Enter快捷键，当前语音模式:", isVoiceChatMode);
      if (isVoiceChatMode) {
        // 在语音模式下，不处理直接解答
        console.log("语音模式下不支持直接解答功能");
        return;
      }

      try {
        // 检查当前是否有解决方案数据来决定使用哪种模式
        // 这里我们简化逻辑，直接使用直接解答模式
        // 具体的模式判断在ProcessingHelper中处理
        console.log("触发直接解答模式");
        await window.electronAPI.triggerDirectAnswer();
      } catch (error) {
        console.error("处理Ctrl+Shift+Enter失败:", error);
      }
    };

    const unsubscribeHandleCtrlShiftEnter = window.electronAPI.onHandleCtrlShiftEnter(handleCtrlShiftEnter);

    return () => {
      unsubscribeHandleCtrlShiftEnter();
    };
  }, [isVoiceChatMode]);

  // 处理权限相关事件
  useEffect(() => {
    // 处理语音功能权限不足事件
    const handleVoicePermissionRequired = () => {
      console.log("收到语音功能权限不足事件");
      showToast(
        "权限不足",
        "语音功能需要Ultra版本权限，请升级到Ultra版本",
        "error"
      );
    };

    // 处理应用未激活事件
    const handleActivationRequired = () => {
      console.log("收到应用未激活事件");
      showToast(
        "需要激活",
        "请先激活应用才能使用此功能",
        "error"
      );
    };

    // 处理语音录制权限不足事件
    const handleVoiceRecordingPermissionRequired = () => {
      console.log("收到语音录制权限不足事件");
      showToast(
        "权限不足",
        "语音录制功能需要Ultra版本权限，请升级到Ultra版本",
        "error"
      );
    };

    // 处理直接解答权限不足事件
    const handleDirectAnswerPermissionRequired = () => {
      console.log("收到直接解答权限不足事件");
      showToast(
        "权限不足",
        "直接解答功能需要Ultra版本权限，请升级到Ultra版本",
        "error"
      );
    };

    // 注册事件监听器
    window.electronAPI.on("voice-permission-required", handleVoicePermissionRequired);
    window.electronAPI.on("activation-required", handleActivationRequired);
    window.electronAPI.on("voice-recording-permission-required", handleVoiceRecordingPermissionRequired);
    window.electronAPI.on("direct-answer-permission-required", handleDirectAnswerPermissionRequired);

    return () => {
      // 使用removeListener清理事件监听器
      window.electronAPI.removeListener("voice-permission-required", handleVoicePermissionRequired);
      window.electronAPI.removeListener("activation-required", handleActivationRequired);
      window.electronAPI.removeListener("voice-recording-permission-required", handleVoiceRecordingPermissionRequired);
      window.electronAPI.removeListener("direct-answer-permission-required", handleDirectAnswerPermissionRequired);
    };
  }, [showToast]);

  // 检查应用激活状态
  useEffect(() => {
    const checkActivation = async () => {
      try {
        console.log("App.tsx: 正在检查激活状态...");
        // 调用主进程检查激活状态
        const result = await window.electronAPI.checkActivation()
        console.log("App.tsx: 激活状态检查结果:", result);
        setIsActivated(result.activated)
        setIsActivationChecked(true)
      } catch (error) {
        console.error("App.tsx: 激活检查失败:", error)
        // 如果检查失败，默认为未激活
        setIsActivated(false)
        setIsActivationChecked(true)
      }
    }
    
    checkActivation()
    
    // 监听激活事件
    const unsubscribeActivationRequired = window.electronAPI.onActivationRequired(() => {
      console.log("App.tsx: 收到激活必要事件");
      setIsActivated(false)
      setIsActivationChecked(true)
    })
    
    const unsubscribeActivationSuccess = window.electronAPI.onActivationSuccess(() => {
      console.log("App.tsx: 收到激活成功事件");
      setIsActivated(true)
      showToast("激活成功", "应用已成功激活", "success")
    })
    
    return () => {
      unsubscribeActivationRequired()
      unsubscribeActivationSuccess()
    }
  }, [showToast])

  // 处理小抄相关事件
  useEffect(() => {
    // 监听打开小抄模式事件
    const handleOpenCheatSheetMode = () => {
      console.log("App: 收到打开小抄模式事件，转发给SubscribedApp处理");
      // 事件会被SubscribedApp.tsx中的监听器处理
    };

    // 监听打开小抄设置事件
    const handleOpenCheatSheetSettings = () => {
      console.log("收到打开小抄设置事件");
      setIsCheatSheetSettingsOpen(true);
    };

    window.addEventListener('open-cheatsheet-mode', handleOpenCheatSheetMode);
    window.addEventListener('open-cheatsheet-settings', handleOpenCheatSheetSettings);

    return () => {
      window.removeEventListener('open-cheatsheet-mode', handleOpenCheatSheetMode);
      window.removeEventListener('open-cheatsheet-settings', handleOpenCheatSheetSettings);
    };
  }, []);

  // Check for OpenAI API key and prompt if not found
  useEffect(() => {
    const checkApiKey = async () => {
      try {
        const hasKey = await window.electronAPI.checkApiKey()
        setHasApiKey(hasKey)
        
        // If no API key is found, show the settings dialog after a short delay
        if (!hasKey) {
          setTimeout(() => {
            setIsSettingsOpen(true)
          }, 1000)
        }
      } catch (error) {
        console.error("Failed to check API key:", error)
      }
    }
    
    if (isInitialized && isActivated) {
      checkApiKey()
    }
  }, [isInitialized, isActivated])

  // Initialize dropdown handler
  useEffect(() => {
    if (isInitialized) {
      // Process all types of dropdown elements with a shorter delay
      const timer = setTimeout(() => {
        // Find both native select elements and custom dropdowns
        const selectElements = document.querySelectorAll('select');
        const customDropdowns = document.querySelectorAll('.dropdown-trigger, [role="combobox"], button:has(.dropdown)');
        
        // Enable native selects
        selectElements.forEach(dropdown => {
          dropdown.disabled = false;
        });
        
        // Enable custom dropdowns by removing any disabled attributes
        customDropdowns.forEach(dropdown => {
          if (dropdown instanceof HTMLElement) {
            dropdown.removeAttribute('disabled');
            dropdown.setAttribute('aria-disabled', 'false');
          }
        });
        
        console.log(`Enabled ${selectElements.length} select elements and ${customDropdowns.length} custom dropdowns`);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [isInitialized]);

  // Listen for settings dialog open requests
  useEffect(() => {
    const unsubscribeSettings = window.electronAPI.onShowSettings(() => {
      console.log("Show settings dialog requested");
      setIsSettingsOpen(true);
    });
    
    return () => {
      unsubscribeSettings();
    };
  }, []);

  // Initialize basic app state
  useEffect(() => {
    // Load config and set values
    const initializeApp = async () => {
      try {
        // Set unlimited credits
        updateCredits()
        
        // Load config including language and model settings
        const config = await window.electronAPI.getConfig()
        
        // Load language preference
        if (config && config.language) {
          updateLanguage(config.language)
        } else {
          updateLanguage("python")
        }
        
        // Model settings are now managed through the settings dialog
        // and stored in config as extractionModel, solutionModel, and debuggingModel
        
        markInitialized()
      } catch (error) {
        console.error("Failed to initialize app:", error)
        // Fallback to defaults
        updateLanguage("python")
        markInitialized()
      }
    }
    
    initializeApp()

    // Event listeners for process events
    const onApiKeyInvalid = () => {
      showToast(
        "API Key Invalid",
        "Your OpenAI API key appears to be invalid or has insufficient credits",
        "error"
      )
      setApiKeyDialogOpen(true)
    }

    // Setup API key invalid listener
    window.electronAPI.onApiKeyInvalid(onApiKeyInvalid)

    // Define a no-op handler for solution success
    const unsubscribeSolutionSuccess = window.electronAPI.onSolutionSuccess(
      () => {
        console.log("Solution success - no credits deducted in this version")
        // No credit deduction in this version
      }
    )

    // Cleanup function
    return () => {
      window.electronAPI.removeListener("API_KEY_INVALID", onApiKeyInvalid)
      unsubscribeSolutionSuccess()
      window.__IS_INITIALIZED__ = false
      setIsInitialized(false)
    }
  }, [updateCredits, updateLanguage, markInitialized, showToast])

  // API Key dialog management
  const handleOpenSettings = useCallback(() => {
    console.log('Opening settings dialog');
    setIsSettingsOpen(true);
  }, []);
  
  const handleCloseSettings = useCallback((open: boolean) => {
    console.log('Settings dialog state changed:', open);
    setIsSettingsOpen(open);
  }, []);

  const handleApiKeySave = useCallback(async (apiKey: string) => {
    try {
      await window.electronAPI.updateConfig({ apiKey })
      setHasApiKey(true)
      showToast("Success", "API key saved successfully", "success")
      
      // Reload app after a short delay to reinitialize with the new API key
      setTimeout(() => {
        window.location.reload()
      }, 1500)
    } catch (error) {
      console.error("Failed to save API key:", error)
      showToast("Error", "Failed to save API key", "error")
    }
  }, [showToast])

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <ToastProvider>
          <ToastContext.Provider value={{ showToast }}>
          <div className="relative">
            {/* 鼠标穿透状态指示器 */}
            {isMouseClickThrough && (
              <div className="fixed top-2 right-2 z-50 theme-bg-secondary backdrop-blur-md border theme-border-primary theme-text-primary px-3 py-1.5 rounded-lg text-xs font-medium shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 theme-bg-tertiary rounded-full"></div>
                  <span>鼠标穿透已启用</span>
                </div>
              </div>
            )}

            {/* 添加激活检查页面 */}
            {isActivationChecked && !isActivated ? (
              <ActivationScreen onActivate={handleActivationSuccess} />
            ) : (
              isInitialized ? (
                hasApiKey ? (
                  <>
                    <SubscribedApp
                      credits={credits}
                      currentLanguage={currentLanguage}
                      setLanguage={updateLanguage}
                    />
                  </>
                ) : (
                  <WelcomeScreen onOpenSettings={handleOpenSettings} />
                )
              ) : (
                <div className="min-h-screen bg-black flex items-center justify-center">
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-6 h-6 border-2 border-white/20 border-t-white/80 rounded-full animate-spin"></div>
                    <p className="text-white/60 text-sm">
                      Initializing...
                    </p>
                  </div>
                </div>
              )
            )}
            <UpdateNotification />

            {/* 语音聊天面板 - 只在应用激活状态下显示 */}
            {isActivated && (
              <VoiceChatPanel
                isVoiceChatMode={isVoiceChatMode}
                onClose={handleCloseVoiceChat}
              />
            )}
          </div>

          {/* Settings Dialog */}
          <SettingsDialog
            open={isSettingsOpen}
            onOpenChange={handleCloseSettings}
          />

          {/* CheatSheet Settings Dialog */}
          <CheatSheetDialog
            open={isCheatSheetSettingsOpen}
            onOpenChange={setIsCheatSheetSettingsOpen}
          />

          <Toast
            open={toastState.open}
            onOpenChange={(open) =>
              setToastState((prev) => ({ ...prev, open }))
            }
            variant={toastState.variant}
            duration={1500}
          >
            <ToastTitle>{toastState.title}</ToastTitle>
            <ToastDescription>{toastState.description}</ToastDescription>
          </Toast>
          <ToastViewport />
          </ToastContext.Provider>
        </ToastProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export default App