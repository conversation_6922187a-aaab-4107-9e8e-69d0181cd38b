import React, { createContext, useContext, useEffect, useState } from 'react';

export type Theme = 'dark' | 'light';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('dark');

  // 从配置加载主题
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const config = await window.electronAPI.getConfig();
        if (config && config.theme) {
          setThemeState(config.theme);
          applyTheme(config.theme);
        } else {
          // 默认深色主题
          setThemeState('dark');
          applyTheme('dark');
        }
      } catch (error) {
        console.error('Failed to load theme:', error);
        setThemeState('dark');
        applyTheme('dark');
      }
    };

    loadTheme();
  }, []);

  // 应用主题到DOM
  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement;
    root.setAttribute('data-theme', newTheme);
    
    // 更新CSS变量
    if (newTheme === 'dark') {
      // 深色主题 - 带透明效果的深色主题
      root.style.setProperty('--bg-primary', 'rgba(0, 0, 0, 0.85)');
      root.style.setProperty('--bg-secondary', 'rgba(26, 26, 26, 0.8)');
      root.style.setProperty('--bg-tertiary', 'rgba(51, 51, 51, 0.75)');
      root.style.setProperty('--text-primary', '#ffffff');
      root.style.setProperty('--text-secondary', 'rgba(255, 255, 255, 0.9)');
      root.style.setProperty('--text-tertiary', 'rgba(255, 255, 255, 0.7)');
      root.style.setProperty('--border-primary', 'rgba(255, 255, 255, 0.2)');
      root.style.setProperty('--border-secondary', 'rgba(255, 255, 255, 0.1)');
      root.style.setProperty('--accent-primary', '#3b82f6');
      root.style.setProperty('--accent-secondary', '#1d4ed8');
    } else {
      // 浅色主题 - 带透明效果的浅色主题
      root.style.setProperty('--bg-primary', 'rgba(255, 255, 255, 0.85)');
      root.style.setProperty('--bg-secondary', 'rgba(245, 245, 245, 0.8)');
      root.style.setProperty('--bg-tertiary', 'rgba(224, 224, 224, 0.75)');
      root.style.setProperty('--text-primary', '#000000');
      root.style.setProperty('--text-secondary', 'rgba(0, 0, 0, 0.8)');
      root.style.setProperty('--text-tertiary', 'rgba(0, 0, 0, 0.6)');
      root.style.setProperty('--border-primary', 'rgba(0, 0, 0, 0.2)');
      root.style.setProperty('--border-secondary', 'rgba(0, 0, 0, 0.1)');
      root.style.setProperty('--accent-primary', '#3b82f6');
      root.style.setProperty('--accent-secondary', '#1d4ed8');
    }
  };

  const setTheme = async (newTheme: Theme) => {
    try {
      // 保存到配置
      await window.electronAPI.updateConfig({ theme: newTheme });
      setThemeState(newTheme);
      applyTheme(newTheme);
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  };

  const value = {
    theme,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
