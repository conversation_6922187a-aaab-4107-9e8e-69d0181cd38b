// Debug.tsx
import {useQuery, useQueryClient} from "@tanstack/react-query"
import React, {useEffect, useRef, useState} from "react"
import {Prism as SyntaxHighlighter} from "react-syntax-highlighter"
import {dracula} from "react-syntax-highlighter/dist/esm/styles/prism"
// 由于无法安装包，我们使用@ts-ignore注释忽略类型错误
// @ts-ignore
import ReactMarkdown from 'react-markdown'
// @ts-ignore
import remarkGfm from 'remark-gfm'
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import SolutionCommands from "../components/Solutions/SolutionCommands"
import {Screenshot} from "../types/screenshots"
import {ComplexitySection, ContentSection} from "./Solutions"
import {useToast} from "../contexts/toast"
import {COMMAND_KEY} from "../utils/platform"
import MarkdownRenderer from "../components/ui/MarkdownRenderer.tsx";

async function fetchScreenshots(): Promise<Screenshot[]> {
  try {
    const existing = await window.electronAPI.getScreenshots()
    console.log("Raw screenshot data in Debug:", existing)
    return (Array.isArray(existing) ? existing : []).map((p) => ({
      id: p.path,
      path: p.path,
      preview: p.preview,
      timestamp: Date.now()
    }))
  } catch (error) {
    console.error("Error loading screenshots:", error)
    throw error
  }
}

interface DebugSolution {
  code: string;
  debug_analysis: string;
  thoughts: string;
  time_complexity: string;
  space_complexity: string;
}

interface DebugProps {
  isProcessing: boolean
  setIsProcessing: (isProcessing: boolean) => void
  currentLanguage: string
  setLanguage: (language: string) => void
}

const Debug: React.FC<DebugProps> = ({
  isProcessing,
  setIsProcessing,
  currentLanguage,
  setLanguage
}) => {
  const [tooltipVisible, setTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)
  const { showToast } = useToast()

  const { data: screenshots = [], refetch } = useQuery<Screenshot[]>({
    queryKey: ["screenshots"],
    queryFn: fetchScreenshots,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnWindowFocus: false
  })

  const [newCode, setNewCode] = useState<string | null>(null)
  const [thoughtsData, setThoughtsData] = useState<string | null>(null)
  const [timeComplexityData, setTimeComplexityData] = useState<string | null>(
    null
  )
  const [spaceComplexityData, setSpaceComplexityData] = useState<string | null>(
    null
  )
  const [debugAnalysis, setDebugAnalysis] = useState<string | null>(null)

  const queryClient = useQueryClient()
  const contentRef = useRef<HTMLDivElement>(null)

  // 添加自动滚动条样式
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* 自定义滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    `;
    document.head.appendChild(styleEl);
    
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  useEffect(() => {
    // Try to get the new solution data from cache first
    const newSolution = queryClient.getQueryData(["new_solution"]) as DebugSolution | null

    // If we have cached data, set all state variables to the cached data
    // 但只在初始化时设置，不在调试过程中覆盖状态
    if (newSolution && !isProcessing) {
      console.log("Found cached debug solution:", newSolution);

      // 简化处理逻辑，直接使用提供的数据
      setDebugAnalysis(newSolution.debug_analysis || null);

      // 只有当code不为空或默认值时才设置
      const codeValue = newSolution.code || null;
      setNewCode(codeValue && codeValue.trim() !== "" ? codeValue : null);

      // 只有当thoughts不为空或默认值时才设置
      const thoughtsValue = newSolution.thoughts || "";
      setThoughtsData(thoughtsValue.trim() !== "" && thoughtsValue !== "Debug analysis based on your screenshots" ? thoughtsValue : null);

      // 只有当复杂度不为默认值时才设置
      const timeValue = newSolution.time_complexity || null;
      const spaceValue = newSolution.space_complexity || null;
      setTimeComplexityData(timeValue !== "N/A - Debug mode" ? timeValue : null);
      setSpaceComplexityData(spaceValue !== "N/A - Debug mode" ? spaceValue : null);
    }

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => refetch()),
      window.electronAPI.onResetView(() => refetch()),
      window.electronAPI.onDebugSuccess((data: DebugSolution) => {
        console.log("Debug success event received with data:", data);
        queryClient.setQueryData(["new_solution"], data);
        
        // 简化处理逻辑，直接使用API返回的数据
        setDebugAnalysis(data.debug_analysis || null);
        
        // 只有当code不为空或默认值时才设置
        const codeValue = data.code || null;
        setNewCode(codeValue && codeValue.trim() !== "" ? codeValue : null);
        
        // 只有当thoughts不为空或默认值时才设置
        const thoughtsValue = data.thoughts || "";
        setThoughtsData(thoughtsValue.trim() !== "" && thoughtsValue !== "Debug analysis based on your screenshots" ? thoughtsValue : null);
        
        // 只有当复杂度不为默认值时才设置
        const timeValue = data.time_complexity || null;
        const spaceValue = data.space_complexity || null;
        setTimeComplexityData(timeValue !== "N/A - Debug mode" ? timeValue : null);
        setSpaceComplexityData(spaceValue !== "N/A - Debug mode" ? spaceValue : null);
        
        setIsProcessing(false);
      }),
      
      window.electronAPI.onDebugStart(() => {
        setIsProcessing(true)
        // 清空之前的调试结果和缓存数据，确保显示加载状态
        queryClient.removeQueries({
          queryKey: ["new_solution"]
        })
        setDebugAnalysis(null)
        setNewCode(null)
        setThoughtsData(null)
        setTimeComplexityData(null)
        setSpaceComplexityData(null)
      }),
      window.electronAPI.onDebugError((error: string) => {
        showToast(
          "处理失败",
          error,
          "error"
        )
        setIsProcessing(false)
        console.error("Processing error:", error)
      })
    ]

    // Set up resize observer
    const updateDimensions = () => {
      if (contentRef.current) {
        const contentHeight = Math.max(contentRef.current.scrollHeight, 600); // 设置最小高度为600
        const contentWidth = Math.max(contentRef.current.scrollWidth, 750); // 设置最小宽度为750
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [queryClient, isProcessing])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const handleDeleteExtraScreenshot = async (index: number) => {
    const screenshotToDelete = screenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch()
      } else {
        console.error("Failed to delete extra screenshot:", response.error)
      }
    } catch (error) {
      console.error("Error deleting extra screenshot:", error)
    }
  }

  return (
    <div ref={contentRef} className="relative h-screen flex flex-col">
      <div className="space-y-3 px-4 py-3 flex-1 overflow-y-auto">
      {/* Conditionally render the screenshot queue */}
      <div className="bg-transparent w-fit">
        <div className="pb-3">
          <div className="space-y-3 w-fit">
            <ScreenshotQueue
              screenshots={screenshots}
              onDeleteScreenshot={handleDeleteExtraScreenshot}
              isLoading={isProcessing}
            />
          </div>
        </div>
      </div>

      {/* Navbar of commands with the tooltip */}
      <SolutionCommands
        screenshots={screenshots}
        onTooltipVisibilityChange={handleTooltipVisibilityChange}
        isProcessing={isProcessing}
        extraScreenshots={screenshots}
        credits={window.__CREDITS__}
        currentLanguage={currentLanguage}
        setLanguage={setLanguage}
      />

      {/* Main Content */}
      <div className="w-full text-sm theme-text-primary theme-bg-secondary rounded-md mb-8">
        <div className="rounded-lg overflow-hidden">
          <div className="px-4 pt-2 text-xs theme-text-tertiary text-center">
            <span>使用鼠标滚轮或触控板上下滚动查看更多内容</span>
          </div>
          <div className="px-4 py-3 space-y-4">
            {/* Thoughts Section - 仅在有思路时显示 */}
            {thoughtsData && (
                <ContentSection
                    title={`关键点总结 (${COMMAND_KEY}+T 展开/折叠)`}
                    content={
                      <div className="theme-bg-tertiary rounded-lg p-3 border-l-4 border-blue-500">
                        <div className="theme-text-secondary leading-relaxed whitespace-pre-wrap">
                          {thoughtsData}
                        </div>
                      </div>
                    }
                    isLoading={false}
                    isThoughts={true}
                />
            )}

            {/* Debug Analysis Section - 主要渲染组件，始终显示 */}
            <DebugSection
              title="调试分析与改进建议"
              content={debugAnalysis}
              isLoading={isProcessing || !debugAnalysis}
              currentLanguage={currentLanguage}
            />

            {/* Complexity Section - 仅在有复杂度信息时显示 */}
            {(timeComplexityData || spaceComplexityData) && (
              <ComplexitySection
                timeComplexity={timeComplexityData || "N/A"}
                spaceComplexity={spaceComplexityData || "N/A"}
                isLoading={false}
              />
            )}
          </div>
        </div>
      </div>
    </div>
    </div>
  )
}

// 添加自定义代码块渲染组件
const DebugSection = ({
  title,
  content,
  isLoading,
  currentLanguage
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
  currentLanguage: string
}) => {
  return (
    <div className="space-y-2">
      <h2 className="text-[13px] font-medium theme-text-primary tracking-wide">
        {title}
      </h2>
      {isLoading ? (
        <div className="space-y-1.5">
          <div className="mt-4 flex">
            <p className="text-xs theme-text-secondary animate-pulse">
              加载调试分析...
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full relative">
          {typeof content === "string" && (
              <MarkdownRenderer content={content}
                                className="markdown-content theme-text-primary theme-bg-secondary p-4 rounded overflow-y-auto max-h-[600px]"
                                defaultLanguage={currentLanguage}
              />
          )}
        </div>
      )}
    </div>
  )
}

export default Debug
