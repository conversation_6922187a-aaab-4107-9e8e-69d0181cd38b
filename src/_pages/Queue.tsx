import React, { useState, useEffect, useRef } from "react"
import { useQuery } from "@tanstack/react-query"
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import QueueCommands from "../components/Queue/QueueCommands"

import { useToast } from "../contexts/toast"
import { Screenshot } from "../types/screenshots"

async function fetchScreenshots(): Promise<Screenshot[]> {
  try {
    const existing = await window.electronAPI.getScreenshots()
    return existing
  } catch (error) {
    console.error("Error loading screenshots:", error)
    throw error
  }
}

interface QueueProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const Queue: React.FC<QueueProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const { showToast } = useToast()


  const contentRef = useRef<HTMLDivElement>(null)

  const {
    data: screenshots = [],
    isLoading,
    refetch
  } = useQuery<Screenshot[]>({
    queryKey: ["screenshots"],
    queryFn: fetchScreenshots,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnWindowFocus: false
  })

  const handleDeleteScreenshot = async (index: number) => {
    const screenshotToDelete = screenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch() // Refetch screenshots instead of managing state directly
      } else {
        console.error("Failed to delete screenshot:", response.error)
        showToast("错误", "删除截图文件失败", "error")
      }
    } catch (error) {
      console.error("Error deleting screenshot:", error)
    }
  }

  // 添加自定义滚动条样式
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* 自定义滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    `;
    document.head.appendChild(styleEl);

    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  useEffect(() => {
    // Height update logic with debounce to prevent jittering
    let updateTimeout: NodeJS.Timeout | null = null
    let lastDimensions = { width: 0, height: 0 }

    const updateDimensions = () => {
      if (contentRef.current) {
        const contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth

        // Only update if dimensions actually changed significantly
        const widthDiff = Math.abs(contentWidth - lastDimensions.width)
        const heightDiff = Math.abs(contentHeight - lastDimensions.height)

        if (widthDiff > 5 || heightDiff > 5) {
          lastDimensions = { width: contentWidth, height: contentHeight }
          window.electronAPI.updateContentDimensions({
            width: contentWidth,
            height: contentHeight
          })
        }
      }
    }

    const debouncedUpdateDimensions = () => {
      if (updateTimeout) {
        clearTimeout(updateTimeout)
      }
      updateTimeout = setTimeout(updateDimensions, 100) // 100ms debounce
    }

    // Initialize resize observer with debounced update
    const resizeObserver = new ResizeObserver(debouncedUpdateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }

    // Initial update without debounce
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => refetch()),
      window.electronAPI.onResetView(() => refetch()),
      window.electronAPI.onDeleteLastScreenshot(async () => {
        if (screenshots.length > 0) {
          const lastScreenshot = screenshots[screenshots.length - 1];
          await handleDeleteScreenshot(screenshots.length - 1);
          // Toast removed as requested
        } else {
          showToast("没有截图", "没有可删除的截图", "neutral");
        }
      }),
      window.electronAPI.onSolutionError((error: string) => {
        showToast(
          "处理失败",
          "处理截图时出错",
          "error"
        )
        setView("queue") // Revert to queue if processing fails
        console.error("Processing error:", error)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "没有截图",
          "没有可处理的截图",
          "neutral"
        )
      }),
      // Removed out of credits handler - unlimited credits in this version
    ]

    return () => {
      if (updateTimeout) {
        clearTimeout(updateTimeout)
      }
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [screenshots])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    // Tooltip visibility changes no longer affect window dimensions
    // This callback is kept for interface compatibility
  }

  const handleOpenSettings = () => {
    window.electronAPI.openSettingsPortal();
  };
  
  return (
    <div ref={contentRef} className="bg-transparent w-1/2 min-h-0">
      <div className="px-4 py-3">
        <div className="space-y-3 w-fit">
          <ScreenshotQueue
            isLoading={false}
            screenshots={screenshots}
            onDeleteScreenshot={handleDeleteScreenshot}
          />

          <QueueCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            screenshotCount={screenshots.length}
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />
        </div>
      </div>
    </div>
  )
}

export default Queue
