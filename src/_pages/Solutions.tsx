// Solutions.tsx
import React, { useState, useEffect, useRef } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import {dracula, tomorrow} from "react-syntax-highlighter/dist/esm/styles/prism"
// 由于无法安装包，我们使用@ts-ignore注释忽略类型错误
// @ts-ignore
import ReactMarkdown from 'react-markdown'
// @ts-ignore
import remarkGfm from 'remark-gfm'
import { ChevronDown, ChevronRight } from "lucide-react";

import ScreenshotQueue from "../components/Queue/ScreenshotQueue"

import { ProblemStatementData } from "../types/solutions"
import SolutionCommands from "../components/Solutions/SolutionCommands"
import Debug from "./Debug"
import { useToast } from "../contexts/toast"
import { COMMAND_KEY } from "../utils/platform"
import MarkdownRenderer from "../components/ui/MarkdownRenderer.tsx";

// 添加接口定义
interface PreviewData {
  path: string;
  preview: string;
}

export const ContentSection = ({
  title,
  content,
  isLoading,
  isThoughts = false
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
  isThoughts?: boolean
}) => {
  const [isCollapsed, setIsCollapsed] = useState(isThoughts);
  
  // 响应全局快捷键事件
  useEffect(() => {
    if (!isThoughts) return;
    
    const handleThoughtsToggle = () => {
      setIsCollapsed(prev => !prev);
    };
    
    // 监听来自主进程的全局快捷键事件
    window.electronAPI.onToggleThoughtsVisibility(handleThoughtsToggle);
    
    return () => {
      // 清理事件监听器
      window.electronAPI.removeListener("toggle-thoughts-visibility", handleThoughtsToggle);
    };
  }, [isThoughts]);
  
  // 获取第一行内容用于折叠后显示
  const getFirstLine = () => {
    if (typeof content !== 'string') {
      return Array.isArray(content)
        ? content[0]?.toString() || '点击展开思考分析...'
        : '点击展开思考分析...';
    }
    
    const firstLine = content.split('\n')[0];
    return firstLine || '点击展开思考分析...';
  };

  return (
    <div className="space-y-2">
      <div 
        className={`flex items-center ${isThoughts ? 'cursor-pointer' : ''}`}
        onClick={isThoughts ? () => setIsCollapsed(prev => !prev) : undefined}
      >
        {isThoughts && (
          <div className="mr-2 theme-text-primary">
            {isCollapsed ? <ChevronRight size={16} /> : <ChevronDown size={16} />}
          </div>
        )}
        <h2 className="text-[13px] font-medium theme-text-primary tracking-wide">
          {title}
        </h2>
        {isThoughts && (
          <span className="ml-2 text-xs theme-text-tertiary">
            (按 {COMMAND_KEY}+T 切换)
          </span>
        )}
      </div>
      {isLoading ? (
        <div className="mt-4 flex">
          <p className="text-xs theme-text-secondary animate-pulse">
            正在提取问题描述...
          </p>
        </div>
      ) : (
        <div className={`text-[13px] leading-[1.4] theme-text-primary ${isThoughts ? 'max-w-none' : 'max-w-[600px]'}`}>
          {isThoughts && isCollapsed ? (
            <div className="theme-text-secondary italic">
              {getFirstLine()}
            </div>
          ) : (
            content
          )}
        </div>
      )}
    </div>
  )
}

const SolutionSection = ({
  title,
  content,
  isLoading,
  currentLanguage
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
  currentLanguage: string
}) => {
  return (
    <div className="space-y-2 relative">
      <h2 className="text-[13px] font-medium theme-text-primary tracking-wide">
        {title}
      </h2>
      {isLoading ? (
        <div className="space-y-1.5">
          <div className="mt-4 flex">
            <p className="text-xs theme-text-secondary animate-pulse">
              加载解决方案...
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full relative">
          {typeof content === "string" && (
            <MarkdownRenderer content={content}
                              className="markdown-content theme-text-primary theme-bg-secondary p-4 rounded"
                              defaultLanguage={currentLanguage}
            />
          )}
        </div>
      )}
    </div>
  )
}

export const ComplexitySection = ({
  timeComplexity,
  spaceComplexity,
  isLoading
}: {
  timeComplexity: string | null
  spaceComplexity: string | null
  isLoading: boolean
}) => {
  // Helper to ensure we have proper complexity values
  const formatComplexity = (complexity: string | null): string => {
    // Default if no complexity returned by LLM
    if (!complexity || complexity.trim() === "") {
      return "暂无复杂度信息";
    }

    const bigORegex = /O\([^)]+\)/i;
    // Return the complexity as is if it already has Big O notation
    if (bigORegex.test(complexity)) {
      return complexity;
    }
    
    // Concat Big O notation to the complexity
    return `O(${complexity})`;
  };
  
  const formattedTimeComplexity = formatComplexity(timeComplexity);
  const formattedSpaceComplexity = formatComplexity(spaceComplexity);
  
  return (
    <div className="space-y-2">
      <h2 className="text-[13px] font-medium theme-text-primary tracking-wide">
        算法复杂度
      </h2>
      {isLoading ? (
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          正在计算复杂度...
        </p>
      ) : (
        <div className="space-y-3">
          <div className="text-[13px] leading-[1.4] theme-text-primary theme-bg-secondary rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
              <div>
                <strong>时间复杂度:</strong> {formattedTimeComplexity}
              </div>
            </div>
          </div>
          <div className="text-[13px] leading-[1.4] theme-text-primary theme-bg-secondary rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
              <div>
                <strong>空间复杂度:</strong> {formattedSpaceComplexity}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export interface SolutionsProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}
const Solutions: React.FC<SolutionsProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const queryClient = useQueryClient()
  const contentRef = useRef<HTMLDivElement>(null)

  const [debugProcessing, setDebugProcessing] = useState(false)
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementData | null>(null)
  const [solutionData, setSolutionData] = useState<string | null>(null)
  const [thoughtsData, setThoughtsData] = useState<string | null>(null)
  const [timeComplexityData, setTimeComplexityData] = useState<string | null>(
    null
  )
  const [spaceComplexityData, setSpaceComplexityData] = useState<string | null>(
    null
  )

  // 添加自定义滚动条样式
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* 自定义滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    `;
    document.head.appendChild(styleEl);
    
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);



  const [isResetting, setIsResetting] = useState(false)

  interface Screenshot {
    id: string
    path: string
    preview: string
    timestamp: number
  }

  const [extraScreenshots, setExtraScreenshots] = useState<Screenshot[]>([])

  useEffect(() => {
    const fetchScreenshots = async () => {
      try {
        const existing = await window.electronAPI.getScreenshots()
        console.log("Raw screenshot data:", existing)
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        console.log("Processed screenshots:", screenshots)
        setExtraScreenshots(screenshots)
      } catch (error) {
        console.error("Error loading extra screenshots:", error)
        setExtraScreenshots([])
      }
    }

    fetchScreenshots()
  }, [solutionData])

  const { showToast } = useToast()

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        const contentHeight = Math.max(contentRef.current.scrollHeight, 600); // 设置最小高度为600
        const contentWidth = Math.max(contentRef.current.scrollWidth, 750);  // 设置最小宽度为750
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(async () => {
        try {
          const existing = await window.electronAPI.getScreenshots()
          const screenshots = (Array.isArray(existing) ? existing : []).map(
            (p) => ({
              id: p.path,
              path: p.path,
              preview: p.preview,
              timestamp: Date.now()
            })
          )
          setExtraScreenshots(screenshots)
        } catch (error) {
          console.error("Error loading extra screenshots:", error)
        }
      }),
      window.electronAPI.onResetView(() => {
        // Set resetting state first
        setIsResetting(true)

        // Remove queries
        queryClient.removeQueries({
          queryKey: ["solution"]
        })
        queryClient.removeQueries({
          queryKey: ["new_solution"]
        })

        // Reset screenshots
        setExtraScreenshots([])

        // After a small delay, clear the resetting state
        setTimeout(() => {
          setIsResetting(false)
        }, 0)
      }),
      window.electronAPI.onSolutionStart(() => {
        // Every time processing starts, reset relevant states
        setSolutionData(null)
        setThoughtsData(null)
        setTimeComplexityData(null)
        setSpaceComplexityData(null)
      }),
      window.electronAPI.onProblemExtracted((data: any) => {
        queryClient.setQueryData(["problem_statement"], data)
      }),
      //if there was an error processing the initial solution
      window.electronAPI.onSolutionError((error: string) => {
        showToast("处理失败", error, "error")
        // Reset solutions in the cache (even though this shouldn't ever happen) and complexities to previous states
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string
          time_complexity: string
          space_complexity: string
        } | null
        if (!solution) {
          setView("queue")
        }
        setSolutionData(solution?.code || null)
        setThoughtsData(solution?.thoughts || null)
        setTimeComplexityData(solution?.time_complexity || null)
        setSpaceComplexityData(solution?.space_complexity || null)
        console.error("Processing error:", error)
      }),
      //when the initial solution is generated, we'll set the solution data to that
      window.electronAPI.onSolutionSuccess((data: any) => {
        if (!data) {
          console.warn("Received empty or invalid solution data")
          return
        }
        console.log({ data })
        const solutionData = {
          code: data.code,
          thoughts: data.thoughts,
          time_complexity: data.time_complexity,
          space_complexity: data.space_complexity
        }

        queryClient.setQueryData(["solution"], solutionData)
        setSolutionData(solutionData.code || null)
        setThoughtsData(solutionData.thoughts || null)
        setTimeComplexityData(solutionData.time_complexity || null)
        setSpaceComplexityData(solutionData.space_complexity || null)

        // Fetch latest screenshots when solution is successful
        const fetchScreenshots = async () => {
          try {
            const existing = await window.electronAPI.getScreenshots()
            const screenshots =
              existing.previews?.map((p: PreviewData) => ({
                id: p.path,
                path: p.path,
                preview: p.preview,
                timestamp: Date.now()
              })) || []
            setExtraScreenshots(screenshots)
          } catch (error) {
            console.error("Error loading extra screenshots:", error)
            setExtraScreenshots([])
          }
        }
        fetchScreenshots()
      }),

      //########################################################
      //DEBUG EVENTS
      //########################################################
      window.electronAPI.onDebugStart(() => {
        //we'll set the debug processing state to true and use that to render a little loader
        setDebugProcessing(true)
        // 清空之前的调试缓存数据，确保新的调试过程能正确显示加载状态
        queryClient.removeQueries({
          queryKey: ["new_solution"]
        })
      }),
      //the first time debugging works, we'll set the view to debug and populate the cache with the data
      window.electronAPI.onDebugSuccess((data: any) => {
        queryClient.setQueryData(["new_solution"], data)
        setDebugProcessing(false)
      }),
      //when there was an error in the initial debugging, we'll show a toast and stop the little generating pulsing thing.
      window.electronAPI.onDebugError((error: string) => {
        showToast(
          "处理失败",
          error,
          "error"
        )
        setDebugProcessing(false)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "没有截图",
          "没有可处理的截图",
          "neutral"
        )
      }),
      // Removed out of credits handler - unlimited credits in this version
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [])

  useEffect(() => {
    setProblemStatementData(
      queryClient.getQueryData(["problem_statement"]) || null
    )
    setSolutionData(queryClient.getQueryData(["solution"]) || null)

    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.query.queryKey[0] === "problem_statement") {
        setProblemStatementData(
          queryClient.getQueryData(["problem_statement"]) || null
        )
      }
      if (event?.query.queryKey[0] === "solution") {
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string
          time_complexity: string
          space_complexity: string
        } | null

        setSolutionData(solution?.code ?? null)
        setThoughtsData(solution?.thoughts ?? null)
        setTimeComplexityData(solution?.time_complexity ?? null)
        setSpaceComplexityData(solution?.space_complexity ?? null)
      }
    })
    return () => unsubscribe()
  }, [queryClient])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    // Tooltip visibility changes no longer affect window dimensions
    // This callback is kept for interface compatibility
  }

  const handleDeleteExtraScreenshot = async (index: number) => {
    const screenshotToDelete = extraScreenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        // Fetch and update screenshots after successful deletion
        const existing = await window.electronAPI.getScreenshots()
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        setExtraScreenshots(screenshots)
      } else {
        console.error("Failed to delete extra screenshot:", response.error)
        showToast("错误", "删除截图失败", "error")
      }
    } catch (error) {
      console.error("Error deleting extra screenshot:", error)
      showToast("错误", "删除截图失败", "error")
    }
  }

  return (
    <>
      {!isResetting && (queryClient.getQueryData(["new_solution"]) || debugProcessing) ? (
        <Debug
          isProcessing={debugProcessing}
          setIsProcessing={setDebugProcessing}
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : (
        <div ref={contentRef} className="relative h-screen flex flex-col">
          <div className="space-y-3 px-4 py-3 flex-1 overflow-y-auto">
            {/* Conditionally render the screenshot queue if solutionData is available */}
            {solutionData && (
              <div className="bg-transparent w-fit">
                <div className="pb-3">
                  <div className="space-y-3 w-fit">
                    <ScreenshotQueue
                      isLoading={debugProcessing}
                      screenshots={extraScreenshots}
                      onDeleteScreenshot={handleDeleteExtraScreenshot}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Navbar of commands with the SolutionsHelper */}
            <SolutionCommands
              onTooltipVisibilityChange={handleTooltipVisibilityChange}
              isProcessing={!problemStatementData || !solutionData}
              extraScreenshots={extraScreenshots}
              credits={credits}
              currentLanguage={currentLanguage}
              setLanguage={setLanguage}
            />

            {/* Main Content - Modified width constraints */}
            <div className="w-full text-sm theme-text-primary theme-bg-secondary rounded-md mb-8">
              <div className="rounded-lg overflow-hidden">
                <div className="px-4 pt-2 text-xs text-gray-400 text-center">
                  <span>使用鼠标滚轮或触控板上下滚动查看更多内容</span>
                </div>
                <div className="px-4 py-3 space-y-4 max-w-full">
                  {!solutionData && (
                    <>
                      <ContentSection
                        title="问题描述"
                        content={problemStatementData?.problem_statement}
                        isLoading={!problemStatementData}
                      />
                      {problemStatementData && (
                        <div className="mt-4 flex">
                          <p className="text-xs theme-text-secondary animate-pulse">
                            正在生成解决方案...
                          </p>
                        </div>
                      )}
                    </>
                  )}

                  {solutionData && (
                    <>
                      <ContentSection
                        title={`我的思路 (${COMMAND_KEY} + T 展开/折叠)`}
                        content={
                          thoughtsData && (
                            <div className="bg-gray-700/50 rounded-lg p-3 border-l-4 border-blue-500">
                              <div className="theme-text-secondary leading-relaxed whitespace-pre-wrap">
                                {thoughtsData}
                              </div>
                            </div>
                          )
                        }
                        isLoading={!thoughtsData}
                        isThoughts={true}
                      />

                      <SolutionSection
                        title="解决方案"
                        content={solutionData}
                        isLoading={!solutionData}
                        currentLanguage={currentLanguage}
                      />

                      {/* 只有当时间和空间复杂度都不为空时才显示ComplexitySection */}
                      {(timeComplexityData || spaceComplexityData) && (
                        <ComplexitySection
                          timeComplexity={timeComplexityData}
                          spaceComplexity={spaceComplexityData}
                          isLoading={!timeComplexityData || !spaceComplexityData}
                        />
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Solutions
