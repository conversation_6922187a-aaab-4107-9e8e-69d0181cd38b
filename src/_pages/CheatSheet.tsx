import React, { useState, useEffect } from 'react';
import { useToast } from '../contexts/toast';
import { useTheme } from '../contexts/theme';
import CheatSheetViewer from '../components/CheatSheet/CheatSheetViewer';
import { CheatSheetDocument } from '../types/cheatsheet';

interface CheatSheetProps {
  setView: (view: "queue" | "solutions" | "debug" | "cheatsheet") => void;
}

const CheatSheet: React.FC<CheatSheetProps> = ({ setView }) => {
  const { showToast } = useToast();
  const { theme } = useTheme();
  const [documents, setDocuments] = useState<CheatSheetDocument[]>([]);
  const [currentDocument, setCurrentDocument] = useState<CheatSheetDocument | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isUltraVersion, setIsUltraVersion] = useState(false);

  // 检查Ultra版本权限
  useEffect(() => {
    console.log("CheatSheet: 组件已挂载，开始检查权限");

    const checkPermissions = async () => {
      try {
        console.log("CheatSheet: 正在检查Ultra版本权限...");
        const ultraResult = await window.electronAPI.isUltraVersion();
        console.log("CheatSheet: Ultra版本权限检查结果:", ultraResult);
        setIsUltraVersion(ultraResult);

        if (!ultraResult) {
          console.log("CheatSheet: 权限不足，显示错误提示");
          showToast(
            "权限不足",
            "小抄功能需要Ultra版本权限，请升级到Ultra版本",
            "error"
          );
          // 延迟返回队列页面
          setTimeout(() => {
            console.log("CheatSheet: 权限不足，返回队列页面");
            setView("queue");
          }, 2000);
          return;
        }

        // 如果有权限，加载文档
        console.log("CheatSheet: 权限验证通过，开始加载文档");
        await loadDocuments();
      } catch (error) {
        console.error('CheatSheet: 检查权限失败:', error);
        showToast('错误', '检查权限失败', 'error');
        setView("queue");
      }
    };

    checkPermissions();
  }, [setView, showToast]);

  // 监听快捷键事件
  useEffect(() => {
    const handleCheatSheetNext = () => {
      if (isUltraVersion && documents.length > 0) {
        nextDocument();
      }
    };

    const handleCheatSheetPrevious = () => {
      if (isUltraVersion && documents.length > 0) {
        previousDocument();
      }
    };

    window.addEventListener('cheatsheet-next', handleCheatSheetNext);
    window.addEventListener('cheatsheet-previous', handleCheatSheetPrevious);

    return () => {
      window.removeEventListener('cheatsheet-next', handleCheatSheetNext);
      window.removeEventListener('cheatsheet-previous', handleCheatSheetPrevious);
    };
  }, [isUltraVersion, documents.length]);

  // 加载文档列表
  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      const docs = await window.electronAPI.cheatSheetGetDocuments();
      setDocuments(docs || []);
      
      if (docs && docs.length > 0) {
        const current = await window.electronAPI.cheatSheetGetCurrentDocument();
        if (current) {
          setCurrentDocument(current);
          const index = docs.findIndex((doc: CheatSheetDocument) => doc.id === current.id);
          setCurrentIndex(index >= 0 ? index : 0);
        } else {
          setCurrentDocument(docs[0]);
          setCurrentIndex(0);
        }
      } else {
        setCurrentDocument(null);
        setCurrentIndex(0);
      }
    } catch (error) {
      console.error('加载文档失败:', error);
      showToast('错误', '加载文档失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 下一个文档
  const nextDocument = async () => {
    try {
      const nextDoc = await window.electronAPI.cheatSheetNextDocument();
      if (nextDoc) {
        setCurrentDocument(nextDoc);
        const index = documents.findIndex(doc => doc.id === nextDoc.id);
        setCurrentIndex(index >= 0 ? index : 0);
      }
    } catch (error) {
      console.error('切换到下一个文档失败:', error);
      showToast('错误', '切换文档失败', 'error');
    }
  };

  // 上一个文档
  const previousDocument = async () => {
    try {
      const prevDoc = await window.electronAPI.cheatSheetPreviousDocument();
      if (prevDoc) {
        setCurrentDocument(prevDoc);
        const index = documents.findIndex(doc => doc.id === prevDoc.id);
        setCurrentIndex(index >= 0 ? index : 0);
      }
    } catch (error) {
      console.error('切换到上一个文档失败:', error);
      showToast('错误', '切换文档失败', 'error');
    }
  };

  // 关闭小抄模式
  const handleClose = () => {
    setView("queue");
  };

  // 如果没有Ultra权限，显示权限提示
  if (!isUltraVersion) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        theme === 'dark' ? 'bg-black' : 'bg-white'
      }`}>
        <div className="text-center space-y-4">
          <div className="text-6xl">🔒</div>
          <h2 className="text-2xl font-bold theme-text-primary">权限不足</h2>
          <p className="theme-text-secondary max-w-md">
            小抄功能需要Ultra版本权限。请升级到Ultra版本以使用此功能。
          </p>
          <button
            onClick={() => setView("queue")}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回队列页面
          </button>
        </div>
      </div>
    );
  }

  // 加载中状态
  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        theme === 'dark' ? 'bg-black' : 'bg-white'
      }`}>
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="theme-text-secondary">加载小抄文档中...</p>
        </div>
      </div>
    );
  }

  // 没有文档时的状态
  if (documents.length === 0) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${
        theme === 'dark' ? 'bg-black' : 'bg-white'
      }`}>
        <div className="text-center space-y-4">
          <div className="text-6xl">📄</div>
          <h2 className="text-2xl font-bold theme-text-primary">暂无小抄文档</h2>
          <p className="theme-text-secondary max-w-md">
            您还没有添加任何小抄文档。请先在设置中添加一些文档。
          </p>
          <div className="space-x-4">
            <button
              onClick={() => setView("queue")}
              className="px-6 py-2 theme-bg-secondary theme-text-primary theme-border-primary border rounded-lg hover:theme-bg-tertiary transition-colors"
            >
              返回队列页面
            </button>
            <button
              onClick={() => {
                // 这里可以触发打开设置对话框的事件
                window.dispatchEvent(new CustomEvent('open-cheatsheet-settings'));
                setView("queue");
              }}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              添加文档
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 有文档时显示查看器
  if (currentDocument) {
    return (
      <CheatSheetViewer
        document={currentDocument}
        onClose={handleClose}
        onNext={nextDocument}
        onPrevious={previousDocument}
        documentIndex={currentIndex}
        totalDocuments={documents.length}
      />
    );
  }

  // 默认返回空状态
  return (
    <div className={`min-h-screen flex items-center justify-center ${
      theme === 'dark' ? 'bg-black' : 'bg-white'
    }`}>
      <div className="text-center space-y-4">
        <div className="text-6xl">❓</div>
        <h2 className="text-2xl font-bold theme-text-primary">加载失败</h2>
        <p className="theme-text-secondary">
          无法加载小抄文档，请重试。
        </p>
        <button
          onClick={() => setView("queue")}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          返回队列页面
        </button>
      </div>
    </div>
  );
};

export default CheatSheet;
