import { useState, useCallback, useRef, useEffect } from 'react';

// 音频源类型
export type AudioSourceType = 'microphone' | 'system';
export type AudioListeningMode = 'microphone-only' | 'system-only' | 'dual-audio';

// 系统音频捕获器类型声明（扩展现有的Window接口）
interface SystemAudioCaptureRenderer {
  setAudioDataCallback(callback: ((audioData: ArrayBuffer) => void) | null): void;
  startCapture(sourceId: string, options?: any): Promise<boolean>;
  stopCapture(): void;
  isActive(): boolean;
  preInitialize?(): Promise<void>;
}

declare global {
  interface Window {
    systemAudioCapture?: SystemAudioCaptureRenderer;
  }
}

// 识别结果接口
export interface RecognitionResult {
  text: string;
  source: AudioSourceType;
  timestamp: number;
  isFinal: boolean;
  sentenceEnd?: boolean;
}

// 通义Gummy语音识别配置
interface GummyConfig {
  apiKey: string;
  hostUrl?: string;
}

interface UseSpeechRecognitionProps {
  onResult?: (text: string) => void;
  onDualResult?: (result: RecognitionResult) => void; // 双音频模式的结果回调
  onEnd?: () => void;
  language?: string;
  config?: GummyConfig;
  listeningMode?: AudioListeningMode; // 音频监听模式
  autoSendConfig?: {
    enabled: boolean;
    roundsToSend: number;
    recognitionsPerRound: number;
  };
  onAutoSend?: (text: string) => void; // 自动发送回调
}

interface UseSpeechRecognitionReturn {
  isListening: boolean;
  isMicrophoneListening: boolean;
  isSystemAudioListening: boolean;
  text: string;
  combinedText: string; // 双音频模式的合并文本
  startListening: () => void;
  stopListening: () => void;
  // 新增：独立控制方法
  startMicrophoneOnly: () => Promise<void>;
  stopMicrophoneOnly: () => Promise<void>;
  startSystemAudioOnly: () => Promise<void>;
  stopSystemAudioOnly: () => Promise<void>;
  resetText: () => void;
  resetTextKeepAutoSendCount: () => void;
  clearAccumulatedText: () => void;
  syncExternalText: (externalText: string) => void;
  connectToService: () => Promise<boolean>;
  disconnectFromService: () => Promise<void>;
  setListeningMode: (mode: AudioListeningMode) => void;
  systemAudioPermissionGranted: boolean;
  checkSystemAudioPermission: () => Promise<boolean>;
  error: string | null;
  provider: string;
  // 自动发送相关
  autoSendEnabled: boolean;
  setAutoSendEnabled: (enabled: boolean) => void;
  recognitionCount: number;
  roundsCompleted: number;
}

// 获取语音配置的方法
const getVoiceConfig = async (): Promise<GummyConfig> => {
  try {
    if (window.electronAPI && window.electronAPI.getVoiceProviderConfig) {
      const config = await window.electronAPI.getVoiceProviderConfig('tongyi-gummy');
      return {
        apiKey: config?.apiKey || '',
        hostUrl: 'wss://dashscope.aliyuncs.com/api-ws/v1/inference'
      };
    }
  } catch (error) {
    console.warn('无法获取语音配置，使用默认配置:', error);
  }

  // 如果无法获取配置，返回空配置
  return {
    apiKey: '',
    hostUrl: 'wss://dashscope.aliyuncs.com/api-ws/v1/inference'
  };
};

export const useGummySpeechRecognition = ({
                                            onResult,
                                            onDualResult,
                                            onEnd,
                                            language = 'zh',
                                            config,
                                            listeningMode = 'microphone-only',
                                            autoSendConfig,
                                            onAutoSend
                                          }: UseSpeechRecognitionProps = {}): UseSpeechRecognitionReturn => {
  const [text, setText] = useState<string>('');
  const [combinedText, setCombinedText] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const [isMicrophoneListening, setIsMicrophoneListening] = useState<boolean>(false);
  const [isSystemAudioListening, setIsSystemAudioListening] = useState<boolean>(false);

  // 创建同步更新状态和ref的函数
  const setMicrophoneListeningState = useCallback((value: boolean) => {
    setIsMicrophoneListening(value);
    isMicrophoneListeningRef.current = value;
  }, []);

  const setSystemAudioListeningState = useCallback((value: boolean) => {
    setIsSystemAudioListening(value);
    isSystemAudioListeningRef.current = value;
  }, []);
  const [error, setError] = useState<string | null>(null);
  const [currentConfig, setCurrentConfig] = useState<GummyConfig | null>(null);
  const [currentListeningMode, setCurrentListeningMode] = useState<AudioListeningMode>(listeningMode);
  const [systemAudioPermissionGranted, setSystemAudioPermissionGranted] = useState<boolean>(false);

  // 自动发送相关状态
  const [autoSendEnabled, setAutoSendEnabled] = useState<boolean>(autoSendConfig?.enabled || false);
  const [recognitionCount, setRecognitionCount] = useState<number>(0);
  const [roundsCompleted, setRoundsCompleted] = useState<number>(0);
  const autoSendConfigRef = useRef(autoSendConfig);

  // 用于存储累积的文本
  const accumulatedTextRef = useRef('');
  const confirmedTextRef = useRef('');

  // 更新自动发送配置
  useEffect(() => {
    autoSendConfigRef.current = autoSendConfig;
    setAutoSendEnabled(autoSendConfig?.enabled || false);
  }, [autoSendConfig]);

  // 双音频识别相关状态
  const recognitionResultsRef = useRef<RecognitionResult[]>([]);
  const lastProcessedSentenceRef = useRef<{ id: number; text: string; source: AudioSourceType } | null>(null);

  // 更新合并文本（双音频模式）
  const updateCombinedText = useCallback(() => {
    const results = recognitionResultsRef.current
      .sort((a, b) => a.timestamp - b.timestamp)
      .slice(-500); // 只保留最近500条结果

    let combined = '';
    let currentSpeaker: AudioSourceType | null = null;
    let sameSpeakerRound = 0;

    results.forEach((result) => {
      if (result.text.trim()) {
        if (currentSpeaker !== result.source) {
          if (combined) combined += '\n';
          const speakerLabel = result.source === 'microphone' ? '我' : '面试官';
          combined += `${speakerLabel}：`;
          currentSpeaker = result.source;
          sameSpeakerRound = 0;
        }else {
          sameSpeakerRound++;
          if (sameSpeakerRound >= 4){
            if (combined) combined += '\n';
            const speakerLabel = result.source === 'microphone' ? '我' : '面试官';
            combined += `${speakerLabel}：`;
            currentSpeaker = result.source;
            sameSpeakerRound = 0;
          }
        }

        // 对于未完成的句子，添加视觉提示
        if (result.isFinal) {
          combined += result.text + ' ';
        } else {
          combined += result.text + '... '; // 添加省略号表示正在识别中
        }
      }
    });

    setCombinedText(combined.trim());
  }, []);

  // 检查系统音频权限
  const checkSystemAudioPermission = useCallback(async (): Promise<boolean> => {
    try {
      if (!window.electronAPI?.checkSystemAudioPermission) {
        console.warn('系统音频权限检查API不可用');
        return false;
      }

      const hasPermission = await window.electronAPI.checkSystemAudioPermission();
      setSystemAudioPermissionGranted(hasPermission);
      return hasPermission;
    } catch (error) {
      console.error('检查系统音频权限失败:', error);
      setError('检查系统音频权限失败');
      return false;
    }
  }, []);
  const sessionResultsRef = useRef<string[]>([]);
  const currentSegmentRef = useRef(''); // 存储当前语音段的识别结果

  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const isConnectedRef = useRef<boolean>(false);
  const taskStartedRef = useRef<boolean>(false); // 标记任务是否已开始
  const lastProcessedResultRef = useRef<string>(''); // 用于去重完全相同的结果

  // 使用 ref 跟踪实时状态，避免 React 状态更新的异步性问题
  const isMicrophoneListeningRef = useRef<boolean>(false);
  const isSystemAudioListeningRef = useRef<boolean>(false);

  // 确保 ref 和状态保持同步
  useEffect(() => {
    isMicrophoneListeningRef.current = isMicrophoneListening;
  }, [isMicrophoneListening]);

  useEffect(() => {
    isSystemAudioListeningRef.current = isSystemAudioListening;
  }, [isSystemAudioListening]);

  // 注意：麦克风权限检查已移至VoiceChatPanel组件中，在进入语音模式时进行
  // 这里不再需要重复检查权限，避免多次权限请求

  // 重置文本（完全重置，包括自动发送计数）
  const resetText = useCallback(() => {
    setText('');
    setCombinedText('');
    accumulatedTextRef.current = '';
    confirmedTextRef.current = '';
    sessionResultsRef.current = [];
    currentSegmentRef.current = '';
    lastProcessedResultRef.current = ''; // 重置去重标识
    recognitionResultsRef.current = [];
    lastProcessedSentenceRef.current = null;
    // 重置自动发送计数
    setRecognitionCount(0);
    setRoundsCompleted(0);
  }, []);

  // 重置文本但保留自动发送计数（用于手动发送）
  const resetTextKeepAutoSendCount = useCallback(() => {
    setText('');
    setCombinedText('');
    accumulatedTextRef.current = '';
    confirmedTextRef.current = '';
    sessionResultsRef.current = [];
    currentSegmentRef.current = '';
    lastProcessedResultRef.current = ''; // 重置去重标识
    recognitionResultsRef.current = [];
    lastProcessedSentenceRef.current = null;
    // 不重置自动发送计数，让用户可以继续累积
  }, []);

  // 清空累积文本（用于开始新的语音识别会话）
  const clearAccumulatedText = useCallback(() => {
    accumulatedTextRef.current = '';
    confirmedTextRef.current = '';
    sessionResultsRef.current = [];
    currentSegmentRef.current = '';
    lastProcessedResultRef.current = ''; // 重置去重标识
    recognitionResultsRef.current = [];
    lastProcessedSentenceRef.current = null;
    setText('');
    setCombinedText('');
    // 重置自动发送计数
    setRecognitionCount(0);
    setRoundsCompleted(0);
  }, []);

  // 同步外部文本到累积文本引用（用于手动编辑输入框后同步状态）
  const syncExternalText = useCallback((externalText: string) => {
    // console.log('同步外部文本到累积文本引用:', externalText);
    accumulatedTextRef.current = externalText;
    confirmedTextRef.current = externalText;
    currentSegmentRef.current = '';
    setText(externalText);
    if (currentListeningMode !== 'microphone-only') {
      setCombinedText(externalText);
    }
  }, [currentListeningMode]);

  // 检查并触发自动发送
  const checkAutoSend = useCallback(() => {
    const config = autoSendConfigRef.current;
    if (!config || !autoSendEnabled || !onAutoSend) {
      return;
    }

    const newRecognitionCount = recognitionCount + 1;
    setRecognitionCount(newRecognitionCount);

    console.log(`语音识别计数: ${newRecognitionCount}/${config.roundsToSend * config.recognitionsPerRound}`);

    // 检查是否达到自动发送条件
    if (newRecognitionCount >= config.roundsToSend * config.recognitionsPerRound) {
      console.log('达到自动发送条件，准备发送消息');

      // 获取当前文本
      let textToSend = '';
      if (currentListeningMode === 'microphone-only') {
        textToSend = accumulatedTextRef.current;
      } else {
        // 对于双音频模式，需要获取最新的合并文本
        textToSend = combinedText;
      }

      if (textToSend.trim()) {
        console.log('自动发送消息:', textToSend);
        onAutoSend(textToSend.trim());

        // 重置计数和文本状态
        setRecognitionCount(0);
        setRoundsCompleted(0);

        // 重置文本状态
        accumulatedTextRef.current = '';
        confirmedTextRef.current = '';
        currentSegmentRef.current = '';
        setText('');
        setCombinedText('');
        recognitionResultsRef.current = [];
      }
    } else {
      // 更新轮次完成情况（用于显示）
      const newRoundsCompleted = Math.floor(newRecognitionCount / config.recognitionsPerRound);
      setRoundsCompleted(newRoundsCompleted);
    }
  }, [autoSendEnabled, recognitionCount, roundsCompleted, onAutoSend, currentListeningMode, combinedText]);

  // 停止语音识别
  const stopListening = useCallback(async () => {
    console.log('停止通义Gummy语音识别...');

    // 结束当前任务（但保持连接）
    try {
      if (currentListeningMode === 'dual-audio') {
        // 双音频模式需要停止两个任务
        await window.electronAPI.gummyFinishTask('microphone');
        await window.electronAPI.gummyFinishTask('system');
      } else if (currentListeningMode === 'microphone-only') {
        await window.electronAPI.gummyFinishTask('microphone');
      } else if (currentListeningMode === 'system-only') {
        await window.electronAPI.gummyFinishTask('system');
      }
    } catch (error) {
      console.error('结束通义Gummy任务失败:', error);
    }

    // 停止麦克风音频处理
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }

    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // 停止系统音频捕获
    if (currentListeningMode === 'system-only' || currentListeningMode === 'dual-audio') {
      try {
        // 清除系统音频数据回调
        if (window.systemAudioCapture) {
          window.systemAudioCapture.setAudioDataCallback(null);
        }

        if (window.electronAPI?.stopSystemAudioCapture) {
          await window.electronAPI.stopSystemAudioCapture();
        }
      } catch (error) {
        console.error('停止系统音频捕获失败:', error);
      }
    }

    isConnectedRef.current = false;
    taskStartedRef.current = false;
    setIsListening(false);
    setMicrophoneListeningState(false);
    setSystemAudioListeningState(false);

    if (onEnd) {
      onEnd();
    }
  }, [onEnd, currentListeningMode]);

  // 建立连接（进入语音聊天模式时调用）
  const connectToService = useCallback(async () => {
    try {
      console.log('建立通义Gummy WebSocket连接...');

      // 获取最新配置
      const voiceConfig = config || await getVoiceConfig();
      setCurrentConfig(voiceConfig);

      // 检查配置
      if (!voiceConfig.apiKey) {
        setError('通义Gummy API配置不完整，请在设置中配置API密钥');
        return false;
      }

      // 根据监听模式建立相应的连接
      if (currentListeningMode === 'microphone-only' || currentListeningMode === 'dual-audio') {
        // 创建麦克风连接
        if (window.electronAPI.getOrCreateGummyHandler) {
          const micResult = await window.electronAPI.getOrCreateGummyHandler('microphone', voiceConfig, language);
          if (!micResult.success) {
            setError(micResult.error || '建立麦克风语音识别连接失败');
            return false;
          }
        } else {
          // 如果新API不可用，使用旧API
          const micResult = await window.electronAPI.gummyConnect(voiceConfig, language);
          if (!micResult.success) {
            setError(micResult.error || '建立麦克风语音识别连接失败');
            return false;
          }
        }
      }

      if (currentListeningMode === 'system-only' || currentListeningMode === 'dual-audio') {
        // 创建系统音频连接
        if (window.electronAPI.getOrCreateGummyHandler) {
          const sysResult = await window.electronAPI.getOrCreateGummyHandler('system', voiceConfig, language);
          if (!sysResult.success) {
            setError(sysResult.error || '建立系统音频语音识别连接失败');
            return false;
          }
        } else {
          console.warn('系统音频连接创建失败，新的双音频API不可用');
        }
      }

      console.log('通义Gummy WebSocket连接已建立');
      return true;
    } catch (error) {
      console.error('建立通义Gummy连接失败:', error);
      setError('建立语音识别连接失败');
      return false;
    }
  }, [config, language, currentListeningMode]);

  // 断开连接（退出语音聊天模式时调用）
  const disconnectFromService = useCallback(async () => {
    try {
      const result = await window.electronAPI.gummyDisconnect();
      // 主进程会处理日志输出，这里不需要重复输出
    } catch (error) {
      console.error('断开通义Gummy连接失败:', error);
    }
  }, []);

  // 检查连接状态并重连的辅助函数
  const checkAndReconnect = useCallback(async (sourceIdentifier: string): Promise<boolean> => {
    try {
      if (window.electronAPI.gummyCheckAndReconnect) {
        console.log(`检查连接状态 [${sourceIdentifier}]...`);
        const result = await window.electronAPI.gummyCheckAndReconnect(sourceIdentifier);
        if (!result.success) {
          console.error(`连接检查/重连失败 [${sourceIdentifier}]:`, result.error);
          setError(`语音识别连接失败: ${result.error}`);
          return false;
        }
        if (result.connected) {
          console.log(`连接状态正常 [${sourceIdentifier}]`);
        }
        return true;
      }
      return true; // 如果API不可用，假设连接正常
    } catch (error) {
      console.error(`连接检查/重连异常 [${sourceIdentifier}]:`, error);
      setError(`语音识别连接异常: ${error}`);
      return false;
    }
  }, []);

  // 开始语音识别
  const startListening = useCallback(async () => {
    try {
      console.log(`开始通义Gummy语音识别 [${currentListeningMode}]...`);
      setError(null);

      if (isListening) {
        return;
      }

      // 不在每次开始时重置累积文本，保持之前识别的内容
      // 只重置会话相关的状态
      sessionResultsRef.current = [];
      currentSegmentRef.current = '';
      taskStartedRef.current = false;

      // 根据监听模式启动相应的音频捕获
      switch (currentListeningMode) {
        case 'microphone-only': {
          // 检查连接状态并重连
          const micConnected = await checkAndReconnect('microphone');
          if (!micConnected) {
            setError('麦克风语音识别连接失败，请重试');
            return;
          }

          await startMicrophoneCapture();
          setMicrophoneListeningState(true);
          // 启动麦克风识别任务
          const micResult = await window.electronAPI.gummyStartTask('microphone');
          if (!micResult.success) {
            setError(micResult.error || '启动麦克风语音识别任务失败');
            await stopListening();
            return;
          }
          break;
        }
        case 'system-only': {
          // 检查连接状态并重连
          const sysConnected = await checkAndReconnect('system');
          if (!sysConnected) {
            setError('系统音频语音识别连接失败，请重试');
            return;
          }

          await startSystemAudioCapture();
          setSystemAudioListeningState(true);
          // 启动系统音频识别任务
          const sysResult = await window.electronAPI.gummyStartTask('system');
          if (!sysResult.success) {
            setError(sysResult.error || '启动系统音频语音识别任务失败');
            await stopListening();
            return;
          }
          break;
        }
        case 'dual-audio': {
          // 并行执行所有初始化操作
          const [micConnected, sysConnected] = await Promise.all([
            checkAndReconnect('microphone'),
            checkAndReconnect('system'),
          ]);

          if (!micConnected || !sysConnected) {
            setError('双音频语音识别连接失败，请重试');
            return;
          }

          await Promise.all([
            startMicrophoneCapture(),
            startSystemAudioCapture()
          ]);
          setMicrophoneListeningState(true);
          setSystemAudioListeningState(true);
          // 启动双音频识别任务
          const micDualResult = await window.electronAPI.gummyStartTask('microphone');
          const sysDualResult = await window.electronAPI.gummyStartTask('system');
          if (!micDualResult.success || !sysDualResult.success) {
            setError('启动双音频语音识别任务失败');
            await stopListening();
            return;
          }
          break;
        }
      }

      setIsListening(true);

    } catch (error) {
      console.error('启动通义Gummy语音识别失败:', error);
      setError('启动语音识别失败');
      setIsListening(false);
      setMicrophoneListeningState(false);
      setSystemAudioListeningState(false);
    }
  }, [isListening, currentListeningMode, checkAndReconnect]);

  // 开始麦克风音频捕获
  const startMicrophoneCapture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      streamRef.current = stream;
      audioContextRef.current = new AudioContext({ sampleRate: 16000 });
      const source = audioContextRef.current.createMediaStreamSource(stream);

      // 创建音频处理器
      processorRef.current = audioContextRef.current.createScriptProcessor(4096, 1, 1);

      processorRef.current.onaudioprocess = async (event) => {
        // 只有在WebSocket连接且任务已开始时才发送音频数据
        if (isConnectedRef.current && taskStartedRef.current) {
          const inputBuffer = event.inputBuffer.getChannelData(0);

          // 转换为16位PCM
          const pcmData = new Int16Array(inputBuffer.length);
          for (let i = 0; i < inputBuffer.length; i++) {
            pcmData[i] = Math.max(-32768, Math.min(32767, inputBuffer[i] * 32768));
          }

          // 发送麦克风音频数据到主进程，明确标识音频源
          try {
            await window.electronAPI.gummySendAudio(pcmData.buffer, 'microphone');
          } catch (error) {
            console.error('发送麦克风音频数据失败:', error);
          }
        }
      };

      source.connect(processorRef.current);
      processorRef.current.connect(audioContextRef.current.destination);

      isConnectedRef.current = true;

    } catch (error) {
      console.error('启动麦克风音频捕获失败:', error);
      setError('启动麦克风音频捕获失败');
      throw error;
    }
  };

  // 系统音频数据处理回调
  const handleSystemAudioData = useCallback(async (audioData: ArrayBuffer) => {
    try {
      if (window.electronAPI?.gummySendAudio && taskStartedRef.current) {
        const result = await window.electronAPI.gummySendAudio(audioData, 'system');
        if (!result.success) {
          console.warn(`系统音频数据发送失败: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('处理系统音频数据失败:', error);
    }
  }, []);

  // 开始系统音频捕获（优化版本）
  const startSystemAudioCapture = async () => {
    try {
      // 快速检查权限（使用缓存的权限状态）
      const hasPermission = systemAudioPermissionGranted || await checkSystemAudioPermission();

      if (!hasPermission) {
        setError('系统音频权限未授予，无法开始监听');
        throw new Error('系统音频权限未授予');
      }

      // 并行执行：设置回调和启动捕获
      const setupTasks = [
        // 设置系统音频数据回调
        new Promise<void>((resolve) => {
          if (window.systemAudioCapture) {
            window.systemAudioCapture.setAudioDataCallback(handleSystemAudioData);
          }
          resolve();
        }),
        // 启动系统音频捕获
        window.electronAPI?.startSystemAudioCapture?.({
          sampleRate: 16000,
          channels: 1,
          bufferSize: 4096
        }) || Promise.resolve({ success: false, error: 'API不可用' })
      ];

      const [, result] = await Promise.all(setupTasks);

      if (!result.success) {
        setError('启动系统音频捕获失败: ' + (result.error || '未知错误'));
        throw new Error('启动系统音频捕获失败');
      }

      console.log('系统音频捕获启动成功');
    } catch (error) {
      console.error('启动系统音频捕获失败:', error);
      setError('启动系统音频捕获失败');
      throw error;
    }
  };



  // 设置事件监听
  useEffect(() => {
    const unsubscribeResult = window.electronAPI.onGummyResult((transcription: any) => {
      // console.log('收到通义Gummy识别结果:', transcription);

      if (transcription && transcription.text && transcription.text.trim()) {
        const currentResult = transcription.text.trim();
        const sentenceEnd = transcription.sentence_end;

        // 创建结果的唯一标识符，用于去重完全相同的结果
        const resultKey = `${transcription.sentence_id}_${transcription.begin_time}_${transcription.end_time}_${currentResult}_${sentenceEnd}`;

        // 如果是完全相同的结果，跳过处理
        if (resultKey === lastProcessedResultRef.current) {
          console.log('跳过重复的识别结果:', currentResult);
          return;
        }
        lastProcessedResultRef.current = resultKey;

        // 确定音频源类型（根据识别结果的来源标识）
        let audioSource: AudioSourceType = 'microphone';

        // 从识别结果中获取音频源标识
        const sourceIdentifier = transcription.source_identifier || 'microphone';

        if (currentListeningMode === 'system-only') {
          audioSource = 'system';
        } else if (currentListeningMode === 'dual-audio') {
          // 在双音频模式下，根据源标识来区分
          audioSource = sourceIdentifier === 'system' ? 'system' : 'microphone';
        }

        if (sentenceEnd) {
          // 句子结束，处理最终结果
          console.log(`句子结束 [${audioSource}]，确认文本:`, currentResult);

          if (currentListeningMode === 'microphone-only') {
            // 仅麦克风模式，使用原有逻辑
            const finalText = accumulatedTextRef.current
                ? accumulatedTextRef.current + ' ' + currentResult
                : currentResult;

            accumulatedTextRef.current = finalText;
            currentSegmentRef.current = '';
            setText(finalText);

            // 只有在句子结束时才触发onResult回调
            if (onResult) {
              onResult(finalText);
            }

            // 检查自动发送条件
            checkAutoSend();
          } else {
            // 系统音频或双音频模式，添加到结果列表
            // 先移除同源的临时结果
            recognitionResultsRef.current = recognitionResultsRef.current.filter(
              r => !(r.source === audioSource && !r.isFinal)
            );

            const result: RecognitionResult = {
              text: currentResult,
              source: audioSource,
              timestamp: Date.now(),
              isFinal: true,
              sentenceEnd: true
            };

            recognitionResultsRef.current.push(result);
            updateCombinedText();

            // 触发双音频结果回调
            if (onDualResult) {
              onDualResult(result);
            }

            // 检查自动发送条件
            checkAutoSend();
          }
        } else {
          // 句子未结束，实时显示识别过程
          console.log(`句子未结束 [${audioSource}]，更新当前段:`, currentResult);

          if (currentListeningMode === 'microphone-only') {
            // 仅麦克风模式，使用原有实时显示逻辑
            currentSegmentRef.current = currentResult;

            // 显示文本 = 已确认的累积文本 + 当前正在识别的语音段
            const displayText = accumulatedTextRef.current
                ? accumulatedTextRef.current + ' ' + currentResult
                : currentResult;

            setText(displayText);

            // 仅麦克风模式也触发onResult回调进行实时更新
            if (onResult) {
              onResult(displayText);
            }
          } else {
            // 系统音频或双音频模式，也实时显示识别过程
            // 创建临时结果用于实时显示
            const tempResult: RecognitionResult = {
              text: currentResult,
              source: audioSource,
              timestamp: Date.now(),
              isFinal: false,
              sentenceEnd: false
            };

            // 更新临时结果到结果列表（替换同源的未完成结果）
            const existingIndex = recognitionResultsRef.current.findIndex(
              r => r.source === audioSource && !r.isFinal
            );

            if (existingIndex >= 0) {
              // 替换现有的临时结果
              recognitionResultsRef.current[existingIndex] = tempResult;
            } else {
              // 添加新的临时结果
              recognitionResultsRef.current.push(tempResult);
            }

            updateCombinedText();

            // 触发双音频结果回调（实时更新）
            if (onDualResult) {
              onDualResult(tempResult);
            }
          }
        }
      }
    });

    const unsubscribeError = window.electronAPI.onGummyError((errorData: any) => {
      const error = typeof errorData === 'string' ? errorData : errorData.error;
      const sourceIdentifier = typeof errorData === 'object' ? errorData.sourceIdentifier : undefined;

      console.error(`通义Gummy识别错误 [${sourceIdentifier || 'unknown'}]:`, error);
      setError(`语音识别错误: ${error}。下次开始识别时将自动重连。`);

      // 根据音频源标识来处理错误
      if (sourceIdentifier === 'microphone' || !sourceIdentifier) {
        setMicrophoneListeningState(false);
      }
      if (sourceIdentifier === 'system' || !sourceIdentifier) {
        setSystemAudioListeningState(false);
      }

      // 只有当所有音频源都停止时才设置总体监听状态为false
      if (sourceIdentifier === 'microphone' && !isSystemAudioListening) {
        setIsListening(false);
        isConnectedRef.current = false;
      } else if (sourceIdentifier === 'system' && !isMicrophoneListening) {
        setIsListening(false);
        isConnectedRef.current = false;
      } else if (!sourceIdentifier) {
        // 兼容旧版本，没有sourceIdentifier时全部停止
        setIsListening(false);
        setMicrophoneListeningState(false);
        setSystemAudioListeningState(false);
        isConnectedRef.current = false;
      }
    });

    const unsubscribeEnd = window.electronAPI.onGummyEnd((endData: any) => {
      console.log('前端接收到 gummy-end 事件，原始数据:', endData);
      const sourceIdentifier = endData?.sourceIdentifier;
      console.log(`通义Gummy识别结束 [${sourceIdentifier || 'unknown'}]`);

      // 识别结束时，如果还有未确认的当前语音段，将其确认到累积文本中
      if (currentSegmentRef.current) {
        console.log('识别结束，确认剩余文本:', currentSegmentRef.current);
        const finalText = accumulatedTextRef.current
            ? accumulatedTextRef.current + ' ' + currentSegmentRef.current
            : currentSegmentRef.current;

        accumulatedTextRef.current = finalText;
        currentSegmentRef.current = '';
        setText(finalText);

        if (onResult) {
          onResult(finalText);
        }
      }

      // 根据音频源标识来处理连接结束
      // 使用 ref 获取实时状态，避免 React 状态更新的异步性问题
      const currentMicState = isMicrophoneListeningRef.current;
      const currentSystemState = isSystemAudioListeningRef.current;

      console.log(`处理连接结束事件 [${sourceIdentifier}], 实时状态: 麦克风=${currentMicState}, 系统音频=${currentSystemState}`);

      let shouldStopOverallListening = false;

      if (sourceIdentifier === 'microphone' || !sourceIdentifier) {
        setMicrophoneListeningState(false);
        console.log(`麦克风监听已停止 [${sourceIdentifier || 'unknown'}]`);
        // 检查系统音频实时状态，如果系统音频也没在监听，则停止总体监听
        if (!currentSystemState) {
          shouldStopOverallListening = true;
          console.log(`系统音频也未在监听，将停止总体监听`);
        } else {
          console.log(`系统音频仍在监听，保持总体监听状态`);
        }
      }

      if (sourceIdentifier === 'system' || !sourceIdentifier) {
        setSystemAudioListeningState(false);
        console.log(`系统音频监听已停止 [${sourceIdentifier || 'unknown'}]`);
        // 检查麦克风实时状态，如果麦克风也没在监听，则停止总体监听
        if (!currentMicState) {
          shouldStopOverallListening = true;
          console.log(`麦克风也未在监听，将停止总体监听`);
        } else {
          console.log(`麦克风仍在监听，保持总体监听状态`);
        }
      }

      // 处理总体监听状态
      if (!sourceIdentifier) {
        // 兼容旧版本，没有sourceIdentifier时全部停止
        console.log(`无音频源标识，全部停止`);
        setIsListening(false);
        setMicrophoneListeningState(false);
        setSystemAudioListeningState(false);
        isConnectedRef.current = false;
        taskStartedRef.current = false;
        if (onEnd) {
          onEnd();
        }
      } else if (shouldStopOverallListening) {
        console.log(`停止总体监听状态 [停止的源: ${sourceIdentifier}]`);
        setIsListening(false);
        isConnectedRef.current = false;
        taskStartedRef.current = false;
        if (onEnd) {
          onEnd();
        }
      } else {
        console.log(`其他音频源仍在运行，保持总体监听状态 [停止的源: ${sourceIdentifier}]`);
      }
    });

    // 监听任务开始事件
    const unsubscribeTaskStarted = window.electronAPI.onGummyTaskStarted?.((taskData: any) => {
      const sourceIdentifier = taskData?.sourceIdentifier;
      console.log(`通义Gummy任务已开始，可以发送音频数据 [${sourceIdentifier || 'unknown'}]`);
      taskStartedRef.current = true;
    }) || (() => {});

    return () => {
      unsubscribeResult();
      unsubscribeError();
      unsubscribeEnd();
      if (typeof unsubscribeTaskStarted === 'function') {
        unsubscribeTaskStarted();
      }
    };
  }, [onResult, onEnd]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 直接清理资源，避免依赖stopListening函数
      if (processorRef.current) {
        processorRef.current.disconnect();
        processorRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }

      // 停止通义Gummy连接
      window.electronAPI.gummyStopRecognition().catch(console.error);
    };
  }, []); // 空依赖数组，只在组件卸载时执行

  // 设置监听模式
  const setListeningMode = useCallback((mode: AudioListeningMode) => {
    setCurrentListeningMode(mode);
  }, []);

  // 监听模式变化时检查系统音频权限
  useEffect(() => {
    if (currentListeningMode === 'system-only' || currentListeningMode === 'dual-audio') {
      checkSystemAudioPermission();
    }
  }, [currentListeningMode, checkSystemAudioPermission]);

  // 独立控制麦克风
  const startMicrophoneOnly = useCallback(async () => {
    if (currentListeningMode !== 'dual-audio') {
      console.warn('独立麦克风控制仅在双音频模式下可用');
      return;
    }

    if (isMicrophoneListening) {
      console.log('麦克风已在监听中');
      return;
    }

    try {
      console.log('独立启动麦克风监听...');

      // 检查连接状态并重连
      const micConnected = await checkAndReconnect('microphone');
      if (!micConnected) {
        setError('麦克风语音识别连接失败，请重试');
        return;
      }

      await startMicrophoneCapture();
      setMicrophoneListeningState(true);

      // 启动麦克风识别任务
      const micResult = await window.electronAPI.gummyStartTask('microphone');
      if (!micResult.success) {
        setError(micResult.error || '启动麦克风语音识别任务失败');
        await stopMicrophoneOnly();
        return;
      }

      // 更新总体监听状态
      setIsListening(true);

      console.log('麦克风监听启动成功');
    } catch (error) {
      console.error('启动麦克风监听失败:', error);
      setError('启动麦克风监听失败');
      setMicrophoneListeningState(false);
    }
  }, [currentListeningMode, isMicrophoneListening, isListening, isSystemAudioListening, checkAndReconnect]);

  const stopMicrophoneOnly = useCallback(async () => {
    if (currentListeningMode !== 'dual-audio') {
      console.warn('独立麦克风控制仅在双音频模式下可用');
      return;
    }

    if (!isMicrophoneListening) {
      console.log('麦克风未在监听中');
      return;
    }

    try {
      console.log('独立停止麦克风监听...');

      // 结束麦克风任务
      await window.electronAPI.gummyFinishTask('microphone');

      // 停止麦克风音频处理
      if (processorRef.current) {
        processorRef.current.disconnect();
        processorRef.current = null;
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }

      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }

      setMicrophoneListeningState(false);

      // 更新总体监听状态 - 如果两个音频源都停止了，则停止总体监听
      if (!isSystemAudioListeningRef.current) {
        setIsListening(false);
      }

      console.log('麦克风监听停止成功');
    } catch (error) {
      console.error('停止麦克风监听失败:', error);
      setError('停止麦克风监听失败');
    }
  }, [currentListeningMode, isMicrophoneListening, isListening, isSystemAudioListening]);

  // 独立控制系统音频
  const startSystemAudioOnly = useCallback(async () => {
    if (currentListeningMode !== 'dual-audio') {
      console.warn('独立系统音频控制仅在双音频模式下可用');
      return;
    }

    if (isSystemAudioListening) {
      console.log('系统音频已在监听中');
      return;
    }

    try {
      console.log('独立启动系统音频监听...');

      // 检查连接状态并重连
      const sysConnected = await checkAndReconnect('system');
      if (!sysConnected) {
        setError('系统音频语音识别连接失败，请重试');
        return;
      }

      await startSystemAudioCapture();
      setSystemAudioListeningState(true);

      // 启动系统音频识别任务
      const sysResult = await window.electronAPI.gummyStartTask('system');
      if (!sysResult.success) {
        setError(sysResult.error || '启动系统音频语音识别任务失败');
        await stopSystemAudioOnly();
        return;
      }

      // 更新总体监听状态
      setIsListening(true);

      console.log('系统音频监听启动成功');
    } catch (error) {
      console.error('启动系统音频监听失败:', error);
      setError('启动系统音频监听失败');
      setSystemAudioListeningState(false);
    }
  }, [currentListeningMode, isSystemAudioListening, isListening, isMicrophoneListening, checkAndReconnect]);

  const stopSystemAudioOnly = useCallback(async () => {
    if (currentListeningMode !== 'dual-audio') {
      console.warn('独立系统音频控制仅在双音频模式下可用');
      return;
    }

    if (!isSystemAudioListening) {
      console.log('系统音频未在监听中');
      return;
    }

    try {
      console.log('独立停止系统音频监听...');

      // 结束系统音频任务
      await window.electronAPI.gummyFinishTask('system');

      // 停止系统音频捕获
      if (window.systemAudioCapture) {
        window.systemAudioCapture.setAudioDataCallback(null);
      }

      if (window.electronAPI?.stopSystemAudioCapture) {
        await window.electronAPI.stopSystemAudioCapture();
      }

      setSystemAudioListeningState(false);

      // 更新总体监听状态 - 如果两个音频源都停止了，则停止总体监听
      if (!isMicrophoneListeningRef.current) {
        setIsListening(false);
      }

      console.log('系统音频监听停止成功');
    } catch (error) {
      console.error('停止系统音频监听失败:', error);
      setError('停止系统音频监听失败');
    }
  }, [currentListeningMode, isSystemAudioListening, isListening, isMicrophoneListening]);

  return {
    isListening,
    isMicrophoneListening,
    isSystemAudioListening,
    text,
    combinedText,
    startListening,
    stopListening,
    startMicrophoneOnly,
    stopMicrophoneOnly,
    startSystemAudioOnly,
    stopSystemAudioOnly,
    resetText,
    resetTextKeepAutoSendCount,
    clearAccumulatedText,
    syncExternalText,
    connectToService,
    disconnectFromService,
    setListeningMode,
    systemAudioPermissionGranted,
    checkSystemAudioPermission,
    error,
    provider: '通义Gummy语音识别',
    // 自动发送相关
    autoSendEnabled,
    setAutoSendEnabled,
    recognitionCount,
    roundsCompleted
  };
};
