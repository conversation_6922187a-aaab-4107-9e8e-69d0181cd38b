@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme variables */
:root {
  /* Default dark theme - 带透明效果的深色主题 */
  --bg-primary: rgba(0, 0, 0, 0.85);
  --bg-secondary: rgba(26, 26, 26, 0.8);
  --bg-tertiary: rgba(51, 51, 51, 0.75);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --border-primary: rgba(255, 255, 255, 0.2);
  --border-secondary: rgba(255, 255, 255, 0.1);
  --accent-primary: #3b82f6;
  --accent-secondary: #1d4ed8;
}

[data-theme="light"] {
  /* Light theme - 带透明效果的浅色主题 */
  --bg-primary: rgba(255, 255, 255, 0.85);
  --bg-secondary: rgba(245, 245, 245, 0.8);
  --bg-tertiary: rgba(224, 224, 224, 0.75);
  --text-primary: #000000;
  --text-secondary: rgba(0, 0, 0, 0.8);
  --text-tertiary: rgba(0, 0, 0, 0.6);
  --border-primary: rgba(0, 0, 0, 0.2);
  --border-secondary: rgba(0, 0, 0, 0.1);
  --accent-primary: #3b82f6;
  --accent-secondary: #1d4ed8;
}

/* Custom animations for UI elements */
@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, -48%); }
  to { opacity: 0.98; transform: translate(-50%, -50%); }
}

/* Dialog transition improvements */
.settings-dialog {
  transform-origin: center;
  backface-visibility: hidden;
  will-change: opacity, transform;
}

/* Custom styling for dropdowns */
select {
  appearance: menulist;
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

option {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  padding: 8px !important;
}

.frosted-glass {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(8px);
}

.auth-button {
  background: rgba(252, 252, 252, 0.98);
  color: rgba(60, 60, 60, 0.9);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.auth-button:hover {
  background: rgba(255, 255, 255, 1);
}

.auth-button::before {
  content: "";
  position: absolute;
  inset: -8px;
  background: linear-gradient(45deg, #ff000000, #0000ff00);
  z-index: -1;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: inherit;
  filter: blur(24px);
  opacity: 0;
}

.auth-button:hover::before {
  background: linear-gradient(
    45deg,
    rgba(255, 0, 0, 0.4),
    rgba(0, 0, 255, 0.4)
  );
  filter: blur(48px);
  inset: -16px;
  opacity: 1;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Theme utility classes */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-bg-tertiary {
  background-color: var(--bg-tertiary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-tertiary {
  color: var(--text-tertiary);
}

.theme-border-primary {
  border-color: var(--border-primary);
}

.theme-border-secondary {
  border-color: var(--border-secondary);
}

/* Update existing styles to use theme variables */
select {
  appearance: menulist;
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

option {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  padding: 8px !important;
}

.frosted-glass {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(8px);
}

[data-theme="light"] .frosted-glass {
  background: rgba(245, 245, 245, 0.95);
}

/* Theme utility classes */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-bg-tertiary {
  background-color: var(--bg-tertiary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-tertiary {
  color: var(--text-tertiary);
}

.theme-border-primary {
  border-color: var(--border-primary);
}

.theme-border-secondary {
  border-color: var(--border-secondary);
}

/* Theme switch specific styles */
.theme-switch-container:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-primary), 0 0 0 4px var(--bg-primary);
}

/* Theme textarea styles */
.theme-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--border-primary);
}
