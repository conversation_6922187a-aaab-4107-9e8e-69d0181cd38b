// 模型验证工具函数

export interface ModelEndpoint {
  name: string
  context_length: number
  pricing: {
    prompt: string
    completion: string
    request: string
    image: string
    web_search: string
    internal_reasoning: string
    discount: number
  }
  provider_name: string
  tag: string
  quantization: string | null
  max_completion_tokens: number
  max_prompt_tokens: number | null
  supported_parameters: string[]
  status: number
  uptime_last_30m: number | null
}

export interface ModelInfo {
  id: string
  name: string
  created: number
  description: string
  architecture: {
    tokenizer: string
    instruct_type: string
    modality: string
    input_modalities: string[]
    output_modalities: string[]
  }
}

export interface ModelValidationResult {
  isValid: boolean
  supportsImages: boolean
  error?: string
  endpoints?: ModelEndpoint[]
  modelInfo?: ModelInfo
}

/**
 * 验证OpenRouter模型是否可用
 * @param modelId 模型ID
 * @param apiKey OpenRouter API密钥
 * @returns 验证结果
 */
export async function validateOpenRouterModel(
  modelId: string,
  apiKey: string
): Promise<ModelValidationResult> {
  try {
    // 使用electron的IPC API进行验证
    const result = await window.electronAPI.validateModel(modelId, apiKey, false);
    return result;
  } catch (error) {
    console.error('模型验证失败:', error)
    return {
      isValid: false,
      supportsImages: false,
      error: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

/**
 * 验证模型是否适用于特定用途
 * @param modelId 模型ID
 * @param apiKey API密钥
 * @param requiresImages 是否需要图像支持
 * @param apiProvider API提供商
 * @returns 验证结果
 */
export async function validateModelForPurpose(
  modelId: string,
  apiKey: string,
  requiresImages: boolean = false,
  apiProvider: "openrouter" | "zhipu" | "custom" = "openrouter"
): Promise<ModelValidationResult> {
  try {
    // 根据API提供商进行不同的验证
    if (apiProvider === "openrouter") {
      // OpenRouter使用现有的验证逻辑
      const result = await window.electronAPI.validateModel(modelId, apiKey, requiresImages);
      return result;
    } else if (apiProvider === "zhipu") {
      // 智谱AI的验证逻辑
      return await validateZhipuModel(modelId, apiKey, requiresImages);
    } else if (apiProvider === "custom") {
      // 自定义API的验证逻辑
      return await validateCustomModel(modelId, apiKey, requiresImages);
    } else {
      return {
        isValid: false,
        supportsImages: false,
        error: '不支持的API提供商'
      };
    }
  } catch (error) {
    console.error('模型验证失败:', error)
    return {
      isValid: false,
      supportsImages: false,
      error: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}

/**
 * 验证智谱AI模型
 * @param modelId 模型ID
 * @param apiKey API密钥
 * @param requiresImages 是否需要图像支持
 * @returns 验证结果
 */
async function validateZhipuModel(
  modelId: string,
  apiKey: string,
  requiresImages: boolean
): Promise<ModelValidationResult> {
  try {
    // 检查多模态支持
    const supportsImages = modelId.includes('4v') || modelId.includes('vision');

    // 如果需要图像支持但模型不支持，返回错误
    if (requiresImages && !supportsImages) {
      return {
        isValid: false,
        supportsImages: false,
        error: '该模型不支持图像分析，请选择GLM-4V系列模型'
      };
    }

    // 基本验证：检查API密钥格式
    const isValidApiKey = /^[a-f0-9]{32}\.[a-zA-Z0-9]{16}$/.test(apiKey.trim());
    if (!isValidApiKey) {
      return {
        isValid: false,
        supportsImages,
        error: 'API密钥格式不正确，请检查智谱AI API密钥'
      };
    }

    return {
      isValid: true,
      supportsImages,
      modelInfo: {
        id: modelId,
        name: modelId,
        created: Date.now(),
        description: `智谱AI ${modelId} 模型`,
        architecture: {
          tokenizer: 'zhipu',
          instruct_type: 'chat',
          modality: supportsImages ? 'multimodal' : 'text',
          input_modalities: supportsImages ? ['text', 'image'] : ['text'],
          output_modalities: ['text']
        }
      }
    };
  } catch (error) {
    return {
      isValid: false,
      supportsImages: false,
      error: error instanceof Error ? error.message : '智谱AI模型验证失败'
    };
  }
}

/**
 * 验证自定义API模型
 * @param modelId 模型ID
 * @param apiKey API密钥
 * @param requiresImages 是否需要图像支持
 * @returns 验证结果
 */
async function validateCustomModel(
  modelId: string,
  apiKey: string,
  requiresImages: boolean
): Promise<ModelValidationResult> {
  try {
    // 对于自定义API，我们进行基本的格式检查
    if (!modelId || modelId.trim().length === 0) {
      return {
        isValid: false,
        supportsImages: false,
        error: '请输入有效的模型ID'
      };
    }

    if (!apiKey || apiKey.trim().length === 0) {
      return {
        isValid: false,
        supportsImages: false,
        error: '请输入有效的API密钥'
      };
    }

    // 检查是否可能支持图像（基于模型名称推测）
    const possiblySupportsImages = true;

    return {
      isValid: true,
      supportsImages: possiblySupportsImages,
      modelInfo: {
        id: modelId,
        name: modelId,
        created: Date.now(),
        description: `自定义API ${modelId} 模型`,
        architecture: {
          tokenizer: 'custom',
          instruct_type: 'chat',
          modality: possiblySupportsImages ? 'multimodal' : 'text',
          input_modalities: possiblySupportsImages ? ['text', 'image'] : ['text'],
          output_modalities: ['text']
        }
      }
    };
  } catch (error) {
    return {
      isValid: false,
      supportsImages: false,
      error: error instanceof Error ? error.message : '自定义模型验证失败'
    };
  }
}

/**
 * 批量验证多个模型
 * @param models 模型配置
 * @param apiKey API密钥
 * @param apiProvider API提供商
 * @returns 验证结果映射
 */
export async function validateModels(
  models: { [key: string]: { modelId: string; requiresImages: boolean } },
  apiKey: string,
  apiProvider: "openrouter" | "zhipu" | "custom" = "openrouter"
): Promise<{ [key: string]: ModelValidationResult }> {
  const results: { [key: string]: ModelValidationResult } = {}

  // 并行验证所有模型
  const validationPromises = Object.entries(models).map(async ([key, config]) => {
    const result = await validateModelForPurpose(
      config.modelId,
      apiKey,
      config.requiresImages,
      apiProvider
    )
    return { key, result }
  })

  const validationResults = await Promise.all(validationPromises)

  validationResults.forEach(({ key, result }) => {
    results[key] = result
  })

  return results
}

/**
 * 获取模型的详细信息
 * @param modelId 模型ID
 * @param apiKey API密钥
 * @returns 模型详细信息
 */
export async function getModelDetails(
  modelId: string,
  apiKey: string
): Promise<{
  success: boolean
  model?: any
  error?: string
}> {
  try {
    const response = await fetch(`https://openrouter.ai/api/v1/models/${modelId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://github.com'
      }
    })

    if (!response.ok) {
      return {
        success: false,
        error: `获取模型信息失败: ${response.status} ${response.statusText}`
      }
    }

    const model = await response.json()
    return {
      success: true,
      model
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络请求失败'
    }
  }
}
