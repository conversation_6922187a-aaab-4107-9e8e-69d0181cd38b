<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 激活码管理系统</title>
    
    <!-- 使用本地安装的Bootstrap -->
    <link rel="stylesheet" href="node_modules/bootstrap/dist/css/bootstrap.min.css">
    
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
        }
        
        .form-signin {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            margin: auto;
        }
        
        .form-signin .card {
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .form-signin .card-header {
            background: linear-gradient(to right, #0d6efd, #0dcaf0);
            color: white;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            text-align: center;
            padding: 1.5rem;
        }
        
        .form-signin .card-body {
            padding: 2rem;
        }
        
        .form-signin .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-signin .btn-primary {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(to right, #0d6efd, #0dcaf0);
            border: none;
        }
        
        .error-message {
            color: #dc3545;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <main class="form-signin">
        <div class="card">
            <div class="card-header">
                <h1 class="h3 mb-0">激活码管理系统</h1>
                <p class="mb-0">管理员登录</p>
            </div>
            <div class="card-body">
                <form action="/admin/login" method="post" id="loginForm">
                    <div class="form-floating">
                        <input type="password" class="form-control" id="apiKey" name="apiKey" placeholder="请输入管理员API密钥" required>
                        <label for="apiKey">管理员API密钥</label>
                    </div>
                    
                    <button class="btn btn-primary" type="submit">登录</button>
                    
                    <div class="error-message" id="errorMessage">
                        密钥错误，请重试
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查URL参数是否有错误信息
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('error')) {
                document.getElementById('errorMessage').style.display = 'block';
            }
            
            // 聚焦到密钥输入框
            document.getElementById('apiKey').focus();
        });
    </script>
</body>
</html> 