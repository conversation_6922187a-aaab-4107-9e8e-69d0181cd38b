{"name": "interview-coder-activation-server", "version": "1.0.0", "description": "激活服务器用于验证和管理一次性激活码", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "admin": "node start-admin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "MIT", "dependencies": {"bootstrap": "^5.3.1", "cors": "^2.8.5", "datatables.net": "^1.13.6", "datatables.net-bs5": "^1.13.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "interview-coder-activation-server": "file:", "mongoose": "^8.0.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}