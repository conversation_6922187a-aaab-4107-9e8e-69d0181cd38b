const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const dotenv = require('dotenv');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const session = require('express-session');

// 加载环境变量
dotenv.config();

// 创建 Express 应用
const app = express();

// 中间件
app.use(cors({
  origin: '*', // 允许所有来源访问，生产环境中应该限制
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'x-admin-key']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 会话中间件
app.use(session({
  secret: process.env.SESSION_SECRET || 'activation-server-secret',
  resave: false,
  saveUninitialized: true,
  cookie: { secure: process.env.NODE_ENV === 'production' }
}));

// 提供静态文件 - 优化路径处理
app.use(express.static(path.join(__dirname), { 
  etag: true, 
  lastModified: true,
  setHeaders: (res, path) => {
    if (path.endsWith('.css')) {
      // 确保CSS文件的MIME类型正确
      res.setHeader('Content-Type', 'text/css');
    }
  }
}));

// 特别为node_modules添加静态文件服务
app.use('/node_modules', express.static(path.join(__dirname, 'node_modules'), {
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css');
    }
  }
}));

// 登录页面路由
app.get('/admin/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'admin-login.html'));
});

// 登录处理
app.post('/admin/login', (req, res) => {
  const { apiKey } = req.body;
  
  if (apiKey === process.env.ADMIN_API_KEY) {
    req.session.isAuthenticated = true;
    res.redirect('/admin');
  } else {
    res.status(401).send('无效的API密钥，请重试');
  }
});

// 退出登录
app.get('/admin/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/admin/login');
});

// 管理页面访问权限中间件
function requireAuth(req, res, next) {
  if (req.session.isAuthenticated) {
    next();
  } else {
    res.redirect('/admin/login');
  }
}

// 管理面板路由
app.get('/admin', requireAuth, (req, res) => {
  res.sendFile(path.join(__dirname, 'admin-panel.html'));
});

const dbUri = process.env.MONGODB_URI;
const dbUser = process.env.MONGODB_USER;
const dbPass = process.env.MONGODB_PASSWORD;
// 拼接带认证信息的 URI
const authDbUri = dbUri.replace('mongodb://', `mongodb://${dbUser}:${encodeURIComponent(dbPass)}@`);
// 数据库连接
mongoose.connect(authDbUri)
  .then(() => console.log('已连接到 MongoDB'))
  .catch(err => console.error('MongoDB 连接失败:', err));

// 定义激活码模型
const ActivationCodeSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true
  },
  isUsed: {
    type: Boolean,
    default: false
  },
  machineId: {
    type: String,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  usedAt: {
    type: Date,
    default: null
  },
  activationId: {
    type: String,
    default: null
  },
  remainingCalls: {
    type: Number,
    default: 3
  },
  isPermanent: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['active', 'disabled', 'expired'],
    default: 'active'
  },
  lastUsed: {
    type: Date,
    default: null
  },
  planType: {
    type: String,
    enum: ['standard', 'ultra'],
    default: 'standard'
  },
  deliveryStatus: {
    type: String,
    enum: ['pending', 'delivered'],
    default: 'pending'
  }
});

const ActivationCode = mongoose.model('ActivationCode', ActivationCodeSchema);

// API 认证中间件
function apiAuth(req, res, next) {
  // 对于会话中已认证的用户，自动添加管理员密钥
  if (req.session.isAuthenticated) {
    req.headers['x-admin-key'] = process.env.ADMIN_API_KEY;
    next();
    return;
  }
  
  // 否则检查请求头中的密钥
  const adminKey = req.headers['x-admin-key'];
  if (adminKey !== process.env.ADMIN_API_KEY) {
    return res.status(401).json({ success: false, message: '未授权访问' });
  }
  next();
}

// API 路由

// 1. 生成新的激活码（管理员操作）
app.post('/api/admin/generate', apiAuth, async (req, res) => {
  try {
    const { count = 1, remainingCalls = 3, isPermanent = false, planType = 'standard' } = req.body;
    const codes = [];

    // 生成指定数量的激活码
    for (let i = 0; i < count; i++) {
      // 生成随机激活码（格式：XXXX-XXXX-XXXX-XXXX）
      const segments = [];
      for (let j = 0; j < 4; j++) {
        segments.push(Math.random().toString(36).substring(2, 6).toUpperCase());
      }
      const code = segments.join('-');

      const newCode = new ActivationCode({
        code,
        remainingCalls,
        isPermanent,
        planType
      });
      await newCode.save();
      codes.push(newCode);
    }

    res.status(201).json({ success: true, codes });
  } catch (error) {
    console.error('生成激活码失败:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 2. 列出所有激活码（管理员操作）
app.get('/api/admin/codes', apiAuth, async (req, res) => {
  try {
    // 获取查询参数（支持DataTables参数格式）
    const draw = parseInt(req.query.draw) || 1;
    
    // 处理分页参数 - 优先使用pageSize参数（用于导出全部）
    let start, length, pageSize;
    
    if (req.query.pageSize) {
      // 如果指定了pageSize，使用它作为批量导出的参数
      pageSize = parseInt(req.query.pageSize) || 1000;
      start = 0;
      length = pageSize;
      // console.log(`使用pageSize模式, pageSize=${pageSize}`);
    } else {
      // 默认使用DataTables分页参数
      start = parseInt(req.query.start) || 0;
      length = parseInt(req.query.length) || 10;
      // console.log(`使用标准分页模式, start=${start}, length=${length}`);
    }
    
    // 计算页码
    const page = Math.floor(start / length) + 1;
    
    // 创建过滤条件
    const filter = {};
    
    // 激活码模糊查询
    if (req.query.code) {
      filter.code = { $regex: req.query.code, $options: 'i' };
    }
    
    // 永久有效过滤 - 只有当明确选择了"是"或"否"时才过滤
    if (req.query.isPermanent === 'true' || req.query.isPermanent === 'false') {
      filter.isPermanent = req.query.isPermanent === 'true';
    }
    
    // 使用状态过滤
    if (req.query.isUsed === 'true' || req.query.isUsed === 'false') {
      filter.isUsed = req.query.isUsed === 'true';
    }

    // 权益版本过滤
    if (req.query.planType && ['standard', 'ultra'].includes(req.query.planType)) {
      filter.planType = req.query.planType;
    }

    // 发货状态过滤
    if (req.query.deliveryStatus && ['pending', 'delivered'].includes(req.query.deliveryStatus)) {
      filter.deliveryStatus = req.query.deliveryStatus;
    }

    // 状态与永久有效的逻辑处理
    if (req.query.status && ['active', 'disabled', 'expired'].includes(req.query.status)) {
      if (req.query.status === 'expired') {
        // 特殊处理过期状态，因为永久有效的不可能过期
        // 如果同时选择了isPermanent=true，则这种组合不存在
        if (req.query.isPermanent === 'true') {
          res.json({
            success: true,
            draw: draw,
            codes: [],
            pagination: { page: 1, pageSize: length, total: 0, totalPages: 0 }
          });
          return;
        } else if (req.query.isPermanent === 'false') {
          // 明确选择了非永久，正常过滤
          filter.status = 'expired';
        } else {
          // 未指定永久状态，只显示非永久的过期码
          filter.$and = [
            { status: 'expired' },
            { isPermanent: false }
          ];
        }
      } else {
        // 对于active或disabled状态，正常过滤
        filter.status = req.query.status;
      }
    }
    
    // 调用次数范围过滤
    if (req.query.minCalls !== undefined && req.query.minCalls !== '') {
      filter.remainingCalls = filter.remainingCalls || {};
      filter.remainingCalls.$gte = parseInt(req.query.minCalls);
    }
    
    if (req.query.maxCalls !== undefined && req.query.maxCalls !== '') {
      filter.remainingCalls = filter.remainingCalls || {};
      filter.remainingCalls.$lte = parseInt(req.query.maxCalls);
    }

    // console.log('查询过滤条件:', JSON.stringify(filter)); // 调试用
    // console.log(`分页参数: start=${start}, length=${length}`); // 调试用
    
    // 获取总条数
    const total = await ActivationCode.countDocuments(filter);
    
    // 获取分页数据
    const codes = await ActivationCode.find(filter)
      .sort({ createdAt: -1 })
      .skip(start)
      .limit(length);
    
    // console.log(`查询结果: 返回 ${codes.length} 条记录，总记录数 ${total}`); // 调试用
    
    res.json({ 
      success: true,
      draw: draw,
      codes,
      pagination: {
        page,
        pageSize: length,
        total,
        totalPages: Math.ceil(total / length)
      } 
    });
  } catch (error) {
    console.error('获取激活码列表失败:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 3. 激活应用
app.post('/api/activate', async (req, res) => {
  try {
    const { activationCode, machineId, appVersion } = req.body;

    if (!activationCode || !machineId) {
      return res.status(200).json({
        success: false, 
        message: '缺少必要参数' 
      });
    }

    // 查找激活码
    const code = await ActivationCode.findOne({ code: activationCode });

    // 验证激活码
    if (!code) {
      return res.status(200).json({
        success: false, 
        message: '激活码无效或不存在' 
      });
    }

    // 检查激活码状态
    if (code.status === 'disabled') {
      return res.status(200).json({
        success: false,
        message: '此激活码已被禁用'
      });
    }

    if (code.status === 'expired') {
      return res.status(200).json({
        success: false,
        message: '此激活码已过期'
      });
    }

    // 检查是否已使用
    if (code.isUsed) {
      // 如果已使用，则检查机器ID是否匹配
      if (code.machineId !== machineId) {
        return res.status(200).json({
          success: false, 
          message: '此激活码已被其他设备使用' 
        });
      }
      
      // 机器ID匹配，返回现有的激活信息
      return res.json({
        success: true,
        message: '设备已激活',
        activationId: code.activationId,
        remainingCalls: code.remainingCalls,
        isPermanent: code.isPermanent,
        planType: code.planType
      });
    }

    // 标记激活码为已使用
    const activationId = uuidv4();
    code.isUsed = true;
    code.machineId = machineId;
    code.usedAt = new Date();
    code.activationId = activationId;
    await code.save();

    // 返回成功响应
    res.json({
      success: true,
      message: '应用已成功激活',
      activationId,
      remainingCalls: code.remainingCalls,
      isPermanent: code.isPermanent,
      planType: code.planType
    });
  } catch (error) {
    console.error('激活失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '服务器错误，请稍后重试' 
    });
  }
});

// 4. 验证激活状态
app.post('/api/verify', async (req, res) => {
  try {
    const { activationId, machineId } = req.body;

    if (!activationId || !machineId) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少必要参数',
        valid: false 
      });
    }

    // 查找对应的激活记录
    const code = await ActivationCode.findOne({ 
      activationId, 
      machineId,
      isUsed: true 
    });

    // 验证激活记录
    if (!code) {
      return res.status(200).json({ 
        success: false, 
        message: '未找到有效的激活记录',
        valid: false 
      });
    }

    // 检查激活码状态
    if (code.status === 'disabled') {
      return res.status(200).json({
        success: false,
        message: '此激活码已被禁用',
        valid: false
      });
    }

    if (code.status === 'expired') {
      return res.status(200).json({
        success: false,
        message: '此激活码已过期',
        valid: false
      });
    }

    // 返回验证成功响应
    res.json({
      success: true,
      valid: true,
      message: '激活有效',
      remainingCalls: code.remainingCalls,
      isPermanent: code.isPermanent,
      planType: code.planType
    });
  } catch (error) {
    console.error('验证失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '服务器错误，请稍后重试',
      valid: false 
    });
  }
});

// 5. 消耗调用次数
app.post('/api/consume', async (req, res) => {
  try {
    const { activationId, machineId } = req.body;

    if (!activationId || !machineId) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少必要参数' 
      });
    }

    // 查找对应的激活记录
    const code = await ActivationCode.findOne({ 
      activationId, 
      machineId,
      isUsed: true 
    });

    // 验证激活记录
    if (!code) {
      return res.status(404).json({ 
        success: false, 
        message: '未找到有效的激活记录' 
      });
    }

    // 检查激活码状态
    if (code.status === 'disabled') {
      return res.status(403).json({
        success: false,
        message: '此激活码已被禁用'
      });
    }

    if (code.status === 'expired') {
      return res.status(403).json({
        success: false,
        message: '此激活码已过期'
      });
    }

    // 检查是否为永久激活
    if (code.isPermanent) {
      // 更新最后使用时间
      code.lastUsed = new Date();
      await code.save();
      
      return res.json({
        success: true,
        message: '永久激活码，无需消耗次数',
        remainingCalls: Infinity,
        isPermanent: true,
        planType: code.planType
      });
    }

    // 检查剩余调用次数
    if (code.remainingCalls <= 0) {
      // 更新状态为过期
      code.status = 'expired';
      await code.save();
      
      return res.status(403).json({
        success: false,
        message: '剩余调用次数已用尽',
        remainingCalls: 0
      });
    }

    // 减少调用次数
    code.remainingCalls -= 1;
    code.lastUsed = new Date();
    
    // 如果次数用完，标记为过期
    if (code.remainingCalls <= 0) {
      code.status = 'expired';
    }
    
    await code.save();

    res.json({
      success: true,
      message: '成功消耗一次调用',
      remainingCalls: code.remainingCalls,
      planType: code.planType
    });
  } catch (error) {
    console.error('消耗调用次数失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '服务器错误，请稍后重试' 
    });
  }
});

// 6. 管理员重置/更新激活码（授权次数或永久有效）
app.post('/api/admin/update-code', apiAuth, async (req, res) => {
  try {
    const { code, remainingCalls, isPermanent, status, planType } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少激活码参数'
      });
    }

    // 查找激活码
    const activationCode = await ActivationCode.findOne({ code });

    if (!activationCode) {
      return res.status(404).json({
        success: false,
        message: '激活码不存在'
      });
    }

    // 更新激活码信息
    if (remainingCalls !== undefined) {
      activationCode.remainingCalls = remainingCalls;
    }

    if (isPermanent !== undefined) {
      activationCode.isPermanent = isPermanent;
    }

    if (status !== undefined) {
      activationCode.status = status;
    }

    if (planType !== undefined) {
      activationCode.planType = planType;
    }

    // 如果重新激活，确保状态正确
    if (status === 'active' && activationCode.remainingCalls <= 0 && !activationCode.isPermanent) {
      activationCode.remainingCalls = 3; // 重新给予默认次数
    }

    await activationCode.save();

    res.json({ 
      success: true, 
      message: '激活码已更新',
      code: activationCode
    });
  } catch (error) {
    console.error('更新激活码失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '服务器错误' 
    });
  }
});

// 7. 管理员重置激活码
app.post('/api/admin/reset-code', apiAuth, async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少激活码参数'
      });
    }

    // 查找激活码
    const activationCode = await ActivationCode.findOne({ code });

    if (!activationCode) {
      return res.status(404).json({
        success: false,
        message: '激活码不存在'
      });
    }

    // 重置激活码状态
    activationCode.isUsed = false;
    activationCode.machineId = null;
    activationCode.usedAt = null;
    activationCode.activationId = null;
    activationCode.remainingCalls = 3;  // 重置默认调用次数
    activationCode.isPermanent = false; // 取消永久激活状态
    activationCode.status = 'active';   // 恢复为激活状态

    await activationCode.save();

    res.json({
      success: true,
      message: '激活码已重置',
      code: activationCode
    });
  } catch (error) {
    console.error('重置激活码失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 8. 发货激活码
app.post('/api/admin/deliver-code', apiAuth, async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少激活码参数'
      });
    }

    // 查找激活码
    const activationCode = await ActivationCode.findOne({ code });

    if (!activationCode) {
      return res.status(404).json({
        success: false,
        message: '激活码不存在'
      });
    }

    if (activationCode.deliveryStatus === 'delivered') {
      return res.status(400).json({
        success: false,
        message: '该激活码已经发货'
      });
    }

    // 更新发货状态
    activationCode.deliveryStatus = 'delivered';
    await activationCode.save();

    res.json({
      success: true,
      message: '激活码发货成功',
      code: activationCode.code
    });
  } catch (error) {
    console.error('发货激活码失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

// 9. 退货激活码
app.post('/api/admin/return-code', apiAuth, async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少激活码参数'
      });
    }

    // 查找激活码
    const activationCode = await ActivationCode.findOne({ code });

    if (!activationCode) {
      return res.status(404).json({
        success: false,
        message: '激活码不存在'
      });
    }

    if (activationCode.deliveryStatus === 'pending') {
      return res.status(400).json({
        success: false,
        message: '该激活码尚未发货'
      });
    }

    // 退货操作：重置激活码状态并改为未发货
    activationCode.isUsed = false;
    activationCode.machineId = null;
    activationCode.usedAt = null;
    activationCode.activationId = null;
    activationCode.remainingCalls = 3;  // 重置默认调用次数
    activationCode.isPermanent = false; // 取消永久激活状态
    activationCode.status = 'active';   // 恢复为激活状态
    activationCode.deliveryStatus = 'pending'; // 改为未发货状态

    await activationCode.save();

    res.json({
      success: true,
      message: '激活码退货成功',
      code: activationCode
    });
  } catch (error) {
    console.error('退货激活码失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});



// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`激活服务器运行在端口: ${PORT}`);
});