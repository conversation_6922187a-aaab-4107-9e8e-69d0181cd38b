# 数据迁移说明

## 概述
本文档说明如何执行数据迁移，为现有激活码添加权益版本和发货状态字段。

## 迁移内容
- **权益版本字段 (planType)**: 为所有现有激活码设置为 "standard"（标准版）
- **发货状态字段 (deliveryStatus)**: 
  - 已使用的激活码设置为 "delivered"（已发货）
  - 未使用的激活码设置为 "pending"（未发货）

## 执行步骤

### 1. 首次上线前执行迁移
```bash
# 进入激活服务器目录
cd activation-server

# 执行数据迁移脚本
node migrate-data.js
```

### 2. 迁移脚本输出示例
```
开始数据迁移...

1. 检查权益版本字段...
发现 140 条记录需要设置默认权益版本
✓ 权益版本字段迁移完成，更新了 140 条记录

2. 检查发货状态字段...
发现 140 条记录需要设置发货状态
✓ 发货状态字段迁移完成，已使用记录设为已发货: 85 条
✓ 发货状态字段迁移完成，未使用记录设为未发货: 55 条

3. 验证迁移结果...
总激活码数量: 140
标准版: 140 条，Ultra版: 0 条
未发货: 55 条，已发货: 85 条

✅ 数据迁移完成！

迁移脚本执行完成，可以安全关闭。
```

### 3. 启动服务器
迁移完成后，正常启动服务器：
```bash
npm start
```

## 注意事项
- 迁移脚本只需要执行一次
- 迁移前建议备份数据库
- 迁移脚本是幂等的，重复执行不会造成问题
- 迁移完成后，服务器启动时不会再自动执行迁移

## 验证迁移结果
可以通过管理界面验证：
1. 访问 `http://localhost:3000/admin`
2. 查看激活码列表，确认权益版本和发货状态列显示正常
3. 测试筛选功能是否正常工作
