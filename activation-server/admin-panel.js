// 管理平台JavaScript逻辑

// 配置
const SERVER_URL = window.location.origin; // 使用当前站点作为API服务器地址
let dataTable; // DataTable实例

// 在页面加载完成后初始化
$(document).ready(function() {
    // 初始化函数
    function init() {
        console.log('初始化管理面板...');
        
        // 添加退出按钮到顶部导航栏，使用更醒目的设计
        $('.header .container').append(`
            <a href="/admin/logout" class="btn btn-danger float-end d-flex align-items-center" 
               style="font-size: 1rem; padding: 8px 15px; margin-top: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-right me-2" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
                    <path fill-rule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                </svg>
                退出登录
            </a>
        `);
        
        // 初始化组件
        initDataTable();
        bindEvents();
        
        // 初始化Toast容器
        if (!document.getElementById('toastContainer')) {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        
        console.log('初始化完成');
    }
    
    // 初始化DataTable
    function initDataTable() {
        dataTable = $('#codesDataTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: SERVER_URL + '/api/admin/codes',
                type: 'GET',
                data: function(d) {
                    // 将DataTables的请求参数转换为API所需的格式
                    return {
                        draw: d.draw, // 传递draw参数，这是修复分页的关键
                        start: d.start,
                        length: d.length,
                        code: $('#filterCode').val(),
                        status: $('#filterStatus').val(),
                        isUsed: $('#filterUsed').val(),
                        isPermanent: $('#filterPermanent').val(),
                        planType: $('#filterPlanType').val(),
                        deliveryStatus: $('#filterDeliveryStatus').val(),
                        minCalls: $('#filterMinCalls').val() || undefined,
                        maxCalls: $('#filterMaxCalls').val() || undefined
                    };
                },
                dataFilter: function(data) {
                    // 将API响应转换为DataTables所需的格式
                    const json = JSON.parse(data);
                    debug('收到数据: ' + JSON.stringify(json).substring(0, 100) + '...');
                    
                    if (!json.success) {
                        showToast(json.message || '加载数据失败', 'danger');
                        return JSON.stringify({
                            draw: json.draw || 1,
                            recordsTotal: 0,
                            recordsFiltered: 0,
                            data: []
                        });
                    }
                    
                    return JSON.stringify({
                        draw: json.draw || 1, // 使用服务器返回的draw值或默认值
                        recordsTotal: json.pagination.total,
                        recordsFiltered: json.pagination.total,
                        data: json.codes
                    });
                },
                error: function(xhr, error, thrown) {
                    console.error('获取数据失败:', error);
                    
                    if (xhr.status === 401) {
                        // 如果未授权，重定向到登录页面
                        window.location.href = '/admin/login?error=1';
                    } else {
                        showToast('获取数据失败: ' + error, 'danger');
                    }
                }
            },
            columns: [
                { data: 'code' },
                { 
                    data: 'isUsed',
                    render: function(data) {
                        return data ? 
                            '<span class="label-used">已使用</span>' : 
                            '<span class="label-unused">未使用</span>';
                    } 
                },
                { 
                    data: 'status',
                    render: function(data) {
                        let cls, text;
                        switch (data) {
                            case 'active':
                                cls = 'label-active';
                                text = '正常';
                                break;
                            case 'disabled':
                                cls = 'label-disabled';
                                text = '禁用';
                                break;
                            case 'expired':
                                cls = 'label-expired';
                                text = '过期';
                                break;
                            default:
                                cls = '';
                                text = data || '未知';
                        }
                        return `<span class="${cls}">${text}</span>`;
                    } 
                },
                { 
                    data: null,
                    render: function(data) {
                        return data.isPermanent ? '无限' : data.remainingCalls;
                    } 
                },
                {
                    data: 'isPermanent',
                    render: function(data) {
                        return data ?
                            '<span class="label-permanent">是</span>' :
                            '<span>否</span>';
                    }
                },
                {
                    data: 'planType',
                    render: function(data) {
                        return data === 'ultra' ? 'Ultra版' : '标准版';
                    }
                },
                {
                    data: 'deliveryStatus',
                    render: function(data) {
                        return data === 'delivered' ?
                            '<span class="label-delivered">已发货</span>' :
                            '<span class="label-pending">未发货</span>';
                    }
                },
                {
                    data: 'usedAt',
                    render: function(data) {
                        return data ? new Date(data).toLocaleString() : 'N/A';
                    }
                },
                { data: 'machineId', defaultContent: 'N/A' },
                {
                    data: null,
                    orderable: false,
                    render: function(data) {
                        const deliveryBtn = data.deliveryStatus === 'delivered'
                            ? `<button class="btn btn-outline-warning return-btn" data-code="${data.code}">退货</button>`
                            : `<button class="btn btn-outline-success deliver-btn" data-code="${data.code}">发货</button>`;

                        return `
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary edit-btn" data-code="${data.code}">编辑</button>
                                <button class="btn btn-outline-danger reset-btn" data-code="${data.code}">重置</button>
                                ${deliveryBtn}
                            </div>
                        `;
                    }
                }
            ],
            language: {
                "processing": "处理中...",
                "lengthMenu": "显示 _MENU_ 条",
                "zeroRecords": "没有找到匹配的记录",
                "info": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                "infoEmpty": "显示第 0 至 0 项结果，共 0 项",
                "infoFiltered": "(由 _MAX_ 项结果过滤)",
                "search": "搜索:",
                "emptyTable": "表中数据为空",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                }
            },
            pagingType: window.innerWidth <= 768 ? "simple" : "full_numbers",
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
            pageLength: 10,
            dom: window.innerWidth <= 768 ? '<"top"l>rt<"bottom"p><"clear">' : '<"top"l>rt<"bottom"ip><"clear">',
            responsive: true,
            scrollX: true,
            columnDefs: [
                {
                    targets: [0], // 激活码列
                    className: 'text-break'
                },
                {
                    targets: [7, 8], // 使用时间和机器ID列在小屏幕隐藏
                    responsivePriority: 10000
                },
                {
                    targets: [9], // 操作列
                    responsivePriority: 1
                }
            ]
        });
        
        // 表格内按钮事件绑定
        $('#codesDataTable tbody').on('click', '.edit-btn', function() {
            const code = $(this).data('code');
            editCode(code);
        });

        $('#codesDataTable tbody').on('click', '.reset-btn', function() {
            const code = $(this).data('code');
            resetCode(code);
        });

        $('#codesDataTable tbody').on('click', '.deliver-btn', function() {
            const code = $(this).data('code');
            deliverCode(code);
        });

        $('#codesDataTable tbody').on('click', '.return-btn', function() {
            const code = $(this).data('code');
            returnCode(code);
        });
    }

    // 绑定事件处理函数
    function bindEvents() {
        // 生成激活码按钮
        $('#generateBtn').click(function() {
            const generateModal = new bootstrap.Modal(document.getElementById('generateModal'));
            generateModal.show();
        });
        
        // 生成激活码提交按钮
        $('#generateSubmitBtn').click(function() {
            generateActivationCodes();
        });
        
        // 保存编辑激活码按钮
        $('#saveEditBtn').click(function() {
            saveActivationCodeChanges();
        });
        
        // 搜索按钮
        $('#searchBtn').click(function() {
            dataTable.ajax.reload();
        });
        
        // 重置搜索按钮
        $('#resetSearchBtn').click(function() {
            $('#filterCode').val('');
            $('#filterStatus').val('');
            $('#filterUsed').val('');
            $('#filterPermanent').val(''); // 清空所有查询条件
            $('#filterPlanType').val(''); // 清空所有查询条件
            $('#filterDeliveryStatus').val(''); // 清空所有查询条件
            $('#filterMinCalls').val('');
            $('#filterMaxCalls').val('');
            dataTable.ajax.reload();
        });

        // 试用查询按钮
        $('#trialQueryBtn').click(function() {
            $('#filterCode').val('');
            $('#filterStatus').val('');
            $('#filterUsed').val('');
            $('#filterPermanent').val('false'); // 永久有效：否
            $('#filterPlanType').val('ultra'); // 权益版本：ultra版
            $('#filterDeliveryStatus').val('pending'); // 发货状态：未发货
            $('#filterMinCalls').val('');
            $('#filterMaxCalls').val('3'); // 最大调用次数：3
            dataTable.ajax.reload();
        });

        // 百次查询按钮
        $('#hundredQueryBtn').click(function() {
            $('#filterCode').val('');
            $('#filterStatus').val('');
            $('#filterUsed').val('');
            $('#filterPermanent').val('false'); // 永久有效：否
            $('#filterPlanType').val(''); // 权益版本：全部
            $('#filterDeliveryStatus').val('pending'); // 发货状态：未发货
            $('#filterMinCalls').val('100');
            $('#filterMaxCalls').val('100'); // 最大调用次数：100
            dataTable.ajax.reload();
        });

        // 永久查询按钮
        $('#permanentQueryBtn').click(function() {
            $('#filterCode').val('');
            $('#filterStatus').val('');
            $('#filterUsed').val('');
            $('#filterPermanent').val('true'); // 永久有效：是
            $('#filterPlanType').val(''); // 权益版本：全部
            $('#filterDeliveryStatus').val('pending'); // 发货状态：未发货
            $('#filterMinCalls').val('');
            $('#filterMaxCalls').val('');
            dataTable.ajax.reload();
        });
        
        // 刷新列表按钮
        $('#refreshBtn').click(function() {
            dataTable.ajax.reload();
        });
        
        // 导出按钮
        $('#exportBtn').click(exportActivationCodes);
    }
    
    // 显示提示消息
    function showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        
        // 如果不存在容器，创建一个
        if (!toastContainer) {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        
        // 创建toast元素
        const toastId = 'toast-' + Date.now();
        const toastEl = document.createElement('div');
        toastEl.id = toastId;
        toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        document.getElementById('toastContainer').appendChild(toastEl);
        
        // 显示toast
        const toast = new bootstrap.Toast(document.getElementById(toastId), {
            autohide: true,
            delay: 3000
        });
        toast.show();
        
        // 自动删除DOM元素
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
    
    // API请求基础函数
    async function apiRequest(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include' // 包含凭据（cookies）
            };
            
            if (data && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(data);
            }
            
            console.log(`发送API请求到: ${SERVER_URL}${endpoint}`);
            const response = await fetch(`${SERVER_URL}${endpoint}`, options);
            const result = await response.json();
            
            if (response.status === 401) {
                // 如果未授权，重定向到登录页面
                window.location.href = '/admin/login?error=1';
                return null;
            }
            
            return result;
        } catch (error) {
            console.error('API请求错误:', error);
            showToast(`请求失败: ${error.message}`, 'danger');
            return { success: false, message: error.message };
        }
    }

    // 生成新的激活码
    async function generateActivationCodes() {
        const count = $('#codeCount').val() || 1;
        const remainingCalls = $('#callCount').val() || 3;
        const isPermanent = $('#isPermanent').prop('checked');
        const planType = $('#planType').val() || 'standard';

        // 显示加载指示器
        $('#generateSubmitBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 生成中...');

        const result = await apiRequest('/api/admin/generate', 'POST', {
            count,
            remainingCalls,
            isPermanent,
            planType
        });
        
        // 恢复按钮状态
        $('#generateSubmitBtn').prop('disabled', false).text('生成');
        
        if (result && result.success) {
            // 隐藏生成模态框
            bootstrap.Modal.getInstance(document.getElementById('generateModal')).hide();
            
            // 显示生成的激活码
            let codesList = '';
            result.codes.forEach(code => {
                codesList += `<div class="generated-code">${code.code} (调用次数: ${isPermanent ? '无限' : code.remainingCalls})</div>`;
            });
            
            $('#generatedCodesList').html(codesList);
            
            // 显示查看激活码模态框
            const viewCodesModal = new bootstrap.Modal(document.getElementById('viewCodesModal'));
            viewCodesModal.show();
            
            // 刷新表格
            dataTable.ajax.reload();
            
            showToast(`成功生成 ${result.codes.length} 个激活码`, 'success');
        } else {
            showToast(`生成激活码失败: ${result?.message || '未知错误'}`, 'danger');
        }
    }
    
    // 导出激活码到文件
    async function exportActivationCodes() {
        // 显示导出中提示
        $('#exportBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 导出中...');
        
        // 确认用户选择导出的范围
        const exportChoice = confirm('选择导出范围：\n确定 - 仅导出当前页数据\n取消 - 导出所有匹配数据');
        
        try {
            // 构建基础过滤参数
            const baseParams = {
                draw: 1,
                code: $('#filterCode').val() || undefined,
                status: $('#filterStatus').val() || undefined,
                isUsed: $('#filterUsed').val() || undefined,
                isPermanent: $('#filterPermanent').val() || undefined,
                planType: $('#filterPlanType').val() || undefined,
                deliveryStatus: $('#filterDeliveryStatus').val() || undefined,
                minCalls: $('#filterMinCalls').val() || undefined,
                maxCalls: $('#filterMaxCalls').val() || undefined
            };
            
            let endpoint;
            
            if (exportChoice) {
                // 导出当前页数据
                const info = dataTable.page.info();
                endpoint = `/api/admin/codes?draw=1&start=${info.start}&length=${info.length}`;
                
                // 添加过滤参数
                Object.entries(baseParams).forEach(([key, value]) => {
                    if (value !== undefined && value !== null && value !== '') {
                        endpoint += `&${key}=${encodeURIComponent(value)}`;
                    }
                });
                
            } else {
                // 导出所有匹配数据
                // 这里不使用start/length，而是直接使用pageSize参数
                endpoint = '/api/admin/codes?draw=1&pageSize=1000';
                
                // 添加过滤参数
                Object.entries(baseParams).forEach(([key, value]) => {
                    if (value !== undefined && value !== null && value !== '') {
                        endpoint += `&${key}=${encodeURIComponent(value)}`;
                    }
                });
            }
            
            console.log('导出请求URL:', endpoint); // 添加调试信息
            
            const result = await apiRequest(endpoint);
            
            // 恢复按钮状态
            $('#exportBtn').prop('disabled', false).text('导出激活码');
            
            if (result && result.success && result.codes) {
                // 创建CSV内容
                let csvContent = '激活码,状态,生成时间,使用时间,机器ID,剩余调用次数,永久有效,权益版本,发货状态,激活状态\n';

                result.codes.forEach(code => {
                    const status = code.isUsed ? '已使用' : '未使用';
                    const createdAt = new Date(code.createdAt).toLocaleString();
                    const usedAt = code.usedAt ? new Date(code.usedAt).toLocaleString() : '';
                    const remainingCalls = code.isPermanent ? '无限' : code.remainingCalls;
                    const permanent = code.isPermanent ? '是' : '否';
                    const planType = code.planType === 'ultra' ? 'Ultra版' : '标准版';
                    const deliveryStatus = code.deliveryStatus === 'delivered' ? '已发货' : '未发货';
                    const activeStatus = code.status === 'active' ? '正常' : (code.status === 'disabled' ? '禁用' : '过期');

                    csvContent += `${code.code},${status},${createdAt},${usedAt},${code.machineId || ''},${remainingCalls},${permanent},${planType},${deliveryStatus},${activeStatus}\n`;
                });
                
                // 创建下载链接
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;bom=\uFEFF' });
                const url = URL.createObjectURL(blob);
                const now = new Date();
                const filename = `activation-codes-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}.csv`;
                
                // 创建临时下载链接并点击
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', filename);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                const mode = exportChoice ? '当前页' : '所有匹配';
                showToast(`成功导出${mode}数据，共 ${result.codes.length} 个激活码`, 'success');
            } else {
                showToast(`导出激活码失败: ${result?.message || '未知错误'}`, 'danger');
            }
        } catch (error) {
            $('#exportBtn').prop('disabled', false).text('导出激活码');
            console.error('导出过程出错:', error);
            showToast(`导出失败: ${error.message}`, 'danger');
        }
    }
    
    // 编辑激活码
    async function editCode(code) {
        // 显示加载中
        showToast('正在加载激活码信息...', 'info');
        
        try {
            // 查找激活码
            const result = await apiRequest('/api/admin/codes?code=' + code);
            
            if (!result || !result.success || !result.codes || result.codes.length === 0) {
                showToast(`未找到激活码: ${code}`, 'danger');
                return;
            }
            
            const codeData = result.codes[0];
            
            // 填充数据到编辑表单
            $('#editCode').val(codeData.code);
            $('#displayCode').text(codeData.code);
            $('#editCallCount').val(codeData.remainingCalls);
            $('#editIsPermanent').prop('checked', codeData.isPermanent);
            $('#editPlanType').val(codeData.planType || 'standard');
            $('#editStatus').val(codeData.status);
            
            // 显示编辑模态框
            const editModal = new bootstrap.Modal(document.getElementById('editModal'));
            editModal.show();
            
        } catch (error) {
            console.error('获取激活码信息失败:', error);
            showToast(`获取激活码信息失败: ${error.message}`, 'danger');
        }
    }
    
    // 保存激活码更改
    async function saveActivationCodeChanges() {
        const code = $('#editCode').val();
        const remainingCalls = parseInt($('#editCallCount').val());
        const isPermanent = $('#editIsPermanent').prop('checked');
        const planType = $('#editPlanType').val();
        const status = $('#editStatus').val();

        if (!code) {
            showToast('激活码不能为空', 'warning');
            return;
        }

        if (isNaN(remainingCalls) || remainingCalls < 0) {
            showToast('请输入有效的调用次数', 'warning');
            return;
        }

        // 禁用保存按钮，显示加载中
        $('#saveEditBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...');

        const result = await apiRequest('/api/admin/update-code', 'POST', {
            code,
            remainingCalls,
            isPermanent,
            planType,
            status
        });
        
        // 恢复按钮状态
        $('#saveEditBtn').prop('disabled', false).text('保存');
        
        if (result && result.success) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            
            // 刷新表格
            dataTable.ajax.reload();
            
            showToast(`成功更新激活码: ${code}`, 'success');
        } else {
            showToast(`更新激活码失败: ${result?.message || '未知错误'}`, 'danger');
        }
    }
    
    // 重置激活码
    async function resetCode(code) {
        if (confirm(`确定要重置激活码 ${code} 吗？这将清除所有使用记录。`)) {
            const result = await apiRequest('/api/admin/reset-code', 'POST', { code });

            if (result && result.success) {
                dataTable.ajax.reload();
                showToast(`成功重置激活码: ${code}`, 'success');
            } else {
                showToast(`重置激活码失败: ${result?.message || '未知错误'}`, 'danger');
            }
        }
    }

    // 发货激活码
    async function deliverCode(code) {
        const result = await apiRequest('/api/admin/deliver-code', 'POST', { code });

        if (result && result.success) {
            // 复制激活码到剪贴板
            try {
                await navigator.clipboard.writeText(code);
                showToast(`激活码 ${code} 发货成功，已复制到剪贴板`, 'success');
            } catch (err) {
                // 如果复制失败，使用备用方法
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast(`激活码 ${code} 发货成功，已复制到剪贴板`, 'success');
            }

            dataTable.ajax.reload();
        } else {
            showToast(`发货失败: ${result?.message || '未知错误'}`, 'danger');
        }
    }

    // 退货激活码
    async function returnCode(code) {
        if (confirm(`确定要退货激活码 ${code} 吗？这将重置激活码并改为未发货状态。`)) {
            const result = await apiRequest('/api/admin/return-code', 'POST', { code });

            if (result && result.success) {
                dataTable.ajax.reload();
                showToast(`激活码 ${code} 退货成功`, 'success');
            } else {
                showToast(`退货失败: ${result?.message || '未知错误'}`, 'danger');
            }
        }
    }

    // 移动端优化函数
    function optimizeForMobile() {
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // 移动端时简化表格显示
            if (dataTable) {
                // 重新配置分页类型
                dataTable.page.len(5); // 移动端显示更少条目
            }

            // 优化模态框
            $('.modal-dialog').addClass('modal-dialog-centered');

            // 优化按钮文字
            $('#generateBtn').html('<i class="bi bi-plus-circle"></i> 生成');
            $('#exportBtn').html('<i class="bi bi-file-earmark-excel"></i> 导出');
            $('#refreshBtn').html('<i class="bi bi-arrow-repeat"></i> 刷新');
        } else {
            // 桌面端恢复完整文字
            $('#generateBtn').html('<i class="bi bi-plus-circle"></i> 生成激活码');
            $('#exportBtn').html('<i class="bi bi-file-earmark-excel"></i> 导出激活码');
            $('#refreshBtn').html('<i class="bi bi-arrow-repeat"></i> 刷新列表');
        }
    }

    // 窗口大小改变时重新优化
    $(window).resize(function() {
        optimizeForMobile();
    });

    // 启动初始化
    init();

    // 初始化后执行移动端优化
    setTimeout(optimizeForMobile, 100);
});