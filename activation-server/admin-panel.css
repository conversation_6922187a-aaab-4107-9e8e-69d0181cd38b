/* 激活码管理系统样式 */

/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* 布局样式 */
.header {
    background: linear-gradient(to right, #1e88e5, #42a5f5);
    color: white;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-title {
    font-size: 24px;
    margin: 0;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.container {
    padding: 15px;
}

/* 面板样式 */
.search-panel {
    background: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* 数据表格样式增强 */
.datagrid-header-row {
    background-color: #f5f5f5;
    height: 35px;
}

.datagrid-row {
    height: 34px;
}

/* 自定义标签样式 */
.badge-active {
    color: white;
    background-color: #28a745;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.badge-disabled {
    color: white;
    background-color: #dc3545;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.badge-expired {
    color: white;
    background-color: #6c757d;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.badge-used {
    color: white;
    background-color: #17a2b8;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.badge-unused {
    color: white;
    background-color: #28a745;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.badge-permanent {
    color: white;
    background-color: #6f42c1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-delivered {
    color: white;
    background-color: #28a745;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-pending {
    color: white;
    background-color: #ffc107;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* 其他标签样式 */
.label-used {
    color: white;
    background-color: #17a2b8;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-unused {
    color: white;
    background-color: #28a745;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-active {
    color: white;
    background-color: #28a745;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-disabled {
    color: white;
    background-color: #dc3545;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-expired {
    color: white;
    background-color: #6c757d;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.label-permanent {
    color: white;
    background-color: #6f42c1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* EasyUI组件美化 */
.l-btn {
    transition: all 0.2s ease;
}

.l-btn:hover {
    opacity: 0.9;
}

.panel-header {
    background: linear-gradient(to bottom, #fdfdfd, #f5f5f5);
}

/* 弹窗样式增强 */
.window {
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.window-header {
    background: linear-gradient(to bottom, #fafafa, #f0f0f0);
    padding: 8px;
}

/* 生成的激活码列表样式 */
.generated-code {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-family: Consolas, monospace;
    font-size: 14px;
}

/* 操作按钮样式 */
.action-buttons {
    white-space: nowrap;
}

/* 调试区域样式 */
#debugArea {
    border: 1px solid #ddd;
    background: #fafafa;
    padding: 15px;
    margin-top: 20px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
}

#debugContent {
    font-family: Consolas, monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
    /* 头部适配 */
    .header {
        padding: 10px 15px;
        text-align: center;
    }

    .app-title {
        font-size: 20px;
    }

    /* 容器适配 */
    .container {
        padding: 10px;
    }

    /* 搜索面板适配 */
    .search-panel {
        padding: 10px;
    }

    .search-panel input,
    .search-panel select {
        width: 100% !important;
        margin-bottom: 10px;
        font-size: 16px; /* 防止iOS缩放 */
    }

    /* 按钮组适配 */
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-bottom: 5px;
        margin-right: 0;
        width: 100%;
    }

    /* 操作按钮适配 */
    .action-buttons {
        white-space: normal;
    }

    .btn-group-sm .btn {
        font-size: 12px;
        padding: 4px 8px;
        margin-bottom: 3px;
    }

    /* 表格适配 */
    .table-responsive {
        border: none;
    }

    .table {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 6px 4px;
        vertical-align: middle;
    }

    /* 激活码列特殊处理 */
    .table td:first-child {
        font-family: Consolas, monospace;
        font-size: 10px;
        word-break: break-all;
        max-width: 80px;
    }

    /* 模态框适配 */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .modal-body {
        padding: 15px;
    }

    /* 生成的激活码适配 */
    .generated-code {
        font-size: 12px;
        padding: 8px;
        word-break: break-all;
    }

    /* 标签适配 */
    .label-used,
    .label-unused,
    .label-active,
    .label-disabled,
    .label-expired,
    .label-permanent,
    .label-delivered,
    .label-pending {
        font-size: 10px;
        padding: 1px 4px;
    }

    /* 调试区域适配 */
    #debugArea {
        padding: 10px;
        font-size: 11px;
    }

    #debugContent {
        font-size: 10px;
    }
}

/* 超小屏幕适配 (手机竖屏) */
@media (max-width: 480px) {
    .app-title {
        font-size: 18px;
    }

    .container {
        padding: 5px;
    }

    .card {
        margin-bottom: 10px;
    }

    .card-header {
        padding: 8px 12px;
        font-size: 14px;
    }

    .card-body {
        padding: 10px;
    }

    /* 搜索条件行适配 */
    .row.align-items-end > .col-md-2,
    .row.align-items-end > .col-md-3,
    .row.align-items-end > .col-md-4 {
        margin-bottom: 10px;
    }

    /* 表格进一步压缩 */
    .table {
        font-size: 11px;
    }

    .table th,
    .table td {
        padding: 4px 2px;
    }

    /* 按钮进一步压缩 */
    .btn-sm {
        font-size: 11px;
        padding: 3px 6px;
    }

    /* 模态框进一步适配 */
    .modal-dialog {
        margin: 5px;
        max-width: calc(100% - 10px);
    }

    .modal-header {
        padding: 10px 15px;
    }

    .modal-title {
        font-size: 16px;
    }

    .modal-body {
        padding: 10px 15px;
    }

    .modal-footer {
        padding: 10px 15px;
    }
}

/* 横屏适配 */
@media (max-width: 768px) and (orientation: landscape) {
    .header {
        padding: 8px 15px;
    }

    .app-title {
        font-size: 18px;
    }

    .container {
        padding: 8px;
    }

    /* 搜索面板在横屏时更紧凑 */
    .search-panel {
        padding: 8px;
    }

    .form-group {
        margin-bottom: 8px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增大可点击区域 */
    .btn {
        min-height: 44px;
        padding: 8px 12px;
    }

    .btn-sm {
        min-height: 36px;
        padding: 6px 10px;
    }

    /* 表格行增高便于点击 */
    .table td {
        min-height: 40px;
    }

    /* 输入框增大 */
    .form-control,
    .form-select {
        min-height: 44px;
        font-size: 16px;
    }
}