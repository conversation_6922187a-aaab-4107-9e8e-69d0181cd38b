# 激活码管理系统

本系统提供了一个激活码生成、管理和验证的完整解决方案，包括服务器端API和网页版管理面板。

## 功能特点

- 生成和管理激活码
- 设置激活码调用次数限制
- 设置激活码永久有效
- 控制激活码状态(启用/禁用/过期)
- 重置激活码
- 导出激活码到CSV文件
- 查看所有激活码及其状态

## 系统需求

- Node.js (v12+)
- MongoDB
- 现代浏览器(Chrome, Firefox, Safari, Edge)

## 安装指南

1. 克隆或下载本项目
2. 在项目根目录运行 `npm install` 安装依赖
3. 创建一个 `.env` 文件包含以下配置:

```
MONGODB_URI=mongodb://localhost:27017/activation-db
MONGODB_USER=your_username
MONGODB_PASSWORD=your_password
ADMIN_API_KEY=your_admin_secret_key
PORT=3000
```

4. 运行 `npm start` 启动服务器

## 使用方法

### 命令行管理工具

可以通过运行 `npm run admin-cli` 使用命令行工具管理激活码。

### 网页管理面板

1. 启动服务器：`npm run admin`
2. 在浏览器中手动访问 `http://localhost:3000/admin` 打开管理面板
3. 首次使用时需要输入管理员API密钥(即 `.env` 文件中的 `ADMIN_API_KEY`)
4. 通过界面可以执行所有激活码管理操作

## 管理功能

### 生成新的激活码
- 可指定生成数量
- 可设置初始调用次数
- 可设置为永久有效

### 查看激活码
- 列出所有激活码
- 显示状态、调用次数、使用情况等信息

### 导出激活码
- 将所有激活码导出为CSV文件，便于备份和批量处理

### 修改激活码权限
- 更新调用次数
- 设置永久有效状态
- 控制启用/禁用状态
- 重置激活码

## 界面问题排查

如果管理界面出现样式问题：

1. 按 Alt+D 显示调试信息区域，检查是否有错误
2. 确认Bootstrap是否正确加载
3. 刷新页面重试
4. 确认浏览器支持现代CSS特性

## API说明

服务器提供了以下API端点：

- `POST /api/admin/generate` - 生成新的激活码(需要管理员权限)
- `GET /api/admin/codes` - 获取所有激活码(需要管理员权限)
- `POST /api/admin/update-code` - 更新激活码信息(需要管理员权限)
- `POST /api/admin/reset-code` - 重置激活码(需要管理员权限)
- `POST /api/activate` - 激活应用程序(客户端使用)
- `POST /api/verify` - 验证激活状态(客户端使用)
- `POST /api/consume` - 消耗调用次数(客户端使用)

请参考代码或联系管理员获取更详细的API文档。

## 安全性注意事项

- 请妥善保管管理员API密钥
- 在生产环境中，建议使用HTTPS协议保护API调用
- 定期导出并备份激活码数据 