// 启动激活码管理平台
const { spawn } = require('child_process');
const path = require('path');

console.log('正在启动激活码管理系统...');

// 启动服务器
const server = spawn('node', ['server.js'], {
  stdio: 'inherit',
  shell: true
});

// 提示用户如何访问管理界面
console.log('\n服务器已启动，您可以通过以下方式访问管理界面:');
console.log('---------------------------------------------');
console.log('本地访问: http://localhost:3000/admin');
console.log('局域网访问: http://本机IP:3000/admin');
console.log('---------------------------------------------');

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.kill();
  process.exit();
});

console.log('\n按 Ctrl+C 可停止服务器'); 