<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>激活码管理系统</title>
    
    <!-- 使用本地安装的Bootstrap和DataTables -->
    <link rel="stylesheet" href="node_modules/bootstrap/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="node_modules/datatables.net-bs5/css/dataTables.bootstrap5.min.css">
    
    <style>
        /* 自定义样式 */
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(to right, #0d6efd, #0dcaf0);
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .container {
            margin-top: 20px;
        }
        .card {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: 500;
        }
        .search-card {
            margin-bottom: 20px;
        }
        .search-group {
            margin-bottom: 10px;
        }
        .btn-group-sm .btn {
            margin-right: 3px;
        }
        .label-active {
            background-color: #198754;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .label-disabled {
            background-color: #dc3545;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .label-expired {
            background-color: #6c757d;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .label-used {
            background-color: #0dcaf0;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .label-unused {
            background-color: #198754;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .label-permanent {
            background-color: #6f42c1;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        /* 调试区域 */
        #debugArea {
            display: none;
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: consolas, monospace;
            font-size: 14px;
        }
        .generated-code {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #f8f9fa;
            font-family: consolas, monospace;
        }

        /* 移动端按钮组优化 */
        @media (max-width: 768px) {
            .mb-3 .btn {
                display: block;
                width: 100%;
                margin-bottom: 8px;
                margin-right: 0 !important;
            }

            .row.mb-3 {
                margin-bottom: 15px;
            }

            /* 搜索面板移动端优化 */
            .search-card .row > div {
                margin-bottom: 10px;
            }

            /* 搜索按钮区域移动端优化 */
            .search-card .col-md-8 .btn {
                display: block;
                width: 100%;
                margin-bottom: 8px;
                margin-right: 0 !important;
            }

            /* DataTables移动端优化 */
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_paginate {
                text-align: center;
                margin-bottom: 10px;
            }

            .dataTables_wrapper .dataTables_length select {
                width: auto;
                display: inline-block;
            }

            /* 分页按钮优化 */
            .dataTables_wrapper .dataTables_paginate .paginate_button {
                padding: 6px 12px;
                margin: 0 2px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <h1>激活码管理系统</h1>
        </div>
    </header>

    <!-- 主体内容 -->
    <div class="container">
        <!-- 操作按钮 -->
        <div class="row mb-3">
            <div class="col">
                <button id="generateBtn" class="btn btn-primary me-2"><i class="bi bi-plus-circle"></i> 生成激活码</button>
                <button id="exportBtn" class="btn btn-success me-2"><i class="bi bi-file-earmark-excel"></i> 导出激活码</button>
                <button id="refreshBtn" class="btn btn-secondary"><i class="bi bi-arrow-repeat"></i> 刷新列表</button>
            </div>
        </div>

        <!-- 搜索面板 -->
        <div class="card search-card">
            <div class="card-header">
                搜索条件
            </div>
            <div class="card-body">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <div class="form-group mb-2">
                            <label for="filterCode" class="form-label">激活码</label>
                            <input type="text" id="filterCode" class="form-control" placeholder="输入激活码关键字">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterStatus" class="form-label">激活状态</label>
                            <select id="filterStatus" class="form-select">
                                <option value="">全部</option>
                                <option value="active">正常</option>
                                <option value="disabled">禁用</option>
                                <option value="expired">过期</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterUsed" class="form-label">使用状态</label>
                            <select id="filterUsed" class="form-select">
                                <option value="">全部</option>
                                <option value="true">已使用</option>
                                <option value="false">未使用</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterPermanent" class="form-label">永久有效</label>
                            <select id="filterPermanent" class="form-select">
                                <option value="">全部</option>
                                <option value="true">是</option>
                                <option value="false">否</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterPlanType" class="form-label">权益版本</label>
                            <select id="filterPlanType" class="form-select">
                                <option value="">全部</option>
                                <option value="standard">标准版</option>
                                <option value="ultra">Ultra版</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterDeliveryStatus" class="form-label">发货状态</label>
                            <select id="filterDeliveryStatus" class="form-select">
                                <option value="">全部</option>
                                <option value="pending">未发货</option>
                                <option value="delivered">已发货</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row align-items-end">
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterMinCalls" class="form-label">最小调用次数</label>
                            <input type="number" id="filterMinCalls" class="form-control" min="0">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group mb-2">
                            <label for="filterMaxCalls" class="form-label">最大调用次数</label>
                            <input type="number" id="filterMaxCalls" class="form-control" min="1">
                        </div>
                    </div>
                    <div class="col-md-8 mb-2">
                        <button id="searchBtn" class="btn btn-primary me-2">搜索</button>
                        <button id="resetSearchBtn" class="btn btn-outline-secondary me-2">重置</button>
                        <button id="trialQueryBtn" class="btn btn-info me-2">试用查询</button>
                        <button id="hundredQueryBtn" class="btn btn-warning me-2">百次查询</button>
                        <button id="permanentQueryBtn" class="btn btn-success">永久查询</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header">
                激活码列表
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="codesDataTable" class="table table-striped table-bordered" style="width:100%">
                        <thead>
                            <tr>
                                <th>激活码</th>
                                <th>使用状态</th>
                                <th>激活状态</th>
                                <th>调用次数</th>
                                <th>永久有效</th>
                                <th>权益版本</th>
                                <th>发货状态</th>
                                <th>使用时间</th>
                                <th>机器ID</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 调试区域 (Alt+D 显示) -->
        <div id="debugArea">
            <h4>调试信息</h4>
            <div id="debugContent"></div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <!-- 生成激活码模态框 -->
    <div class="modal fade" id="generateModal" tabindex="-1" aria-labelledby="generateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="generateModalLabel">生成新激活码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="codeCount" class="form-label">生成数量</label>
                        <input type="number" class="form-control" id="codeCount" min="1" value="1">
                    </div>
                    <div class="mb-3">
                        <label for="callCount" class="form-label">调用次数</label>
                        <input type="number" class="form-control" id="callCount" min="1" value="3">
                    </div>
                    <div class="mb-3">
                        <label for="planType" class="form-label">权益版本</label>
                        <select class="form-select" id="planType">
                            <option value="standard">标准版</option>
                            <option value="ultra">Ultra版</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isPermanent">
                        <label class="form-check-label" for="isPermanent">永久有效</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="generateSubmitBtn">生成</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看生成的激活码模态框 -->
    <div class="modal fade" id="viewCodesModal" tabindex="-1" aria-labelledby="viewCodesModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewCodesModalLabel">生成的激活码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="generatedCodesList"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑激活码模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">编辑激活码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="editCode">
                    <div class="mb-3">
                        <p>激活码: <strong id="displayCode"></strong></p>
                    </div>
                    <div class="mb-3">
                        <label for="editCallCount" class="form-label">调用次数</label>
                        <input type="number" class="form-control" id="editCallCount" min="0">
                    </div>
                    <div class="mb-3 form-check">
                        <input class="form-check-input" type="checkbox" id="editIsPermanent">
                        <label class="form-check-label" for="editIsPermanent">永久有效</label>
                    </div>
                    <div class="mb-3">
                        <label for="editPlanType" class="form-label">权益版本</label>
                        <select class="form-select" id="editPlanType">
                            <option value="standard">标准版</option>
                            <option value="ultra">Ultra版</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editStatus" class="form-label">状态</label>
                        <select class="form-select" id="editStatus">
                            <option value="active">正常</option>
                            <option value="disabled">禁用</option>
                            <option value="expired">过期</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="node_modules/jquery/dist/jquery.min.js"></script>
    <script src="node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="node_modules/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="node_modules/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script src="admin-panel.js"></script>
    
    <script>
        // 调试区域切换（按Alt+D可显示/隐藏）
        document.addEventListener('keydown', function(e) {
            if (e.altKey && e.key === 'd') {
                const debugArea = document.getElementById('debugArea');
                debugArea.style.display = debugArea.style.display === 'none' ? 'block' : 'none';
            }
        });
        
        function debug(message) {
            console.log(message);
            const debugContent = document.getElementById('debugContent');
            if (debugContent) {
                const time = new Date().toLocaleTimeString();
                debugContent.innerHTML += `<div>${time}: ${message}</div>`;
            }
        }
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            debug(`错误: ${event.message} (${event.filename}:${event.lineno})`);
        });
    </script>
</body>
</html> 