// 数据迁移脚本 - 添加权益版本和发货状态字段
// 使用方法: node migrate-data.js

const mongoose = require('mongoose');
require('dotenv').config();

// 连接MongoDB
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/activation-db';
    
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    };

    // 如果设置了用户名和密码，添加认证信息
    if (process.env.MONGODB_USER && process.env.MONGODB_PASSWORD) {
      options.auth = {
        username: process.env.MONGODB_USER,
        password: process.env.MONGODB_PASSWORD
      };
    }

    await mongoose.connect(mongoURI, options);
    console.log('已连接到 MongoDB');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 定义激活码模型（简化版，只包含必要字段）
const ActivationCodeSchema = new mongoose.Schema({
  code: String,
  isUsed: Boolean,
  planType: {
    type: String,
    enum: ['standard', 'ultra'],
    default: 'standard'
  },
  deliveryStatus: {
    type: String,
    enum: ['pending', 'delivered'],
    default: 'pending'
  }
}, { strict: false }); // 允许其他字段存在

const ActivationCode = mongoose.model('ActivationCode', ActivationCodeSchema);

// 数据迁移函数
async function migrateData() {
  try {
    console.log('开始数据迁移...\n');
    
    // 1. 迁移权益版本字段
    console.log('1. 检查权益版本字段...');
    const codesWithoutPlanType = await ActivationCode.find({ 
      planType: { $exists: false } 
    });
    
    if (codesWithoutPlanType.length > 0) {
      console.log(`发现 ${codesWithoutPlanType.length} 条记录需要设置默认权益版本`);
      
      const result1 = await ActivationCode.updateMany(
        { planType: { $exists: false } },
        { $set: { planType: 'standard' } }
      );
      
      console.log(`✓ 权益版本字段迁移完成，更新了 ${result1.modifiedCount} 条记录`);
    } else {
      console.log('✓ 所有记录都已有权益版本字段');
    }
    
    // 2. 迁移发货状态字段
    console.log('\n2. 检查发货状态字段...');
    const codesWithoutDeliveryStatus = await ActivationCode.find({ 
      deliveryStatus: { $exists: false } 
    });
    
    if (codesWithoutDeliveryStatus.length > 0) {
      console.log(`发现 ${codesWithoutDeliveryStatus.length} 条记录需要设置发货状态`);
      
      // 已使用的设为已发货
      const result2 = await ActivationCode.updateMany(
        { deliveryStatus: { $exists: false }, isUsed: true },
        { $set: { deliveryStatus: 'delivered' } }
      );
      
      // 未使用的设为未发货
      const result3 = await ActivationCode.updateMany(
        { deliveryStatus: { $exists: false }, isUsed: false },
        { $set: { deliveryStatus: 'pending' } }
      );
      
      console.log(`✓ 发货状态字段迁移完成，已使用记录设为已发货: ${result2.modifiedCount} 条`);
      console.log(`✓ 发货状态字段迁移完成，未使用记录设为未发货: ${result3.modifiedCount} 条`);
    } else {
      console.log('✓ 所有记录都已有发货状态字段');
    }
    
    // 3. 验证迁移结果
    console.log('\n3. 验证迁移结果...');
    const totalCodes = await ActivationCode.countDocuments();
    const standardCodes = await ActivationCode.countDocuments({ planType: 'standard' });
    const ultraCodes = await ActivationCode.countDocuments({ planType: 'ultra' });
    const pendingCodes = await ActivationCode.countDocuments({ deliveryStatus: 'pending' });
    const deliveredCodes = await ActivationCode.countDocuments({ deliveryStatus: 'delivered' });
    
    console.log(`总激活码数量: ${totalCodes}`);
    console.log(`标准版: ${standardCodes} 条，Ultra版: ${ultraCodes} 条`);
    console.log(`未发货: ${pendingCodes} 条，已发货: ${deliveredCodes} 条`);
    
    console.log('\n✅ 数据迁移完成！');
    
  } catch (error) {
    console.error('❌ 数据迁移失败:', error);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    await connectDB();
    await migrateData();
    
    console.log('\n迁移脚本执行完成，可以安全关闭。');
    process.exit(0);
  } catch (error) {
    console.error('脚本执行失败:', error);
    process.exit(1);
  }
}

// 运行脚本
main();
