# 语音识别功能设计方案

## 界面设计稿

### 主要界面元素
1. **语音对话面板**：
   - 顶部：标题栏、模式切换按钮
   - 中间：对话历史区域（气泡式布局）
   - 底部：录音控制区、输入框、发送按钮

2. **语音控制元素**：
   - 麦克风按钮 - 录音开始/停止
   - 语音波形动画 - 显示录音状态
   - 实时识别文本框 - 显示和编辑识别结果
   - 发送按钮 - 提交问题

3. **状态指示器**：
   - 录音状态 - 波形动画，蓝色脉冲
   - AI思考状态 - 加载动画
   - AI回复状态 - 流式文本展示

## 交互设计

1. **进入语音模式**：
   - 按下 Ctrl+Shift+M 切换到语音问答模式
   - 界面转换到语音对话面板，保留原有窗口控制功能

2. **语音录制**：
   - 按下 Ctrl+M 开始录音，波形动画显示
   - 实时显示识别文字
   - 再次按下 Ctrl+M 停止录音
   - 可手动编辑识别文本

3. **发送问题**：
   - 按 Ctrl+Enter 发送问题（注意ctrl+回车程序原来就有此快捷键，你需要判断当前程序处于什么模式，如果在语音解答模式，ctrl+回车不再处理截图提取问题然后解答的流程，只用于发送语音识别后的问题，若不在语音解答模式，按照原逻辑处理。）
   - 问题文本显示在对话区域中
   - 立即显示"正在思考..."状态

4. **接收回复**：
   - AI回复以流式方式显示在对话区域中
   - 代码块使用语法高亮显示
   - 完成回复后自动滚动到底部

5. **多轮对话**：
   - 保持最近3轮对话作为上下文
   - 可以滚动查看历史对话

## 技术实现方案

### 1. 语音识别技术
- 主要使用浏览器原生的**Web Speech API**（SpeechRecognition接口）
- 支持中英文语音识别
- 实时返回识别结果
- 实现连续识别模式

### 2. 流式AI调用
- 利用OpenRouter API进行流式响应
- 使用Server-Sent Events(SSE)或WebSocket实现流式文本接收
- 保持对话上下文，发送历史记录
- 要考虑到有深度思考模型返回的推理信息可输出，返回在Reasoning字段中
- OpenRouter返回的choices[0].message.content的内容，都是markdown格式，注意解析
- 流式调用传递的model是程序设置的solutionModel，其他max_tokens、temperature、reasoning等参数保持和原有调用解答模型解答传递的参数一致。
- 官方流式调用文档可供参考https://openrouter.ai/docs/api-reference/streaming

### 3. 文件修改计划

#### 新增文件
1. `src/components/VoiceChat/VoiceChatPanel.tsx` - 语音对话主面板
2. `src/components/VoiceChat/AudioWaveform.tsx` - 音频波形动画组件
3. `src/components/VoiceChat/ChatMessage.tsx` - 对话气泡组件
4. `src/components/VoiceChat/ChatHistory.tsx` - 对话历史记录组件
5. `src/components/VoiceChat/VoiceInput.tsx` - 语音输入控制组件
6. `src/hooks/useSpeechRecognition.ts` - 语音识别自定义Hook
7. `src/hooks/useStreamingChat.ts` - 流式AI聊天Hook

#### 修改文件
1. `electron/shortcuts.ts` - 添加新快捷键处理
2. `electron/main.ts` - 添加语音模式状态和相关IPC处理
3. `electron/preload.ts` - 添加语音相关API到renderer
4. `src/App.tsx` - 集成语音对话面板
5. `src/_pages/SubscribedApp.tsx` - 添加语音模式切换逻辑
6. `electron/ProcessingHelper.ts` - 添加OpenRouter流式调用支持

#### 具体修改内容大纲

1. **在shortcuts.ts中添加新快捷键**:
```typescript
// 添加语音模式切换快捷键
globalShortcut.register("CommandOrControl+Shift+M", () => {
  console.log("Command/Ctrl + Shift + M pressed. Toggling voice chat mode.")
  // 通知主进程切换到语音模式
  const mainWindow = this.deps.getMainWindow()
  if (mainWindow) {
    mainWindow.webContents.send("toggle-voice-chat-mode")
  }
})

// 修改录音控制快捷键
globalShortcut.register("CommandOrControl+M", () => {
  console.log("Command/Ctrl + M pressed. Toggle voice recording.")
  const mainWindow = this.deps.getMainWindow()
  if (mainWindow) {
    mainWindow.webContents.send("toggle-voice-recording")
  }
})
```

2. **在main.ts中添加语音模式状态**:
```typescript
// 在state对象中添加
isVoiceChatMode: false,

// 添加IPC处理程序
ipcMain.handle('toggle-voice-chat-mode', () => {
  state.isVoiceChatMode = !state.isVoiceChatMode
  return state.isVoiceChatMode
})

// 添加流式调用处理
ipcMain.handle('streaming-chat', async (_, messages, model) => {
  // 实现流式调用OpenRouter API
})
```

3. **在ProcessingHelper.ts中添加流式调用支持**:
```typescript
// 添加流式调用方法
public async streamChat(messages: any[], signal: AbortSignal): Promise<ReadableStream> {
  const config = configHelper.loadConfig();
  // 实现OpenRouter流式调用
}
```

## 数据流

1. **语音识别流**:
   用户语音 → Web Speech API → 实时文本 → 可编辑文本框
   
2. **聊天流**:
   文本 + 历史记录 → OpenRouter API → 流式回复 → 实时显示

3. **状态管理**:
   - `isVoiceChatMode` - 是否在语音聊天模式
   - `isRecording` - 是否正在录音
   - `conversations` - 对话历史数组
   - `currentMessage` - 当前编辑的消息 